{"test_results": [{"test_type": "static_analysis", "contract_name": "Standard ERC20 Token", "contract_id": "legitimate_erc20", "expected_risk": "low", "actual_risk": "low", "risk_score": 0.3499999999999999, "vulnerabilities_found": 3, "analysis_time": 0.0006678104400634766, "test_passed": true}, {"test_type": "static_analysis", "contract_name": "Honeypot Contract", "contract_id": "honeypot_contract", "expected_risk": "high", "actual_risk": "low", "risk_score": 0.35, "vulnerabilities_found": 4, "analysis_time": 0.00030493736267089844, "test_passed": false}, {"test_type": "static_analysis", "contract_name": "<PERSON><PERSON>tract", "contract_id": "rug_pull_contract", "expected_risk": "critical", "actual_risk": "low", "risk_score": 0.35000000000000003, "vulnerabilities_found": 10, "analysis_time": 0.00020575523376464844, "test_passed": false}, {"test_type": "risk_scoring", "error": "'str' object has no attribute 'value'", "test_passed": false}], "performance_metrics": {"total_validation_time": 0, "average_analysis_time": 0.0003928343454996745, "average_scoring_time": 0, "max_analysis_time": 0.0006678104400634766, "meets_performance_target": true}, "accuracy_assessment": {"total_tests": 3, "passed_tests": 1, "accuracy": 0.3333333333333333, "meets_accuracy_target": false}, "production_readiness": {"criteria_met": {"accuracy_threshold": false, "performance_threshold": true, "no_critical_errors": true}, "overall_ready": false, "go_no_go_decision": "NO_GO"}}