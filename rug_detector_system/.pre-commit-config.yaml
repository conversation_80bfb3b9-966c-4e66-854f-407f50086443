# Pre-commit hooks configuration for production-grade code quality
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # Pre-commit hooks for general file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer
      - id: check-yaml
        args: ['--unsafe']
      - id: check-toml
      - id: check-json
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: mixed-line-ending
        args: ['--fix=lf']
      - id: name-tests-test
        args: ['--pytest-test-first']

  # Python code formatting with Black
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=88]

  # Python import sorting with isort
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]

  # Python linting with flake8
  - repo: https://github.com/pycqa/flake8
    rev: 7.3.0
    hooks:
      - id: flake8

  # Python type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.16.1
    hooks:
      - id: mypy
        additional_dependencies: [
          types-requests,
          types-redis,
          types-PyYAML,
        ]
        args: [--strict, --ignore-missing-imports]

  # Security linting with bandit
  - repo: https://github.com/pycqa/bandit
    rev: 1.8.6
    hooks:
      - id: bandit
        args: [-r, src/]
        exclude: ^tests/

  # YAML linting
  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.37.0
    hooks:
      - id: yamllint
        args: [-d, relaxed]

# Global configuration
default_language_version:
  python: python3.11

# CI configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
