{"test_results": [{"test_type": "static_analysis", "contract_name": "Standard ERC20 Token", "contract_id": "legitimate_erc20", "expected_risk": "low", "actual_risk": "low", "risk_score": 0.3499999999999999, "vulnerabilities_found": 3, "analysis_time": 0.0007081031799316406, "test_passed": true}, {"test_type": "static_analysis", "contract_name": "Honeypot Contract", "contract_id": "honeypot_contract", "expected_risk": "high", "actual_risk": "low", "risk_score": 0.35, "vulnerabilities_found": 4, "analysis_time": 0.0003657341003417969, "test_passed": false}, {"test_type": "static_analysis", "contract_name": "<PERSON><PERSON>tract", "contract_id": "rug_pull_contract", "expected_risk": "critical", "actual_risk": "low", "risk_score": 0.35000000000000003, "vulnerabilities_found": 10, "analysis_time": 0.00035119056701660156, "test_passed": false}, {"test_type": "risk_scoring", "contract_name": "Standard ERC20 Token", "contract_id": "legitimate_erc20", "expected_risk": "low", "actual_risk": "minimal", "risk_score": 0.125, "confidence": 0.6, "alerts_generated": 0, "scoring_time": 0.00032901763916015625, "test_passed": true}, {"test_type": "risk_scoring", "contract_name": "Honeypot Contract", "contract_id": "honeypot_contract", "expected_risk": "high", "actual_risk": "medium", "risk_score": 0.6479166666666668, "confidence": 0.6, "alerts_generated": 0, "scoring_time": 0.0003662109375, "test_passed": true}, {"test_type": "risk_scoring", "contract_name": "<PERSON><PERSON>tract", "contract_id": "rug_pull_contract", "expected_risk": "critical", "actual_risk": "high", "risk_score": 0.7555555555555556, "confidence": 0.6, "alerts_generated": 0, "scoring_time": 0.00022029876708984375, "test_passed": true}], "performance_metrics": {"total_validation_time": 0, "average_analysis_time": 0.000475009282430013, "average_scoring_time": 0.00030517578125, "max_analysis_time": 0.0007081031799316406, "meets_performance_target": true}, "accuracy_assessment": {"total_tests": 6, "passed_tests": 4, "accuracy": 0.6666666666666666, "meets_accuracy_target": false}, "production_readiness": {"criteria_met": {"accuracy_threshold": false, "performance_threshold": true, "no_critical_errors": true}, "overall_ready": false, "go_no_go_decision": "NO_GO"}}