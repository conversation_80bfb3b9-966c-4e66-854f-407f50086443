[build-system]
requires = ["setuptools>=65.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "rug-detector-system"
version = "2.0.0"
description = "Production-grade blockchain security analysis platform for detecting rug-pulls"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "MLDevOps Architect", email = "<EMAIL>"}
]
maintainers = [
    {name = "MLDevOps Architect", email = "<EMAIL>"}
]
keywords = ["blockchain", "security", "rug-pull", "detection", "web3", "defi"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Office/Business :: Financial",
    "Topic :: Security",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.11"
dependencies = [
    "web3>=6.0.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "redis>=5.0.0",
    "kafka-python>=2.0.0",
    "requests>=2.31.0",
    "httpx>=0.25.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "structlog>=23.0.0",
    "prometheus-client>=0.18.0",
    "psycopg2-binary>=2.9.0",
    "asyncio>=3.4.3",
    "websockets>=11.0.0",
    "tenacity>=8.2.0",
    "click>=8.1.0",
    "rich>=13.0.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "bandit>=1.7.5",
    "safety>=2.3.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "factory-boy>=3.3.0",
    "faker>=19.0.0",
]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/rugdetector/system"
Documentation = "https://rugdetector.readthedocs.io"
Repository = "https://github.com/rugdetector/system.git"
"Bug Tracker" = "https://github.com/rugdetector/system/issues"

[project.scripts]
rug-detector = "src.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]
exclude = ["tests*"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py311', 'py312', 'py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["web3", "sqlalchemy", "redis", "kafka", "requests", "httpx", "pydantic"]

# MyPy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "web3.*",
    "kafka.*",
    "redis.*",
    "slither.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "blockchain: marks tests that require blockchain connection",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# Bandit security linter configuration
[tool.bandit]
exclude_dirs = ["tests", "venv"]
skips = ["B101", "B601"]
