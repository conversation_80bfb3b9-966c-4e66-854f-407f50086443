"""
Production Configuration

Production-specific configuration settings for the Rug Detector System.
Optimized for security, performance, and reliability.

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import os
from typing import Dict, Any

# Production configuration
PRODUCTION_CONFIG: Dict[str, Any] = {
    # Application settings
    "app": {
        "name": "Rug Detector System",
        "version": "2.0.0",
        "environment": "production",
        "debug": False,
        "log_level": "INFO"
    },
    
    # Database configuration
    "database": {
        "url": os.getenv("DATABASE_URL", "postgresql://rug_detector_user:password@localhost:5432/rug_detector"),
        "pool_size": 20,
        "max_overflow": 30,
        "pool_timeout": 30,
        "pool_recycle": 3600,
        "echo": False
    },
    
    # Redis configuration
    "redis": {
        "url": os.getenv("REDIS_URL", "redis://localhost:6379/0"),
        "max_connections": 50,
        "socket_timeout": 30,
        "socket_connect_timeout": 10,
        "retry_on_timeout": True
    },
    
    # Blockchain configuration
    "blockchain": {
        "ethereum_rpc_url": os.getenv("ETHEREUM_RPC_URL", "https://mainnet.infura.io/v3/YOUR_PROJECT_ID"),
        "polygon_rpc_url": os.getenv("POLYGON_RPC_URL", "https://polygon-rpc.com"),
        "bsc_rpc_url": os.getenv("BSC_RPC_URL", "https://bsc-dataseed.binance.org"),
        "request_timeout": 30,
        "max_retries": 3
    },
    
    # API Keys
    "api_keys": {
        "etherscan": os.getenv("ETHERSCAN_API_KEY", ""),
        "coingecko": os.getenv("COINGECKO_API_KEY", ""),
        "dune_analytics": os.getenv("DUNE_ANALYTICS_API_KEY", ""),
        "alchemy": os.getenv("ALCHEMY_API_KEY", ""),
        "alpha_vantage": os.getenv("ALPHA_VANTAGE_API_KEY", "")
    },
    
    # Security settings
    "security": {
        "secret_key": os.getenv("SECRET_KEY", "production-secret-key-change-me"),
        "allowed_hosts": ["*"],  # Configure for production
        "cors_origins": ["https://yourdomain.com"],
        "rate_limit": {
            "requests_per_minute": 100,
            "burst_size": 200
        }
    },
    
    # Monitoring and observability
    "monitoring": {
        "prometheus_enabled": True,
        "prometheus_port": 9090,
        "grafana_enabled": True,
        "grafana_port": 3000,
        "health_check_interval": 30,
        "metrics_retention_days": 30
    },
    
    # Performance settings
    "performance": {
        "worker_processes": 4,
        "worker_connections": 1000,
        "keepalive_timeout": 65,
        "client_max_body_size": "10M",
        "gzip_enabled": True,
        "cache_ttl": 3600
    }
}

def get_production_config() -> Dict[str, Any]:
    """Get production configuration."""
    return PRODUCTION_CONFIG.copy()
