# Development Dockerfile for Rug Detector System with LangGraph Multi-Agent Support
# Optimized for M1 Max architecture with Ollama integration

FROM python:3.11-slim

# Set build arguments
ARG BUILDPLATFORM
ARG TARGETPLATFORM

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt ./

# Install Python dependencies including LangGraph
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir \
        langgraph \
        langchain-community \
        langchain-ollama \
        aiohttp \
        asyncio \
        tavily-python

# Create necessary directories
RUN mkdir -p logs data src

# Set environment variables
ENV PYTHONPATH=/app/src
ENV PYTHONUNBUFFERED=1
ENV ENVIRONMENT=development

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Start application (will be overridden by docker-compose)
CMD ["python", "-c", "print('LangGraph Multi-Agent Rug Detector Ready')"]
