["tests/test_simplified_agents.py::TestIntegrationAnalysis::test_analyze_contract_fast_mode", "tests/test_simplified_agents.py::TestSimpleAgentState::test_agent_state_initialization", "tests/test_simplified_agents.py::TestSimpleAgentState::test_agent_state_post_init", "tests/test_simplified_agents.py::TestSimplifiedCoordinatorAgent::test_calculate_final_risk_score", "tests/test_simplified_agents.py::TestSimplifiedCoordinatorAgent::test_determine_final_recommendation", "tests/test_simplified_agents.py::TestSimplifiedCoordinatorAgent::test_risk_coordination_success", "tests/test_simplified_agents.py::TestSimplifiedMarketAgent::test_market_analysis_success", "tests/test_simplified_agents.py::TestSimplifiedPatternAgent::test_pattern_agent_initialization", "tests/test_simplified_agents.py::TestSimplifiedPatternAgent::test_pattern_analysis_error", "tests/test_simplified_agents.py::TestSimplifiedPatternAgent::test_pattern_analysis_success", "tests/test_simplified_agents.py::TestSimplifiedPatternAgent::test_pattern_analysis_timeout", "tests/test_simplified_agents.py::TestSimplifiedSocialAgent::test_social_analysis_success"]