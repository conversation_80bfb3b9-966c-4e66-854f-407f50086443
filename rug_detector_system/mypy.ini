[mypy]
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True
show_error_codes = True

# Module path configuration
mypy_path = src

[mypy-web3.*]
ignore_missing_imports = True

[mypy-kafka.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-slither.*]
ignore_missing_imports = True

[mypy-tenacity.*]
ignore_missing_imports = True

[mypy-httpx.*]
ignore_missing_imports = True

[mypy-structlog.*]
ignore_missing_imports = True

[mypy-prometheus_client.*]
ignore_missing_imports = True
