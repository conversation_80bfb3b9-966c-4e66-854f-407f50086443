{"data_mtime": 1751828718, "dep_lines": [16, 17, 18, 19, 20, 22, 23, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "dataclasses", "enum", "pathlib", "typing", "structlog", "dotenv", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "dotenv.main", "functools", "logging", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "re", "structlog._base", "structlog.processors", "structlog.stdlib", "structlog.typing", "types", "typing_extensions"], "hash": "8244167abf82a73e4dcba92a4791708a1603e41f", "id": "src.config", "ignore_all": false, "interface_hash": "7518c1f97189dbc54884b160e1d3fa55e3a45ea0", "mtime": 1751828705, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "src/config.py", "plugin_data": null, "size": 18954, "suppressed": [], "version_id": "1.16.1"}