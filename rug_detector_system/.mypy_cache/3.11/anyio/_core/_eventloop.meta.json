{"data_mtime": 1751828453, "dep_lines": [6, 19, 1, 3, 4, 5, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "anyio.abc", "__future__", "math", "sys", "threading", "contextlib", "importlib", "typing", "sniffio", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "anyio.abc._eventloop"], "hash": "5db12510b1a53a24a28c64b11df1073775b98d6f", "id": "anyio._core._eventloop", "ignore_all": true, "interface_hash": "d33e6d4b658dcd5c6daf8c1c443a91806f68b910", "mtime": 1751824996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/anyio/_core/_eventloop.py", "plugin_data": null, "size": 4695, "suppressed": [], "version_id": "1.16.1"}