{"data_mtime": 1751828453, "dep_lines": [19, 7, 12, 20, 1, 3, 4, 5, 6, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._typedattr", "collections.abc", "anyio.to_thread", "anyio.abc", "__future__", "logging", "re", "ssl", "sys", "dataclasses", "functools", "typing", "anyio", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "anyio._core", "anyio.abc._resources", "anyio.abc._streams", "anyio.abc._tasks"], "hash": "39ea7b6e3e22f8136085a0f32b92231c4770a814", "id": "anyio.streams.tls", "ignore_all": true, "interface_hash": "561f9ce78bf1a26721772d0da805c4b6de78d64f", "mtime": 1751824996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/anyio/streams/tls.py", "plugin_data": null, "size": 13199, "suppressed": [], "version_id": "1.16.1"}