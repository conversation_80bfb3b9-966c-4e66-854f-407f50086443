{"data_mtime": 1751828453, "dep_lines": [33, 34, 35, 37, 47, 48, 49, 6, 36, 1, 3, 4, 5, 7, 8, 9, 10, 11, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 5, 25, 5, 10, 10, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._synchronization", "anyio._core._tasks", "anyio._core._testing", "anyio.abc._sockets", "anyio.abc._subprocesses", "anyio.abc._tasks", "anyio.abc._testing", "collections.abc", "anyio.from_thread", "__future__", "math", "sys", "abc", "contextlib", "os", "signal", "socket", "typing", "_typeshed", "builtins", "_frozen_importlib", "_socket", "anyio._core", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._streams", "enum", "types"], "hash": "b452595d599ddc41d83e9a05e957d706b5410038", "id": "anyio.abc._eventloop", "ignore_all": true, "interface_hash": "704bce7771f7797b721402a4d23eba98ff0eebb1", "mtime": 1751824996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/anyio/abc/_eventloop.py", "plugin_data": null, "size": 9682, "suppressed": [], "version_id": "1.16.1"}