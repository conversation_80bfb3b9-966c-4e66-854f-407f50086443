{"data_mtime": 1751828453, "dep_lines": [1, 2, 3, 4, 5, 8, 20, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["httpcore._sync.connection", "httpcore._sync.connection_pool", "httpcore._sync.http11", "httpcore._sync.http_proxy", "httpcore._sync.interfaces", "httpcore._sync.http2", "httpcore._sync.socks_proxy", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "c43e980b1d24e1b2425e8426aea879942b8e43c5", "id": "httpcore._sync", "ignore_all": true, "interface_hash": "e55077617dc4e56ba94cfedc78c7e84f135fc764", "mtime": 1751824996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/httpcore/_sync/__init__.py", "plugin_data": null, "size": 1141, "suppressed": [], "version_id": "1.16.1"}