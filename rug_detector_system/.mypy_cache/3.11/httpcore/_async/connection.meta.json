{"data_mtime": 1751828453, "dep_lines": [9, 10, 16, 17, 86, 11, 12, 13, 14, 15, 1, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.auto", "httpcore._backends.base", "httpcore._async.http11", "httpcore._async.interfaces", "httpcore._async.http2", "httpcore._exceptions", "httpcore._models", "httpcore._ssl", "httpcore._synchronization", "httpcore._trace", "__future__", "itertools", "logging", "ssl", "types", "typing", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "httpcore._backends"], "hash": "448a066ee2ddc686dafdb1cc14b4dfc212306ccf", "id": "httpcore._async.connection", "ignore_all": true, "interface_hash": "b41c49254626581080f72c5e8d3daaa4e6ed6bda", "mtime": 1751824996, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/httpcore/_async/connection.py", "plugin_data": null, "size": 8449, "suppressed": [], "version_id": "1.16.1"}