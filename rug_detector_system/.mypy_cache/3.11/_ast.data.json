{".class": "MypyFile", "_fullname": "_ast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AST": {".class": "SymbolTableNode", "cross_ref": "ast.AST", "kind": "Gdef"}, "Add": {".class": "SymbolTableNode", "cross_ref": "ast.Add", "kind": "Gdef"}, "And": {".class": "SymbolTableNode", "cross_ref": "ast.And", "kind": "Gdef"}, "AnnAssign": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "Assert": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Assign": {".class": "SymbolTableNode", "cross_ref": "ast.Assign", "kind": "Gdef"}, "AsyncFor": {".class": "SymbolTableNode", "cross_ref": "ast.AsyncFor", "kind": "Gdef"}, "AsyncFunctionDef": {".class": "SymbolTableNode", "cross_ref": "ast.AsyncFunctionDef", "kind": "Gdef"}, "AsyncWith": {".class": "SymbolTableNode", "cross_ref": "ast.AsyncWith", "kind": "Gdef"}, "Attribute": {".class": "SymbolTableNode", "cross_ref": "ast.Attribute", "kind": "Gdef"}, "AugAssign": {".class": "SymbolTableNode", "cross_ref": "ast.Aug<PERSON><PERSON>", "kind": "Gdef"}, "Await": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>t", "kind": "Gdef"}, "BinOp": {".class": "SymbolTableNode", "cross_ref": "ast.BinOp", "kind": "Gdef"}, "BitAnd": {".class": "SymbolTableNode", "cross_ref": "ast.BitAnd", "kind": "Gdef"}, "BitOr": {".class": "SymbolTableNode", "cross_ref": "ast.BitOr", "kind": "Gdef"}, "BitXor": {".class": "SymbolTableNode", "cross_ref": "ast.BitXor", "kind": "Gdef"}, "BoolOp": {".class": "SymbolTableNode", "cross_ref": "ast.Bool<PERSON>p", "kind": "Gdef"}, "Break": {".class": "SymbolTableNode", "cross_ref": "ast.Break", "kind": "Gdef"}, "Call": {".class": "SymbolTableNode", "cross_ref": "ast.Call", "kind": "Gdef"}, "ClassDef": {".class": "SymbolTableNode", "cross_ref": "ast.ClassDef", "kind": "Gdef"}, "Compare": {".class": "SymbolTableNode", "cross_ref": "ast.Comp<PERSON>", "kind": "Gdef"}, "Constant": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "Continue": {".class": "SymbolTableNode", "cross_ref": "ast.Continue", "kind": "Gdef"}, "Del": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON>", "kind": "Gdef"}, "Delete": {".class": "SymbolTableNode", "cross_ref": "ast.Delete", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "ast.Dict", "kind": "Gdef"}, "DictComp": {".class": "SymbolTableNode", "cross_ref": "ast.DictComp", "kind": "Gdef"}, "Div": {".class": "SymbolTableNode", "cross_ref": "ast.Div", "kind": "Gdef"}, "Eq": {".class": "SymbolTableNode", "cross_ref": "ast.Eq", "kind": "Gdef"}, "ExceptHandler": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Expr": {".class": "SymbolTableNode", "cross_ref": "ast.Expr", "kind": "Gdef"}, "Expression": {".class": "SymbolTableNode", "cross_ref": "ast.Expression", "kind": "Gdef"}, "FloorDiv": {".class": "SymbolTableNode", "cross_ref": "ast.FloorDiv", "kind": "Gdef"}, "For": {".class": "SymbolTableNode", "cross_ref": "ast.For", "kind": "Gdef"}, "FormattedValue": {".class": "SymbolTableNode", "cross_ref": "ast.FormattedValue", "kind": "Gdef"}, "FunctionDef": {".class": "SymbolTableNode", "cross_ref": "ast.FunctionDef", "kind": "Gdef"}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "ast.FunctionType", "kind": "Gdef"}, "GeneratorExp": {".class": "SymbolTableNode", "cross_ref": "ast.GeneratorExp", "kind": "Gdef"}, "Global": {".class": "SymbolTableNode", "cross_ref": "ast.Global", "kind": "Gdef"}, "Gt": {".class": "SymbolTableNode", "cross_ref": "ast.Gt", "kind": "Gdef"}, "GtE": {".class": "SymbolTableNode", "cross_ref": "ast.GtE", "kind": "Gdef"}, "If": {".class": "SymbolTableNode", "cross_ref": "ast.If", "kind": "Gdef"}, "IfExp": {".class": "SymbolTableNode", "cross_ref": "ast.IfExp", "kind": "Gdef"}, "Import": {".class": "SymbolTableNode", "cross_ref": "ast.I<PERSON>rt", "kind": "Gdef"}, "ImportFrom": {".class": "SymbolTableNode", "cross_ref": "ast.ImportFrom", "kind": "Gdef"}, "In": {".class": "SymbolTableNode", "cross_ref": "ast.In", "kind": "Gdef"}, "Interactive": {".class": "SymbolTableNode", "cross_ref": "ast.Interactive", "kind": "Gdef"}, "Invert": {".class": "SymbolTableNode", "cross_ref": "ast.Invert", "kind": "Gdef"}, "Is": {".class": "SymbolTableNode", "cross_ref": "ast.Is", "kind": "Gdef"}, "IsNot": {".class": "SymbolTableNode", "cross_ref": "ast.IsNot", "kind": "Gdef"}, "JoinedStr": {".class": "SymbolTableNode", "cross_ref": "ast.JoinedStr", "kind": "Gdef"}, "LShift": {".class": "SymbolTableNode", "cross_ref": "ast.LShift", "kind": "Gdef"}, "Lambda": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "ast.List", "kind": "Gdef"}, "ListComp": {".class": "SymbolTableNode", "cross_ref": "ast.ListComp", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Load": {".class": "SymbolTableNode", "cross_ref": "ast.Load", "kind": "Gdef"}, "Lt": {".class": "SymbolTableNode", "cross_ref": "ast.Lt", "kind": "Gdef"}, "LtE": {".class": "SymbolTableNode", "cross_ref": "ast.LtE", "kind": "Gdef"}, "MatMult": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON>", "kind": "Gdef"}, "MatchAs": {".class": "SymbolTableNode", "cross_ref": "ast.MatchAs", "kind": "Gdef"}, "MatchClass": {".class": "SymbolTableNode", "cross_ref": "ast.MatchClass", "kind": "Gdef"}, "MatchMapping": {".class": "SymbolTableNode", "cross_ref": "ast.MatchMapping", "kind": "Gdef"}, "MatchOr": {".class": "SymbolTableNode", "cross_ref": "ast.MatchOr", "kind": "Gdef"}, "MatchSequence": {".class": "SymbolTableNode", "cross_ref": "ast.MatchSequence", "kind": "Gdef"}, "MatchSingleton": {".class": "SymbolTableNode", "cross_ref": "ast.Match<PERSON><PERSON>leton", "kind": "Gdef"}, "MatchStar": {".class": "SymbolTableNode", "cross_ref": "ast.MatchStar", "kind": "Gdef"}, "MatchValue": {".class": "SymbolTableNode", "cross_ref": "ast.MatchValue", "kind": "Gdef"}, "Mod": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON>d", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Mult": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "Name": {".class": "SymbolTableNode", "cross_ref": "ast.Name", "kind": "Gdef"}, "NamedExpr": {".class": "SymbolTableNode", "cross_ref": "ast.NamedExpr", "kind": "Gdef"}, "Nonlocal": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Not": {".class": "SymbolTableNode", "cross_ref": "ast.Not", "kind": "Gdef"}, "NotEq": {".class": "SymbolTableNode", "cross_ref": "ast.NotEq", "kind": "Gdef"}, "NotIn": {".class": "SymbolTableNode", "cross_ref": "ast.NotIn", "kind": "Gdef"}, "Or": {".class": "SymbolTableNode", "cross_ref": "ast.Or", "kind": "Gdef"}, "Pass": {".class": "SymbolTableNode", "cross_ref": "ast.Pass", "kind": "Gdef"}, "Pow": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "PyCF_ALLOW_TOP_LEVEL_AWAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.PyCF_ALLOW_TOP_LEVEL_AWAIT", "name": "PyCF_ALLOW_TOP_LEVEL_AWAIT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}}}, "PyCF_ONLY_AST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.PyCF_ONLY_AST", "name": "PyCF_ONLY_AST", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}}}, "PyCF_TYPE_COMMENTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.PyCF_TYPE_COMMENTS", "name": "PyCF_TYPE_COMMENTS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4096}}}, "RShift": {".class": "SymbolTableNode", "cross_ref": "ast.RShift", "kind": "Gdef"}, "Raise": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "Return": {".class": "SymbolTableNode", "cross_ref": "ast.Return", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "ast.Set", "kind": "Gdef"}, "SetComp": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Slice": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "Starred": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON>ed", "kind": "Gdef"}, "Store": {".class": "SymbolTableNode", "cross_ref": "ast.Store", "kind": "Gdef"}, "Sub": {".class": "SymbolTableNode", "cross_ref": "ast.Sub", "kind": "Gdef"}, "Subscript": {".class": "SymbolTableNode", "cross_ref": "ast.Subscript", "kind": "Gdef"}, "Try": {".class": "SymbolTableNode", "cross_ref": "ast.Try", "kind": "Gdef"}, "TryStar": {".class": "SymbolTableNode", "cross_ref": "ast.TryStar", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "TypeIgnore": {".class": "SymbolTableNode", "cross_ref": "ast.TypeIgnore", "kind": "Gdef"}, "UAdd": {".class": "SymbolTableNode", "cross_ref": "ast.U<PERSON>dd", "kind": "Gdef"}, "USub": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "UnaryOp": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "While": {".class": "SymbolTableNode", "cross_ref": "ast.While", "kind": "Gdef"}, "With": {".class": "SymbolTableNode", "cross_ref": "ast.With", "kind": "Gdef"}, "Yield": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON>", "kind": "Gdef"}, "YieldFrom": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ast.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "alias": {".class": "SymbolTableNode", "cross_ref": "ast.alias", "kind": "Gdef"}, "arg": {".class": "SymbolTableNode", "cross_ref": "ast.arg", "kind": "Gdef"}, "arguments": {".class": "SymbolTableNode", "cross_ref": "ast.arguments", "kind": "Gdef"}, "boolop": {".class": "SymbolTableNode", "cross_ref": "ast.boolop", "kind": "Gdef"}, "cmpop": {".class": "SymbolTableNode", "cross_ref": "ast.cmpop", "kind": "Gdef"}, "comprehension": {".class": "SymbolTableNode", "cross_ref": "ast.comprehension", "kind": "Gdef"}, "excepthandler": {".class": "SymbolTableNode", "cross_ref": "ast.except<PERSON>ler", "kind": "Gdef"}, "expr": {".class": "SymbolTableNode", "cross_ref": "ast.expr", "kind": "Gdef"}, "expr_context": {".class": "SymbolTableNode", "cross_ref": "ast.expr_context", "kind": "Gdef"}, "keyword": {".class": "SymbolTableNode", "cross_ref": "ast.keyword", "kind": "Gdef"}, "match_case": {".class": "SymbolTableNode", "cross_ref": "ast.match_case", "kind": "Gdef"}, "mod": {".class": "SymbolTableNode", "cross_ref": "ast.mod", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "ast.operator", "kind": "Gdef"}, "pattern": {".class": "SymbolTableNode", "cross_ref": "ast.pattern", "kind": "Gdef"}, "stmt": {".class": "SymbolTableNode", "cross_ref": "ast.stmt", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_ignore": {".class": "SymbolTableNode", "cross_ref": "ast.type_ignore", "kind": "Gdef"}, "unaryop": {".class": "SymbolTableNode", "cross_ref": "ast.unaryop", "kind": "Gdef"}, "withitem": {".class": "SymbolTableNode", "cross_ref": "ast.withitem", "kind": "Gdef"}}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/_ast.pyi"}