{"data_mtime": 1751828793, "dep_lines": [18, 19, 29, 30, 10, 12, 13, 14, 16, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 30, 30], "dependencies": ["structlog._base", "structlog._log_levels", "structlog.contextvars", "structlog.typing", "__future__", "asyncio", "<PERSON><PERSON><PERSON>", "sys", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "7ade1cdde946c6714849fd89e0b2364db776382f", "id": "structlog._native", "ignore_all": true, "interface_hash": "394a87473887edbcf9b85ad2abfc56143d98d0ab", "mtime": 1751826986, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/structlog/_native.py", "plugin_data": null, "size": 8154, "suppressed": [], "version_id": "1.16.1"}