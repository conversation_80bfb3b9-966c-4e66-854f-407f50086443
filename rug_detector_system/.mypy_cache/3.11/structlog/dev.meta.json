{"data_mtime": 1751828793, "dep_lines": [33, 34, 35, 51, 52, 12, 14, 15, 16, 18, 19, 20, 21, 49, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 39, 44], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["structlog._frames", "structlog.processors", "structlog.typing", "rich.console", "rich.traceback", "__future__", "shutil", "sys", "warnings", "dataclasses", "io", "types", "typing", "rich", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "datetime", "os", "rich.jupyter", "rich.style", "rich.text", "rich.theme", "typing_extensions"], "hash": "61b22d1dbe6fc7a5a7c730df8e461b0320dad45f", "id": "structlog.dev", "ignore_all": true, "interface_hash": "cc32832d9635b27ddc5c4d239360e658fe6ec856", "mtime": 1751826986, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/structlog/dev.py", "plugin_data": null, "size": 24080, "suppressed": ["colorama", "better_exceptions"], "version_id": "1.16.1"}