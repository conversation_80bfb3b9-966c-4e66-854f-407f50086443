{"data_mtime": 1751828792, "dep_lines": [15, 17, 19, 20, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["__future__", "sys", "types", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "0106953e2a3e6d3dc7ebbad42d9d234e26362726", "id": "structlog.typing", "ignore_all": true, "interface_hash": "eb32313fa40547dd42c1a0f3076787111426a7b4", "mtime": 1751826986, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/structlog/typing.py", "plugin_data": null, "size": 8375, "suppressed": [], "version_id": "1.16.1"}