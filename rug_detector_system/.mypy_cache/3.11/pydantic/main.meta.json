{"data_mtime": 1751828452, "dep_lines": [33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 33, 52, 62, 1314, 1374, 33, 46, 47, 48, 49, 50, 51, 53, 63, 1184, 3, 5, 6, 7, 8, 9, 10, 28, 29, 56, 57, 1195, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 20, 20, 20, 5, 5, 5, 5, 5, 5, 5, 25, 20, 5, 10, 10, 10, 5, 10, 5, 5, 5, 25, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._import_utils", "pydantic._internal._mock_val_ser", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.plugin._schema_validator", "pydantic.deprecated.parse", "pydantic.deprecated.copy_internals", "pydantic.deprecated.json", "pydantic._internal", "pydantic._migration", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.json_schema", "pydantic.warnings", "pydantic.fields", "pydantic.deprecated", "__future__", "operator", "sys", "types", "typing", "warnings", "copy", "pydantic_core", "typing_extensions", "inspect", "pathlib", "json", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "os", "pydantic._internal._generate_schema", "pydantic.plugin", "pydantic_core._pydantic_core"], "hash": "0c052ec4a1f3099c48416a5a145000148f8197cf", "id": "pydantic.main", "ignore_all": true, "interface_hash": "e4ff701866c580bee68e701aca058a22bec3acd6", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/main.py", "plugin_data": null, "size": 71978, "suppressed": [], "version_id": "1.16.1"}