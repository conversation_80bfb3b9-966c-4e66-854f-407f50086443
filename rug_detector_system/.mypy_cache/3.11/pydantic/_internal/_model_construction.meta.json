{"data_mtime": 1751828452, "dep_lines": [21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 40, 41, 20, 22, 44, 47, 163, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 20, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic._internal._import_utils", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._signature", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic._internal._validate_call", "pydantic.errors", "pydantic.warnings", "pydantic.fields", "pydantic.main", "pydantic.root_model", "__future__", "builtins", "operator", "sys", "typing", "warnings", "weakref", "abc", "functools", "types", "typing_extensions", "pydantic_core", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "annotated_types", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.types", "re"], "hash": "8ad59982cc867a57b4ffc7ee2fab2ce4109930e4", "id": "pydantic._internal._model_construction", "ignore_all": true, "interface_hash": "8bd7f811a45763b357887b263d3d56f8e8f570cd", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_model_construction.py", "plugin_data": null, "size": 33137, "suppressed": [], "version_id": "1.16.1"}