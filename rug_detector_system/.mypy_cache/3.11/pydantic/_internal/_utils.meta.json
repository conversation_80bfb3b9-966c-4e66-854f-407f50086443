{"data_mtime": 1751828452, "dep_lines": [20, 20, 21, 20, 26, 6, 8, 9, 10, 11, 12, 13, 14, 15, 18, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 25, 5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._import_utils", "pydantic._internal", "pydantic.main", "__future__", "dataclasses", "keyword", "typing", "weakref", "collections", "copy", "itertools", "types", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "65d41ef65dade9d6b38ea5a166aa5d0dcc8840ba", "id": "pydantic._internal._utils", "ignore_all": true, "interface_hash": "da33bafc83933d813bde6014d7db11bcf8a58f7a", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_utils.py", "plugin_data": null, "size": 12723, "suppressed": [], "version_id": "1.16.1"}