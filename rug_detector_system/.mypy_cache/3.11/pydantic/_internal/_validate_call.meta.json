{"data_mtime": 1751828452, "dep_lines": [10, 11, 11, 12, 9, 11, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 20, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._generate_schema", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic.config", "pydantic._internal", "__future__", "inspect", "functools", "typing", "pydantic_core", "builtins", "_frozen_importlib", "abc", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "types"], "hash": "ff1ae798c45500fb06af7a75106f04def3bea88d", "id": "pydantic._internal._validate_call", "ignore_all": true, "interface_hash": "51b57f8a75d58821fa80ee1fae54a64eae004a67", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_validate_call.py", "plugin_data": null, "size": 3783, "suppressed": [], "version_id": "1.16.1"}