{"data_mtime": 1751828452, "dep_lines": [15, 16, 17, 18, 24, 1, 3, 4, 5, 6, 7, 8, 11, 13, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 5, 10, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._forward_ref", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.main", "__future__", "sys", "types", "typing", "collections", "contextlib", "<PERSON><PERSON><PERSON>", "weakref", "typing_extensions", "builtins", "_collections_abc", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal._model_construction"], "hash": "bbf6f3cf19e8ec2df4130ce6db3cd7d2441870a2", "id": "pydantic._internal._generics", "ignore_all": true, "interface_hash": "aec6fc97c264d61ef9037cd7ad50a38da29bb4ab", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_generics.py", "plugin_data": null, "size": 22226, "suppressed": [], "version_id": "1.16.1"}