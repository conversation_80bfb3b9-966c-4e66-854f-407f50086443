{"data_mtime": 1751828452, "dep_lines": [11, 12, 9, 15, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 25, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic._internal._import_utils", "pydantic_core.core_schema", "pydantic.annotated_handlers", "__future__", "collections", "copy", "functools", "typing", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "c0022e832936927b671a40208a890b8744556f58", "id": "pydantic._internal._known_annotated_metadata", "ignore_all": true, "interface_hash": "e531d51f2b5be7e6f056ff8949d4739cddb6b2b4", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_known_annotated_metadata.py", "plugin_data": null, "size": 16389, "suppressed": [], "version_id": "1.16.1"}