{"data_mtime": 1751828452, "dep_lines": [8, 5, 7, 8, 15, 1, 3, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic.types", "__future__", "typing", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "2da453a9a2e8a065c8d37c8485f4a19ffacd9120", "id": "pydantic._internal._discriminated_union", "ignore_all": true, "interface_hash": "1eea531a6b2b7d20381e94d9b78a6db95bc0da29", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_discriminated_union.py", "plugin_data": null, "size": 26435, "suppressed": [], "version_id": "1.16.1"}