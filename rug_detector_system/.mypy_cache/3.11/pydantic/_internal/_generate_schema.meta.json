{"data_mtime": 1751828452, "dep_lines": [65, 65, 65, 65, 65, 66, 67, 93, 94, 95, 96, 97, 98, 99, 101, 107, 515, 1700, 2039, 5, 45, 57, 58, 59, 60, 61, 62, 63, 64, 65, 104, 105, 106, 1794, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 24, 41, 44, 45, 1653, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 20, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 25, 25, 25, 20, 5, 20, 10, 10, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._decorators", "pydantic._internal._discriminated_union", "pydantic._internal._known_annotated_metadata", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._docs_extraction", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._import_utils", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic._internal._validators", "pydantic._internal._serializers", "pydantic._internal._std_types_schema", "collections.abc", "pydantic_core.core_schema", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.functional_validators", "pydantic.json_schema", "pydantic.version", "pydantic.warnings", "pydantic._internal", "pydantic.fields", "pydantic.main", "pydantic.types", "pydantic.dataclasses", "__future__", "collections", "dataclasses", "datetime", "inspect", "os", "pathlib", "re", "sys", "typing", "warnings", "contextlib", "copy", "decimal", "enum", "functools", "ipaddress", "itertools", "operator", "types", "uuid", "typing_extensions", "pydantic_core", "zoneinfo", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic_core._pydantic_core"], "hash": "0d7956efafecd4bd4c92fcfa37f38f75c502edaf", "id": "pydantic._internal._generate_schema", "ignore_all": true, "interface_hash": "2fab236d57e2f489b22eb12c379fac6a3dbb86bf", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_generate_schema.py", "plugin_data": null, "size": 115069, "suppressed": [], "version_id": "1.16.1"}