{".class": "MypyFile", "_fullname": "pydantic._internal._core_metadata", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CoreMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._core_metadata.CoreMetadata", "name": "CoreMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_metadata.CoreMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._core_metadata", "mro": ["pydantic._internal._core_metadata.CoreMetadata", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["pydantic_js_functions", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["pydantic_js_annotation_functions", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["pydantic_js_prefer_positional_arguments", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["pydantic_js_input_core_schema", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "CoreMetadataHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler", "name": "CoreMetadataHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._core_metadata", "mro": ["pydantic._internal._core_metadata.CoreMetadataHandler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._core_metadata.CoreMetadataHandler", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CoreMetadataHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler._schema", "name": "_schema", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler.metadata", "name": "metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._core_metadata.CoreMetadataHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "metadata of CoreMetadataHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_metadata.CoreMetadata"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler.metadata", "name": "metadata", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._core_metadata.CoreMetadataHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "metadata of CoreMetadataHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_metadata.CoreMetadata"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._core_metadata.CoreMetadataHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._core_metadata.CoreMetadataHandler", "values": [], "variance": 0}, "slots": ["_schema"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "CoreSchemaOrField": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.CoreSchemaOrField", "kind": "Gdef"}, "GetJsonSchemaFunction": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_metadata.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_metadata.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_metadata.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_metadata.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_metadata.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_metadata.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "build_metadata_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["js_functions", "js_annotation_functions", "js_prefer_positional_arguments", "js_input_core_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_metadata.build_metadata_dict", "name": "build_metadata_dict", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["js_functions", "js_annotation_functions", "js_prefer_positional_arguments", "js_input_core_schema"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_metadata_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/_internal/_core_metadata.py"}