{"data_mtime": 1751828452, "dep_lines": [8, 8, 8, 13, 3, 5, 6, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 25, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._validate_call", "pydantic._internal", "pydantic.config", "__future__", "functools", "typing", "builtins", "_frozen_importlib", "abc", "pydantic._internal._generate_schema", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields"], "hash": "2dc10ba20def692694e10d830e319e60f08f92d0", "id": "pydantic.validate_call_decorator", "ignore_all": true, "interface_hash": "ccd125300e918478a2ad69f0d4035dd189460a40", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/validate_call_decorator.py", "plugin_data": null, "size": 2127, "suppressed": [], "version_id": "1.16.1"}