{"data_mtime": 1751828452, "dep_lines": [13, 13, 13, 14, 13, 15, 16, 17, 18, 3, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._typing_extra", "pydantic._internal._dataclasses", "pydantic._internal", "pydantic._migration", "pydantic.config", "pydantic.errors", "pydantic.fields", "__future__", "dataclasses", "sys", "types", "typing", "warnings", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "enum", "pydantic._internal._generate_schema", "pydantic._internal._repr", "pydantic.aliases", "pydantic.types", "re"], "hash": "9bc15b43ee61414a94cfe494a590c14920a1af1e", "id": "pydantic.dataclasses", "ignore_all": true, "interface_hash": "b4f9802862fe43e1cfff2d0a3d9454907249ac85", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/dataclasses.py", "plugin_data": null, "size": 14678, "suppressed": [], "version_id": "1.16.1"}