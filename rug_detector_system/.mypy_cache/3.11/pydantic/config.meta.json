{"data_mtime": 1751828452, "dep_lines": [14, 9, 10, 11, 15, 3, 5, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._generate_schema", "pydantic._migration", "pydantic.aliases", "pydantic.errors", "pydantic.fields", "__future__", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._repr"], "hash": "8c27346ce0257f0601f63c76fabdeca40ddb6607", "id": "pydantic.config", "ignore_all": true, "interface_hash": "d4a4d027adb34bdbfe42f3810901f9354d97fdd5", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/config.py", "plugin_data": null, "size": 35602, "suppressed": [], "version_id": "1.16.1"}