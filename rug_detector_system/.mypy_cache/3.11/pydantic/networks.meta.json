{"data_mtime": 1751828452, "dep_lines": [14, 14, 14, 7, 11, 14, 15, 16, 17, 3, 5, 6, 8, 9, 11, 12, 1, 1, 1, 1, 1, 1, 20], "dep_prios": [10, 10, 10, 5, 10, 20, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 25], "dependencies": ["pydantic._internal._fields", "pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "importlib.metadata", "pydantic_core.core_schema", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.json_schema", "__future__", "dataclasses", "re", "ipaddress", "typing", "pydantic_core", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic_core._pydantic_core", "types"], "hash": "8ed0baa110493e163f2cc17d77e7a001671d8380", "id": "pydantic.networks", "ignore_all": true, "interface_hash": "9e669d27d92c9940f45cda938bd55499aae0f3cc", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/networks.py", "plugin_data": null, "size": 23497, "suppressed": ["email_validator"], "version_id": "1.16.1"}