{"data_mtime": 1751828452, "dep_lines": [12, 12, 12, 13, 14, 3, 5, 6, 7, 8, 10, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._decorators_v1", "pydantic._internal", "pydantic.errors", "pydantic.warnings", "__future__", "functools", "types", "typing", "warnings", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "b1bcab762d7777edc1016fe0197a4ebf09fc3f66", "id": "pydantic.deprecated.class_validators", "ignore_all": true, "interface_hash": "94939886d5989822d1e14259ad40ce4521217dd3", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/pydantic/deprecated/class_validators.py", "plugin_data": null, "size": 10245, "suppressed": [], "version_id": "1.16.1"}