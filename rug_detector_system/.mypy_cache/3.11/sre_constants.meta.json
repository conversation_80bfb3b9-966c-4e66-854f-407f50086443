{"data_mtime": 1751825481, "dep_lines": [1, 2, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["sys", "re", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "679b2e9f227a80c6ad0602e35a5d920f213eece2", "id": "sre_constants", "ignore_all": true, "interface_hash": "d0aefdc52e438705b64dfa14bba61bd5b9730f78", "mtime": 1751824995, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/sre_constants.pyi", "plugin_data": null, "size": 4493, "suppressed": [], "version_id": "1.16.1"}