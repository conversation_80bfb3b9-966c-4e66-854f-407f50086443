{"data_mtime": 1751828452, "dep_lines": [3, 4, 5, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 5, 5, 30, 30, 30], "dependencies": ["rich.segment", "rich.style", "rich._loop", "rich.console", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "9b1f3ebaeb1d0fbe43a4a1a87ec5fc19097707a9", "id": "rich.screen", "ignore_all": true, "interface_hash": "d9e4605dd72018b1e347f6ce210b9bfbaad0e3f8", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/rich/screen.py", "plugin_data": null, "size": 1579, "suppressed": [], "version_id": "1.16.1"}