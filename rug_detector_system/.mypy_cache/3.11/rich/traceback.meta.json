{"data_mtime": 1751828452, "dep_lines": [28, 29, 30, 31, 39, 40, 41, 42, 43, 44, 45, 46, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 23, 26], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["rich.pretty", "rich._loop", "rich.columns", "rich.console", "rich.constrain", "rich.highlighter", "rich.panel", "rich.scope", "rich.style", "rich.syntax", "rich.text", "rich.theme", "inspect", "linecache", "os", "sys", "dataclasses", "itertools", "traceback", "types", "typing", "rich", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "rich.jupyter", "rich.segment", "typing_extensions"], "hash": "71aa970a516c8116e2db0f58388b1c5fda11b57c", "id": "rich.traceback", "ignore_all": true, "interface_hash": "45f3849f589025ace7e991b00619325d642786f9", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/rich/traceback.py", "plugin_data": null, "size": 35098, "suppressed": ["pygments.lexers", "pygments.token", "pygments.util"], "version_id": "1.16.1"}