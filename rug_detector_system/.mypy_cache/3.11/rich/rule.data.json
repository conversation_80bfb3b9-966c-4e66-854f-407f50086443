{".class": "MypyFile", "_fullname": "rich.rule", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlignMethod": {".class": "SymbolTableNode", "cross_ref": "rich.align.AlignMethod", "kind": "Gdef"}, "Console": {".class": "SymbolTableNode", "cross_ref": "rich.console.Console", "kind": "Gdef"}, "ConsoleOptions": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleOptions", "kind": "Gdef"}, "JupyterMixin": {".class": "SymbolTableNode", "cross_ref": "rich.jupyter.Ju<PERSON>", "kind": "Gdef"}, "Measurement": {".class": "SymbolTableNode", "cross_ref": "rich.measure.Measurement", "kind": "Gdef"}, "RenderResult": {".class": "SymbolTableNode", "cross_ref": "rich.console.RenderResult", "kind": "Gdef"}, "Rule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["rich.jupyter.Ju<PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.rule.Rule", "name": "Rule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.rule.Rule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.rule", "mro": ["rich.rule.Rule", "rich.jupyter.Ju<PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "title", "characters", "style", "end", "align"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "rich.rule.Rule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "title", "characters", "style", "end", "align"], "arg_types": ["rich.rule.Rule", {".class": "UnionType", "items": ["builtins.str", "rich.text.Text"], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich.align.AlignMethod"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Rule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "rich.rule.Rule.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.rule.Rule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of Rule", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_trivial_self"], "fullname": "rich.rule.Rule.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.rule.Rule", "rich.console.Console", "rich.console.ConsoleOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rich_console__ of Rule", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_measure__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "rich.rule.Rule.__rich_measure__", "name": "__rich_measure__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.rule.Rule", "rich.console.Console", "rich.console.ConsoleOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rich_measure__ of Rule", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.measure.Measurement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rule_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "chars_len", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "rich.rule.Rule._rule_line", "name": "_rule_line", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "chars_len", "width"], "arg_types": ["rich.rule.Rule", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_rule_line of Rule", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "align": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.rule.Rule.align", "name": "align", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.align.AlignMethod"}}}, "characters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.rule.Rule.characters", "name": "characters", "setter_type": null, "type": "builtins.str"}}, "end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.rule.Rule.end", "name": "end", "setter_type": null, "type": "builtins.str"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.rule.Rule.style", "name": "style", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}}}, "title": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.rule.Rule.title", "name": "title", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "rich.text.Text"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.rule.Rule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.rule.Rule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Style": {".class": "SymbolTableNode", "cross_ref": "rich.style.Style", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "rich.text.Text", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.rule.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.rule.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.rule.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.rule.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.rule.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.rule.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cell_len": {".class": "SymbolTableNode", "cross_ref": "rich.cells.cell_len", "kind": "Gdef"}, "console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.rule.console", "name": "console", "setter_type": null, "type": "rich.console.Console"}}, "set_cell_size": {".class": "SymbolTableNode", "cross_ref": "rich.cells.set_cell_size", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.rule.text", "name": "text", "setter_type": null, "type": "builtins.str"}}}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/rich/rule.py"}