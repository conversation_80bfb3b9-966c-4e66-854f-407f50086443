{".class": "MypyFile", "_fullname": "__main__", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARCHITECTURE_PATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.ARCHITECTURE_PATTERN", "name": "ARCHITECTURE_PATTERN", "setter_type": null, "type": "builtins.str"}}, "QUALITY_STANDARD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.QUALITY_STANDARD", "name": "QUALITY_STANDARD", "setter_type": null, "type": "builtins.str"}}, "SUPPORTED_NETWORKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "__main__.SUPPORTED_NETWORKS", "name": "SUPPORTED_NETWORKS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "TESTING_COVERAGE_TARGET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.TESTING_COVERAGE_TARGET", "name": "TESTING_COVERAGE_TARGET", "setter_type": null, "type": "builtins.float"}}, "VERSION_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "__main__.VERSION_INFO", "name": "VERSION_INFO", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "__main__.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__author__", "name": "__author__", "setter_type": null, "type": "builtins.str"}}, "__copyright__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__copyright__", "name": "__copyright__", "setter_type": null, "type": "builtins.str"}}, "__description__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__description__", "name": "__description__", "setter_type": null, "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "__main__.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__email__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__email__", "name": "__email__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "__main__.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__license__", "name": "__license__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "__main__.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "__main__.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "__main__.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "__main__.__spec__", "name": "__spec__", "setter_type": null, "type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "__title__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__title__", "name": "__title__", "setter_type": null, "type": "builtins.str"}}, "__url__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__url__", "name": "__url__", "setter_type": null, "type": "builtins.str"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "__main__.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}}, "path": "src/__init__.py"}