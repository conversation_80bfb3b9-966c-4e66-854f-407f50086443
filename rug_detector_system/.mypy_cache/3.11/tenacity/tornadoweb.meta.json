{"data_mtime": 1751828793, "dep_lines": [15, 16, 18, 1, 1, 1, 1, 1, 1, 26, 23], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30, 30, 25, 5], "dependencies": ["sys", "typing", "tenacity", "builtins", "_frozen_importlib", "abc", "tenacity.retry", "tenacity.stop", "tenacity.wait"], "hash": "a64eb074e87f8c8349d864c838ca996cb9c07d32", "id": "tenacity.tornadoweb", "ignore_all": true, "interface_hash": "e5aaebaf72ffb5e48c22b72fbbaad989423379c3", "mtime": 1751824993, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/tenacity/tornadoweb.py", "plugin_data": null, "size": 2125, "suppressed": ["tornado.concurrent", "tornado"], "version_id": "1.16.1"}