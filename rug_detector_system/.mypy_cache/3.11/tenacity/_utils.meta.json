{"data_mtime": 1751828792, "dep_lines": [16, 17, 18, 19, 20, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 30, 30], "dependencies": ["functools", "inspect", "sys", "typing", "datetime", "builtins", "_frozen_importlib", "abc"], "hash": "045531cc5013ef64a191afaf4f24fcc0347832d1", "id": "tenacity._utils", "ignore_all": true, "interface_hash": "c85926ebbc0c9825dc46c42e0ee872defd02412a", "mtime": 1751824993, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/tenacity/_utils.py", "plugin_data": null, "size": 2916, "suppressed": [], "version_id": "1.16.1"}