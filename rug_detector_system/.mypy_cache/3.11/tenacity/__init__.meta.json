{"data_mtime": 1751828793, "dep_lines": [26, 28, 31, 47, 51, 60, 72, 76, 80, 93, 664, 18, 19, 20, 21, 22, 23, 24, 25, 26, 89, 91, 1, 1, 1, 1, 1, 1, 1, 84], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 20, 25, 25, 5, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["concurrent.futures", "tenacity._utils", "tenacity.retry", "tenacity.nap", "tenacity.stop", "tenacity.wait", "tenacity.before", "tenacity.after", "tenacity.before_sleep", "tenacity.asyncio", "tenacity.tornadoweb", "dataclasses", "functools", "sys", "threading", "time", "typing", "warnings", "abc", "concurrent", "types", "typing_extensions", "builtins", "_frozen_importlib", "_thread", "_typeshed", "concurrent.futures._base", "enum", "tenacity.asyncio.retry"], "hash": "91354b7aeeaf70f36dbb2114cb17074bc44a5360", "id": "tenacity", "ignore_all": true, "interface_hash": "495665d8c8c146970f8c8075a0ad9da3710a0764", "mtime": 1751824993, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/tenacity/__init__.py", "plugin_data": null, "size": 24060, "suppressed": ["tornado"], "version_id": "1.16.1"}