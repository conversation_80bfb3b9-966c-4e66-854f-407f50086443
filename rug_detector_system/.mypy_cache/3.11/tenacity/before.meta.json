{"data_mtime": 1751828793, "dep_lines": [19, 17, 19, 22, 1, 1, 1], "dep_prios": [10, 10, 20, 25, 5, 30, 30], "dependencies": ["tenacity._utils", "typing", "tenacity", "logging", "builtins", "_frozen_importlib", "abc"], "hash": "e1f5003c81db9a37a009b45a94a701b70fc20235", "id": "tenacity.before", "ignore_all": true, "interface_hash": "bc3c90ab4d407f7ae858763d0a3b479fa3da37f6", "mtime": 1751824993, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/tenacity/before.py", "plugin_data": null, "size": 1544, "suppressed": [], "version_id": "1.16.1"}