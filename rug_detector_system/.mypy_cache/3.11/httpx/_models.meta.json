{"data_mtime": 1751828792, "dep_lines": [5, 9, 10, 11, 13, 14, 24, 33, 34, 35, 48, 49, 1, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 20, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["email.message", "urllib.request", "collections.abc", "http.cookiejar", "httpx._content", "httpx._decoders", "httpx._exceptions", "httpx._multipart", "httpx._status_codes", "httpx._types", "httpx._urls", "httpx._utils", "__future__", "codecs", "datetime", "email", "json", "re", "typing", "urllib", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "contextlib", "http", "types", "typing_extensions"], "hash": "fa1ee00b603010cd0e3d56b7f8ed51048988f30e", "id": "httpx._models", "ignore_all": true, "interface_hash": "eccb95e786d579af9ad2c9949f0c4ee069a40c2f", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/httpx/_models.py", "plugin_data": null, "size": 44700, "suppressed": [], "version_id": "1.16.1"}