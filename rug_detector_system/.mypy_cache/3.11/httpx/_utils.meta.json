{"data_mtime": 1751828792, "dep_lines": [7, 9, 12, 1, 3, 4, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 5, 10, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["urllib.request", "httpx._types", "httpx._urls", "__future__", "ipaddress", "os", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "49bc1e11bfc56c18f49d86d0f5f78f9efe929b07", "id": "httpx._utils", "ignore_all": true, "interface_hash": "1865f0d70b2c401900af72d4ea8fb7b14c8f4310", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/httpx/_utils.py", "plugin_data": null, "size": 8285, "suppressed": [], "version_id": "1.16.1"}