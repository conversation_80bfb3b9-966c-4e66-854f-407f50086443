{"data_mtime": 1751828792, "dep_lines": [14, 7, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 24, 19, 31], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 10, 10, 10], "dependencies": ["httpx._exceptions", "__future__", "codecs", "io", "typing", "zlib", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "httpx._models", "typing_extensions"], "hash": "556d0e46aa956b95f9ac134e38ab42337afa0cb7", "id": "httpx._decoders", "ignore_all": true, "interface_hash": "9049dd94419be05d877aa7776e8c6d9f39b13e20", "mtime": 1751824997, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/httpx/_decoders.py", "plugin_data": null, "size": 12041, "suppressed": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "brotli", "zstandard"], "version_id": "1.16.1"}