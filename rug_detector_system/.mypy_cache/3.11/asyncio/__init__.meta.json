{"data_mtime": 1751828453, "dep_lines": [4, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 24, 27, 28, 33, 3, 5, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "asyncio.base_events", "asyncio.coroutines", "asyncio.events", "asyncio.exceptions", "asyncio.futures", "asyncio.locks", "asyncio.protocols", "asyncio.queues", "asyncio.runners", "asyncio.streams", "asyncio.subprocess", "asyncio.tasks", "asyncio.transports", "asyncio.threads", "asyncio.taskgroups", "asyncio.timeouts", "asyncio.unix_events", "sys", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "a11e0640106c303171d5e31ced34a5d8dfddf081", "id": "asyncio", "ignore_all": true, "interface_hash": "d5a5f5628835ee745c623113f07cec489e2e97ca", "mtime": 1751824995, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/Users/<USER>/ar/rug_detector_system/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/__init__.pyi", "plugin_data": null, "size": 51809, "suppressed": [], "version_id": "1.16.1"}