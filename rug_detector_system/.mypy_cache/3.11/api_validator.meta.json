{"data_mtime": 1751828862, "dep_lines": [26, 19, 20, 21, 22, 24, 25, 26, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tenacity.retry", "asyncio", "time", "dataclasses", "typing", "httpx", "structlog", "tenacity", "config", "builtins", "_asyncio", "_frozen_importlib", "_ssl", "_typeshed", "abc", "datetime", "http", "http.cookiejar", "httpx._auth", "httpx._client", "httpx._config", "httpx._models", "httpx._transports", "httpx._transports.base", "httpx._urls", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "ssl", "tenacity.asyncio", "tenacity.asyncio.retry", "tenacity.stop", "tenacity.wait", "types", "typing_extensions"], "hash": "8a7dd56615b410348aba25b84501b0fe7257f073", "id": "api_validator", "ignore_all": false, "interface_hash": "643dc79b37ca80c716e0ddedca8436b835afd2f8", "mtime": 1751828848, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "src/api_validator.py", "plugin_data": null, "size": 14817, "suppressed": [], "version_id": "1.16.1"}