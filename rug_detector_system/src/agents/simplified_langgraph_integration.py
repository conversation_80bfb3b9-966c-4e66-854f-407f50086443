#!/usr/bin/env python3
"""
Simplified LangGraph Multi-Agent Integration for Rug Detection

This module implements a working multi-agent architecture using LangGraph
with the most powerful available Ollama models.

Author: MLDevOps Architect
Version: 4.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import time
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from langgraph.graph import StateGraph, START, END
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_ollama import ChatOllama

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from analysis.static_analyzer import StaticAnalyzer
from logging_config import get_logger


class AgentRole(Enum):
    """Agent roles in the multi-agent system."""
    PATTERN_ANALYST = "pattern_analyst"
    MARKET_DATA_ANALYST = "market_data_analyst"
    SOCIAL_SENTIMENT_ANALYST = "social_sentiment_analyst"
    RISK_COORDINATOR = "risk_coordinator"


@dataclass
class SimpleAgentState:
    """Simplified state for the multi-agent system."""
    contract_address: str
    contract_code: Optional[str]
    agent_analyses: Dict[str, Dict[str, Any]]
    final_risk_score: Optional[float]
    final_recommendation: Optional[str]
    confidence_level: Optional[float]
    analysis_complete: bool
    timestamp: float
    
    def __post_init__(self):
        if not hasattr(self, 'agent_analyses'):
            self.agent_analyses = {}
        if not hasattr(self, 'analysis_complete'):
            self.analysis_complete = False


class SimplifiedPatternAgent:
    """Simplified pattern analysis agent."""
    
    def __init__(self, llm: ChatOllama):
        """Initialize pattern analysis agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
        self.static_analyzer = None
        
    async def initialize(self):
        """Initialize the agent."""
        self.static_analyzer = StaticAnalyzer()
        await self.static_analyzer.initialize()
        
    async def analyze(self, state: SimpleAgentState) -> SimpleAgentState:
        """Perform pattern analysis with comprehensive error handling."""
        analysis_start = time.time()

        try:
            self.logger.info(f"Pattern analysis for: {state.contract_address}")

            # Perform static analysis with timeout
            static_result = None
            try:
                if state.contract_code:
                    # Add timeout for static analysis
                    static_result = await asyncio.wait_for(
                        self.static_analyzer.analyze_contract(
                            state.contract_address, state.contract_code
                        ),
                        timeout=30.0  # 30 second timeout
                    )
                else:
                    # Mock static result for testing
                    from analysis.static_analyzer import AnalysisResult
                    static_result = AnalysisResult(
                        contract_address=state.contract_address,
                        contract_name="Unknown",
                        analysis_timestamp=time.time(),
                        vulnerabilities=[],
                        slither_output=None,
                        gas_analysis=None,
                        complexity_metrics=None,
                        overall_risk_score=0.7,
                        analysis_duration=0.0
                    )
            except asyncio.TimeoutError:
                self.logger.warning(f"Static analysis timeout for {state.contract_address}")
                # Create fallback result
                from analysis.static_analyzer import AnalysisResult
                static_result = AnalysisResult(
                    contract_address=state.contract_address,
                    contract_name="Unknown",
                    analysis_timestamp=time.time(),
                    vulnerabilities=[],
                    slither_output=None,
                    gas_analysis=None,
                    complexity_metrics=None,
                    overall_risk_score=0.5,  # Medium risk for timeout
                    analysis_duration=30.0
                )
            except Exception as e:
                self.logger.error(f"Static analysis failed for {state.contract_address}: {e}")
                # Create error fallback result
                from analysis.static_analyzer import AnalysisResult
                static_result = AnalysisResult(
                    contract_address=state.contract_address,
                    contract_name="Unknown",
                    analysis_timestamp=time.time(),
                    vulnerabilities=[],
                    slither_output=None,
                    gas_analysis=None,
                    complexity_metrics=None,
                    overall_risk_score=0.3,  # Lower confidence due to error
                    analysis_duration=time.time() - analysis_start
                )
            
            # Enhanced pattern analysis with LLM
            pattern_prompt = f"""
            You are an expert smart contract security analyst specializing in rug-pull detection.
            
            Analyze the following contract for rug-pull patterns:
            Contract Address: {state.contract_address}
            
            Static Analysis Results:
            - Risk Score: {static_result.overall_risk_score}
            - Vulnerabilities Found: {len(static_result.vulnerabilities)}
            
            Consider these rug-pull indicators:
            1. Honeypot mechanisms
            2. Ownership centralization
            3. Liquidity manipulation functions
            4. Hidden backdoors
            5. Unusual fee structures
            
            Provide a risk assessment with:
            1. Risk level (minimal/low/medium/high/critical)
            2. Confidence (0-1)
            3. Key findings
            """
            
            # Get LLM analysis with timeout and error handling
            try:
                response = await asyncio.wait_for(
                    self.llm.ainvoke([
                        SystemMessage(content=pattern_prompt),
                        HumanMessage(content="Analyze this contract for rug-pull patterns.")
                    ]),
                    timeout=60.0  # 60 second timeout for LLM
                )

                # Extract risk level from response
                response_text = response.content.lower()
                if 'critical' in response_text:
                    risk_level = 'critical'
                    confidence = 0.9
                elif 'high' in response_text:
                    risk_level = 'high'
                    confidence = 0.8
                elif 'medium' in response_text:
                    risk_level = 'medium'
                    confidence = 0.7
                elif 'low' in response_text:
                    risk_level = 'low'
                    confidence = 0.6
                else:
                    risk_level = 'minimal'
                    confidence = 0.5

                llm_analysis = response.content

            except asyncio.TimeoutError:
                self.logger.warning(f"LLM analysis timeout for {state.contract_address}")
                # Fallback based on static analysis
                if static_result.overall_risk_score > 0.8:
                    risk_level = 'high'
                    confidence = 0.6
                elif static_result.overall_risk_score > 0.5:
                    risk_level = 'medium'
                    confidence = 0.5
                else:
                    risk_level = 'low'
                    confidence = 0.4
                llm_analysis = "LLM analysis timed out - using static analysis fallback"

            except Exception as e:
                self.logger.error(f"LLM analysis failed for {state.contract_address}: {e}")
                # Conservative fallback
                risk_level = 'medium'
                confidence = 0.3
                llm_analysis = f"LLM analysis failed: {str(e)}"
            
            analysis_result = {
                'agent_role': AgentRole.PATTERN_ANALYST.value,
                'recommended_risk_level': risk_level,
                'confidence': confidence,
                'llm_analysis': llm_analysis,
                'static_risk_score': static_result.overall_risk_score,
                'vulnerability_count': len(static_result.vulnerabilities),
                'timestamp': time.time()
            }
            
            state.agent_analyses['pattern_analyst'] = analysis_result
            return state
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {e}")
            state.agent_analyses['pattern_analyst'] = {
                'agent_role': AgentRole.PATTERN_ANALYST.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }
            return state


class SimplifiedMarketAgent:
    """Simplified market data analysis agent."""
    
    def __init__(self, llm: ChatOllama):
        """Initialize market data agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
        
    async def analyze(self, state: SimpleAgentState) -> SimpleAgentState:
        """Perform market analysis."""
        try:
            self.logger.info(f"Market analysis for: {state.contract_address}")
            
            # Mock market data for testing
            market_data = {
                'fear_greed_index': 45,
                'market_sentiment': 'neutral',
                'volatility': 'medium'
            }
            
            # Analyze market context with LLM
            market_prompt = f"""
            You are a DeFi market analyst specializing in risk assessment.
            
            Analyze the current market conditions for contract: {state.contract_address}
            
            Current Market Data:
            - Fear & Greed Index: {market_data['fear_greed_index']} (neutral)
            - Market Sentiment: {market_data['market_sentiment']}
            - Volatility: {market_data['volatility']}
            
            Consider these market risk factors:
            1. Overall market sentiment
            2. DeFi sector trends
            3. Liquidity conditions
            4. Market manipulation indicators
            
            Provide analysis on:
            1. Market risk level (minimal/low/medium/high/critical)
            2. Confidence (0-1)
            """
            
            response = await self.llm.ainvoke([
                SystemMessage(content=market_prompt),
                HumanMessage(content="Analyze current market conditions for rug-pull risk.")
            ])
            
            # Extract risk level
            response_text = response.content.lower()
            if 'high' in response_text or 'critical' in response_text:
                market_risk = 'high'
                confidence = 0.8
            elif 'medium' in response_text:
                market_risk = 'medium'
                confidence = 0.7
            else:
                market_risk = 'low'
                confidence = 0.6
            
            analysis_result = {
                'agent_role': AgentRole.MARKET_DATA_ANALYST.value,
                'market_risk_level': market_risk,
                'confidence': confidence,
                'market_analysis': response.content,
                'fear_greed_index': market_data,
                'timestamp': time.time()
            }
            
            state.agent_analyses['market_data_analyst'] = analysis_result
            return state
            
        except Exception as e:
            self.logger.error(f"Market analysis failed: {e}")
            state.agent_analyses['market_data_analyst'] = {
                'agent_role': AgentRole.MARKET_DATA_ANALYST.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }
            return state


class SimplifiedSocialAgent:
    """Simplified social sentiment analysis agent."""
    
    def __init__(self, llm: ChatOllama):
        """Initialize social sentiment agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
    
    async def analyze(self, state: SimpleAgentState) -> SimpleAgentState:
        """Perform social sentiment analysis."""
        try:
            self.logger.info(f"Social sentiment analysis for: {state.contract_address}")
            
            # Mock social data for testing
            social_data = {
                'sentiment_score': 0.5,
                'mention_count': 100,
                'scam_keywords': []
            }
            
            # Analyze social sentiment with LLM
            sentiment_prompt = f"""
            You are a social media analyst specializing in crypto sentiment.
            
            Analyze social sentiment for: {state.contract_address}
            
            Social Data:
            - Sentiment Score: {social_data['sentiment_score']} (neutral)
            - Mention Count: {social_data['mention_count']}
            - Scam Keywords: {social_data['scam_keywords']}
            
            Consider these social risk indicators:
            1. Sudden spikes in interest
            2. Association with scam keywords
            3. Influencer promotion patterns
            4. Community engagement authenticity
            
            Assess:
            1. Social risk level (minimal/low/medium/high/critical)
            2. Confidence (0-1)
            """
            
            response = await self.llm.ainvoke([
                SystemMessage(content=sentiment_prompt),
                HumanMessage(content="Analyze social sentiment for rug-pull indicators.")
            ])
            
            # Extract risk level
            response_text = response.content.lower()
            if 'high' in response_text or 'critical' in response_text:
                social_risk = 'high'
                confidence = 0.7
            elif 'medium' in response_text:
                social_risk = 'medium'
                confidence = 0.6
            else:
                social_risk = 'low'
                confidence = 0.5
            
            analysis_result = {
                'agent_role': AgentRole.SOCIAL_SENTIMENT_ANALYST.value,
                'social_risk_level': social_risk,
                'confidence': confidence,
                'sentiment_analysis': response.content,
                'social_data': social_data,
                'timestamp': time.time()
            }
            
            state.agent_analyses['social_sentiment_analyst'] = analysis_result
            return state
            
        except Exception as e:
            self.logger.error(f"Social sentiment analysis failed: {e}")
            state.agent_analyses['social_sentiment_analyst'] = {
                'agent_role': AgentRole.SOCIAL_SENTIMENT_ANALYST.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }
            return state


class SimplifiedCoordinatorAgent:
    """Simplified risk coordinator agent."""
    
    def __init__(self, llm: ChatOllama):
        """Initialize risk coordinator agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
    
    async def coordinate(self, state: SimpleAgentState) -> SimpleAgentState:
        """Coordinate and synthesize all agent analyses."""
        try:
            self.logger.info(f"Risk coordination for: {state.contract_address}")
            
            # Extract analyses from all agents
            pattern_analysis = state.agent_analyses.get('pattern_analyst', {})
            market_analysis = state.agent_analyses.get('market_data_analyst', {})
            social_analysis = state.agent_analyses.get('social_sentiment_analyst', {})
            
            # Calculate final risk score
            final_risk_score = self._calculate_final_risk_score(
                pattern_analysis, market_analysis, social_analysis
            )
            
            # Determine final recommendation
            final_recommendation = self._determine_final_recommendation(final_risk_score)
            
            # Calculate overall confidence
            confidence_level = self._calculate_overall_confidence(
                pattern_analysis, market_analysis, social_analysis
            )
            
            state.final_risk_score = final_risk_score
            state.final_recommendation = final_recommendation
            state.confidence_level = confidence_level
            state.analysis_complete = True
            
            return state
            
        except Exception as e:
            self.logger.error(f"Risk coordination failed: {e}")
            state.final_risk_score = 0.0
            state.final_recommendation = 'error'
            state.confidence_level = 0.0
            state.analysis_complete = False
            return state
    
    def _calculate_final_risk_score(self, pattern_analysis: Dict, 
                                   market_analysis: Dict, 
                                   social_analysis: Dict) -> float:
        """Calculate final risk score from all analyses."""
        # Weight the different analyses
        weights = {
            'pattern': 0.6,  # Pattern analysis is most important
            'market': 0.25,  # Market conditions are significant
            'social': 0.15   # Social sentiment is supplementary
        }
        
        # Convert risk levels to scores
        risk_level_scores = {
            'minimal': 0.1,
            'low': 0.3,
            'medium': 0.5,
            'high': 0.7,
            'critical': 0.9,
            'unknown': 0.5
        }
        
        pattern_score = risk_level_scores.get(
            pattern_analysis.get('recommended_risk_level', 'unknown'), 0.5
        )
        market_score = risk_level_scores.get(
            market_analysis.get('market_risk_level', 'unknown'), 0.5
        )
        social_score = risk_level_scores.get(
            social_analysis.get('social_risk_level', 'unknown'), 0.5
        )
        
        # Calculate weighted average
        final_score = (
            pattern_score * weights['pattern'] +
            market_score * weights['market'] +
            social_score * weights['social']
        )
        
        return min(final_score, 1.0)
    
    def _determine_final_recommendation(self, risk_score: float) -> str:
        """Determine final recommendation from risk score."""
        if risk_score >= 0.8:
            return 'avoid'
        elif risk_score >= 0.6:
            return 'high_caution'
        elif risk_score >= 0.4:
            return 'caution'
        elif risk_score >= 0.2:
            return 'low_risk'
        else:
            return 'safe'
    
    def _calculate_overall_confidence(self, pattern_analysis: Dict,
                                     market_analysis: Dict,
                                     social_analysis: Dict) -> float:
        """Calculate overall confidence from all analyses."""
        confidences = [
            pattern_analysis.get('confidence', 0),
            market_analysis.get('confidence', 0),
            social_analysis.get('confidence', 0)
        ]
        
        # Remove zero confidences (failed analyses)
        valid_confidences = [c for c in confidences if c > 0]
        
        if not valid_confidences:
            return 0.0
        
        return sum(valid_confidences) / len(valid_confidences)


async def create_simplified_multi_agent_system(fast_mode: bool = False) -> StateGraph:
    """Create the simplified multi-agent rug detector system."""

    # Initialize Ollama LLM with model selection based on speed requirements
    ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')

    if fast_mode:
        # Use faster, smaller models for speed
        primary_model = os.getenv('OLLAMA_GENERAL_MODEL', 'llama3.1:8b')
        num_predict = 1000  # Shorter responses
        temperature = 0.2   # Slightly higher for faster generation
    else:
        # Use most powerful models for accuracy
        primary_model = os.getenv('OLLAMA_MODEL', 'deepseek-r1:latest')
        num_predict = 2000  # Longer responses
        temperature = 0.1   # Lower for more deterministic results

    llm = ChatOllama(
        model=primary_model,
        base_url=ollama_base_url,
        temperature=temperature,
        num_predict=num_predict
    )
    
    # Initialize agents
    pattern_agent = SimplifiedPatternAgent(llm)
    market_agent = SimplifiedMarketAgent(llm)
    social_agent = SimplifiedSocialAgent(llm)
    coordinator_agent = SimplifiedCoordinatorAgent(llm)
    
    await pattern_agent.initialize()
    
    # Define node functions
    async def pattern_analysis_node(state: SimpleAgentState) -> SimpleAgentState:
        """Pattern analysis node."""
        return await pattern_agent.analyze(state)
    
    async def market_analysis_node(state: SimpleAgentState) -> SimpleAgentState:
        """Market analysis node."""
        return await market_agent.analyze(state)
    
    async def social_analysis_node(state: SimpleAgentState) -> SimpleAgentState:
        """Social sentiment analysis node."""
        return await social_agent.analyze(state)
    
    async def coordination_node(state: SimpleAgentState) -> SimpleAgentState:
        """Risk coordination node."""
        return await coordinator_agent.coordinate(state)
    
    # Build the graph
    builder = StateGraph(SimpleAgentState)
    
    # Add nodes
    builder.add_node("pattern_analysis", pattern_analysis_node)
    builder.add_node("market_analysis", market_analysis_node)
    builder.add_node("social_analysis", social_analysis_node)
    builder.add_node("risk_coordination", coordination_node)
    
    # Add edges for optimized sequential execution
    builder.add_edge(START, "pattern_analysis")
    builder.add_edge("pattern_analysis", "market_analysis")
    builder.add_edge("market_analysis", "social_analysis")
    builder.add_edge("social_analysis", "risk_coordination")
    builder.add_edge("risk_coordination", END)
    
    return builder.compile()


# Simplified analysis function
async def analyze_contract_with_simplified_agents(contract_address: str,
                                                 contract_code: Optional[str] = None,
                                                 fast_mode: bool = False) -> Dict[str, Any]:
    """Analyze a contract using the simplified multi-agent system."""

    # Create the multi-agent system
    agent_system = await create_simplified_multi_agent_system(fast_mode=fast_mode)
    
    # Initialize state
    initial_state = SimpleAgentState(
        contract_address=contract_address,
        contract_code=contract_code,
        agent_analyses={},
        final_risk_score=None,
        final_recommendation=None,
        confidence_level=None,
        analysis_complete=False,
        timestamp=time.time()
    )
    
    # Run the analysis
    final_state = await agent_system.ainvoke(initial_state)

    # Handle both dict and object returns
    if isinstance(final_state, dict):
        return {
            'contract_address': contract_address,
            'final_risk_score': final_state.get('final_risk_score'),
            'final_recommendation': final_state.get('final_recommendation'),
            'confidence_level': final_state.get('confidence_level'),
            'agent_analyses': final_state.get('agent_analyses', {}),
            'analysis_complete': final_state.get('analysis_complete', False),
            'timestamp': final_state.get('timestamp', time.time())
        }
    else:
        return {
            'contract_address': contract_address,
            'final_risk_score': final_state.final_risk_score,
            'final_recommendation': final_state.final_recommendation,
            'confidence_level': final_state.confidence_level,
            'agent_analyses': final_state.agent_analyses,
            'analysis_complete': final_state.analysis_complete,
            'timestamp': final_state.timestamp
        }
