#!/usr/bin/env python3
"""
LangGraph Agentic AI Integration for Rug Detector System

This module implements a multi-agent architecture using LangGraph to enhance
the rug detection capabilities with real-time data integration and dynamic
context-aware analysis.

Author: MLDevOps Architect
Version: 4.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import json
import time
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Literal
from dataclasses import dataclass, asdict
from enum import Enum

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_ollama import ChatOllama
from langchain_core.tools import tool

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from analysis.static_analyzer import StaticAnalyzer, VulnerabilityType, SeverityLevel
from analysis.enhanced_risk_scorer import <PERSON>han<PERSON><PERSON><PERSON><PERSON>core<PERSON>, <PERSON><PERSON>evel
from logging_config import get_logger


class AgentRole(Enum):
    """Agent roles in the multi-agent system."""
    PATTERN_ANALYST = "pattern_analyst"
    MARKET_DATA_ANALYST = "market_data_analyst"
    SOCIAL_SENTIMENT_ANALYST = "social_sentiment_analyst"
    RISK_COORDINATOR = "risk_coordinator"
    BLOCKCHAIN_MONITOR = "blockchain_monitor"


@dataclass
class AgentState:
    """State for individual agents."""
    messages: List[Any]
    contract_address: str
    agent_role: AgentRole
    analysis_data: Dict[str, Any]
    confidence: float
    findings: List[str]
    risk_factors: List[Dict[str, Any]]
    timestamp: float
    
    def __post_init__(self):
        if not hasattr(self, 'messages'):
            self.messages = []


@dataclass
class MultiAgentState:
    """Global state for the multi-agent system."""
    messages: List[Any]
    contract_address: str
    contract_code: Optional[str]
    agent_analyses: Dict[str, Dict[str, Any]]
    market_data: Dict[str, Any]
    social_signals: Dict[str, Any]
    blockchain_data: Dict[str, Any]
    final_risk_score: Optional[float]
    final_recommendation: Optional[str]
    confidence_level: Optional[float]
    analysis_complete: bool
    timestamp: float
    
    def __post_init__(self):
        if not hasattr(self, 'messages'):
            self.messages = []
        if not hasattr(self, 'agent_analyses'):
            self.agent_analyses = {}
        if not hasattr(self, 'analysis_complete'):
            self.analysis_complete = False


class PatternAnalysisAgent:
    """Enhanced pattern analysis agent with dynamic context awareness."""

    def __init__(self, llm: ChatOllama):
        """Initialize pattern analysis agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
        self.static_analyzer = None
        
    async def initialize(self):
        """Initialize the agent."""
        self.static_analyzer = StaticAnalyzer()
        await self.static_analyzer.initialize()
        
    async def analyze(self, state: MultiAgentState) -> Dict[str, Any]:
        """Perform enhanced pattern analysis with context awareness."""
        try:
            self.logger.info(f"Pattern analysis for: {state.contract_address}")
            
            # Perform static analysis
            if state.contract_code:
                static_result = await self.static_analyzer.analyze_contract(
                    state.contract_address, state.contract_code
                )
            else:
                # Fetch contract code from blockchain
                contract_code = await self._fetch_contract_code(state.contract_address)
                static_result = await self.static_analyzer.analyze_contract(
                    state.contract_address, contract_code
                )
            
            # Enhanced pattern analysis with LLM
            pattern_prompt = f"""
            You are an expert smart contract security analyst specializing in rug-pull detection.
            
            Analyze the following contract for rug-pull patterns:
            Contract Address: {state.contract_address}
            
            Static Analysis Results:
            - Vulnerabilities Found: {len(static_result.vulnerabilities)}
            - Risk Score: {static_result.overall_risk_score}
            
            Vulnerability Details:
            {self._format_vulnerabilities(static_result.vulnerabilities)}
            
            Consider these advanced rug-pull indicators:
            1. Honeypot mechanisms (transfer restrictions)
            2. Ownership centralization patterns
            3. Liquidity manipulation functions
            4. Hidden backdoors and admin functions
            5. Unusual fee structures
            6. Time-based restrictions
            7. Blacklist/whitelist mechanisms
            
            Provide a detailed analysis with:
            1. Specific rug-pull patterns identified
            2. Severity assessment for each pattern
            3. Contextual risk factors
            4. Confidence level (0-1)
            5. Recommended risk level (minimal/low/medium/high/critical)
            """
            
            # Get LLM analysis
            response = await self.llm.ainvoke([
                SystemMessage(content=pattern_prompt),
                HumanMessage(content="Analyze this contract for rug-pull patterns.")
            ])
            
            # Extract structured analysis
            analysis_result = {
                'agent_role': AgentRole.PATTERN_ANALYST.value,
                'static_analysis': static_result.to_dict(),
                'llm_analysis': response.content,
                'risk_factors': self._extract_risk_factors(static_result, response.content),
                'confidence': self._calculate_pattern_confidence(static_result),
                'recommended_risk_level': self._determine_risk_level(static_result),
                'timestamp': time.time()
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {e}")
            return {
                'agent_role': AgentRole.PATTERN_ANALYST.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }
    
    def _format_vulnerabilities(self, vulnerabilities: List) -> str:
        """Format vulnerabilities for LLM analysis."""
        if not vulnerabilities:
            return "No vulnerabilities detected"
        
        formatted = []
        for vuln in vulnerabilities:
            formatted.append(f"- {vuln.type.value}: {vuln.severity.value} "
                           f"(confidence: {vuln.confidence:.2f}) - {vuln.title}")
        
        return "\n".join(formatted)
    
    def _extract_risk_factors(self, static_result, llm_analysis: str) -> List[Dict[str, Any]]:
        """Extract risk factors from analysis results."""
        risk_factors = []
        
        # Add static analysis risk factors
        for vuln in static_result.vulnerabilities:
            risk_factors.append({
                'type': vuln.type.value,
                'severity': vuln.severity.value,
                'confidence': vuln.confidence,
                'source': 'static_analysis',
                'description': vuln.title
            })
        
        # Add LLM-identified risk factors (simplified extraction)
        if 'honeypot' in llm_analysis.lower():
            risk_factors.append({
                'type': 'honeypot_pattern',
                'severity': 'high',
                'confidence': 0.8,
                'source': 'llm_analysis',
                'description': 'Honeypot patterns detected in LLM analysis'
            })
        
        return risk_factors
    
    def _calculate_pattern_confidence(self, static_result) -> float:
        """Calculate confidence in pattern analysis."""
        if not static_result.vulnerabilities:
            return 0.9  # High confidence in clean contracts
        
        # Calculate based on vulnerability confidence and count
        avg_confidence = sum(v.confidence for v in static_result.vulnerabilities) / len(static_result.vulnerabilities)
        count_factor = min(len(static_result.vulnerabilities) / 10, 1.0)
        
        return min(avg_confidence * (1 + count_factor * 0.2), 1.0)
    
    def _determine_risk_level(self, static_result) -> str:
        """Determine risk level from static analysis."""
        if static_result.overall_risk_score >= 0.8:
            return 'critical'
        elif static_result.overall_risk_score >= 0.6:
            return 'high'
        elif static_result.overall_risk_score >= 0.4:
            return 'medium'
        elif static_result.overall_risk_score >= 0.2:
            return 'low'
        else:
            return 'minimal'
    
    async def _fetch_contract_code(self, contract_address: str) -> str:
        """Fetch contract source code from blockchain."""
        # This would integrate with Etherscan API or similar
        # For now, return a placeholder
        return f"// Contract code for {contract_address} would be fetched here"


class MarketDataAgent:
    """Market data analysis agent with real-time integration."""

    def __init__(self, llm: ChatOllama):
        """Initialize market data agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
        
    @tool
    async def fetch_defi_llama_data(self, protocol_name: str) -> Dict[str, Any]:
        """Fetch protocol data from DeFiLlama API."""
        import aiohttp
        
        try:
            async with aiohttp.ClientSession() as session:
                # Get protocol list
                async with session.get("https://api.llama.fi/protocols") as response:
                    protocols = await response.json()
                
                # Find matching protocol
                protocol = next((p for p in protocols if protocol_name.lower() in p['name'].lower()), None)
                
                if protocol:
                    # Get detailed protocol data
                    async with session.get(f"https://api.llama.fi/protocol/{protocol['slug']}") as response:
                        protocol_data = await response.json()
                    
                    return {
                        'name': protocol_data.get('name'),
                        'tvl': protocol_data.get('tvl'),
                        'chain_tvls': protocol_data.get('chainTvls', {}),
                        'change_1d': protocol_data.get('change_1d'),
                        'change_7d': protocol_data.get('change_7d'),
                        'mcap': protocol_data.get('mcap'),
                        'category': protocol_data.get('category')
                    }
                
                return {'error': 'Protocol not found'}
                
        except Exception as e:
            return {'error': str(e)}
    
    @tool
    async def fetch_fear_greed_index(self) -> Dict[str, Any]:
        """Fetch crypto fear and greed index from Alternative.me."""
        import aiohttp
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://api.alternative.me/fng/?limit=10") as response:
                    data = await response.json()
                
                current = data['data'][0]
                return {
                    'value': int(current['value']),
                    'value_classification': current['value_classification'],
                    'timestamp': current['timestamp'],
                    'time_until_update': current.get('time_until_update'),
                    'historical': data['data'][1:6]  # Last 5 days
                }
                
        except Exception as e:
            return {'error': str(e)}
    
    async def analyze(self, state: MultiAgentState) -> Dict[str, Any]:
        """Perform market data analysis."""
        try:
            self.logger.info(f"Market analysis for: {state.contract_address}")
            
            # Fetch market sentiment data
            fear_greed = await self.fetch_fear_greed_index()
            
            # Analyze market context with LLM
            market_prompt = f"""
            You are a DeFi market analyst specializing in risk assessment.
            
            Analyze the current market conditions for contract: {state.contract_address}
            
            Current Market Data:
            - Fear & Greed Index: {fear_greed.get('value', 'N/A')} ({fear_greed.get('value_classification', 'N/A')})
            
            Consider these market risk factors:
            1. Overall market sentiment and volatility
            2. DeFi sector trends and TVL changes
            3. Regulatory environment
            4. Market manipulation indicators
            5. Liquidity conditions
            
            Provide analysis on:
            1. Market risk level for new token launches
            2. Likelihood of rug-pull activity in current conditions
            3. Investor sentiment and behavior patterns
            4. Recommended caution level
            """
            
            response = await self.llm.ainvoke([
                SystemMessage(content=market_prompt),
                HumanMessage(content="Analyze current market conditions for rug-pull risk.")
            ])
            
            return {
                'agent_role': AgentRole.MARKET_DATA_ANALYST.value,
                'fear_greed_index': fear_greed,
                'market_analysis': response.content,
                'market_risk_level': self._assess_market_risk(fear_greed),
                'confidence': 0.8,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"Market analysis failed: {e}")
            return {
                'agent_role': AgentRole.MARKET_DATA_ANALYST.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }
    
    def _assess_market_risk(self, fear_greed_data: Dict[str, Any]) -> str:
        """Assess market risk level based on fear/greed index."""
        if 'error' in fear_greed_data:
            return 'unknown'
        
        value = fear_greed_data.get('value', 50)
        
        if value <= 20:  # Extreme fear
            return 'high'  # High risk of panic selling, but lower rug-pull risk
        elif value <= 40:  # Fear
            return 'medium'
        elif value >= 80:  # Extreme greed
            return 'high'  # High risk of rug-pulls due to FOMO
        elif value >= 60:  # Greed
            return 'medium'
        else:  # Neutral
            return 'low'


class SocialSentimentAgent:
    """Social sentiment analysis agent."""

    def __init__(self, llm: ChatOllama):
        """Initialize social sentiment agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
    
    @tool
    async def fetch_google_trends(self, search_term: str) -> Dict[str, Any]:
        """Fetch Google Trends data for a search term."""
        # This would integrate with Google Trends API
        # For now, return mock data
        return {
            'search_term': search_term,
            'interest_over_time': [
                {'date': '2025-07-01', 'value': 45},
                {'date': '2025-07-02', 'value': 67},
                {'date': '2025-07-03', 'value': 89},
                {'date': '2025-07-04', 'value': 92},
                {'date': '2025-07-05', 'value': 78},
                {'date': '2025-07-06', 'value': 56}
            ],
            'related_queries': ['crypto scam', 'rug pull', 'token launch'],
            'trend_direction': 'increasing'
        }
    
    async def analyze(self, state: MultiAgentState) -> Dict[str, Any]:
        """Perform social sentiment analysis."""
        try:
            self.logger.info(f"Social sentiment analysis for: {state.contract_address}")
            
            # Extract token name/symbol for trend analysis
            token_name = self._extract_token_name(state.contract_address)
            
            # Fetch social signals
            trends_data = await self.fetch_google_trends(token_name)
            
            # Analyze social sentiment with LLM
            sentiment_prompt = f"""
            You are a social media analyst specializing in crypto sentiment and rug-pull detection.
            
            Analyze social sentiment for: {state.contract_address}
            Token: {token_name}
            
            Google Trends Data:
            - Search interest trend: {trends_data.get('trend_direction', 'unknown')}
            - Related queries: {trends_data.get('related_queries', [])}
            
            Consider these social risk indicators:
            1. Sudden spikes in search interest
            2. Association with scam-related keywords
            3. Influencer promotion patterns
            4. Community engagement authenticity
            5. Social media manipulation signs
            
            Assess:
            1. Social sentiment risk level
            2. Manipulation indicators
            3. Community legitimacy
            4. Hype cycle stage
            """
            
            response = await self.llm.ainvoke([
                SystemMessage(content=sentiment_prompt),
                HumanMessage(content="Analyze social sentiment for rug-pull indicators.")
            ])
            
            return {
                'agent_role': AgentRole.SOCIAL_SENTIMENT_ANALYST.value,
                'trends_data': trends_data,
                'sentiment_analysis': response.content,
                'social_risk_level': self._assess_social_risk(trends_data),
                'confidence': 0.7,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"Social sentiment analysis failed: {e}")
            return {
                'agent_role': AgentRole.SOCIAL_SENTIMENT_ANALYST.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }
    
    def _extract_token_name(self, contract_address: str) -> str:
        """Extract token name from contract address."""
        # This would query the blockchain for token name/symbol
        # For now, return a placeholder
        return f"Token_{contract_address[-6:]}"
    
    def _assess_social_risk(self, trends_data: Dict[str, Any]) -> str:
        """Assess social risk level from trends data."""
        related_queries = trends_data.get('related_queries', [])
        
        # Check for scam-related keywords
        scam_keywords = ['scam', 'rug pull', 'fraud', 'fake']
        scam_mentions = sum(1 for query in related_queries 
                           for keyword in scam_keywords 
                           if keyword in query.lower())
        
        if scam_mentions >= 2:
            return 'high'
        elif scam_mentions >= 1:
            return 'medium'
        else:
            return 'low'


class RiskCoordinatorAgent:
    """Risk coordinator agent that synthesizes all analyses."""

    def __init__(self, llm: ChatOllama):
        """Initialize risk coordinator agent."""
        self.llm = llm
        self.logger = get_logger(__name__)
        self.risk_scorer = None
    
    async def initialize(self):
        """Initialize the coordinator."""
        self.risk_scorer = EnhancedRiskScorer()
        await self.risk_scorer.initialize()
    
    async def coordinate(self, state: MultiAgentState) -> Dict[str, Any]:
        """Coordinate and synthesize all agent analyses."""
        try:
            self.logger.info(f"Risk coordination for: {state.contract_address}")
            
            # Extract analyses from all agents
            pattern_analysis = state.agent_analyses.get('pattern_analyst', {})
            market_analysis = state.agent_analyses.get('market_data_analyst', {})
            social_analysis = state.agent_analyses.get('social_sentiment_analyst', {})
            blockchain_analysis = state.agent_analyses.get('blockchain_monitor', {})
            
            # Synthesize with LLM
            coordination_prompt = f"""
            You are a senior risk assessment coordinator for a rug-pull detection system.

            Synthesize the following analyses for contract: {state.contract_address}

            Pattern Analysis:
            - Risk Level: {pattern_analysis.get('recommended_risk_level', 'unknown')}
            - Confidence: {pattern_analysis.get('confidence', 0)}
            - Risk Factors: {len(pattern_analysis.get('risk_factors', []))}

            Market Analysis:
            - Market Risk: {market_analysis.get('market_risk_level', 'unknown')}
            - Fear/Greed Index: {market_analysis.get('fear_greed_index', {}).get('value', 'N/A')}
            - Confidence: {market_analysis.get('confidence', 0)}

            Social Sentiment:
            - Social Risk: {social_analysis.get('social_risk_level', 'unknown')}
            - Confidence: {social_analysis.get('confidence', 0)}

            Blockchain Monitoring:
            - Blockchain Risk: {blockchain_analysis.get('blockchain_risk_level', 'unknown')}
            - Confidence: {blockchain_analysis.get('confidence', 0)}

            Provide a final risk assessment with:
            1. Overall risk level (minimal/low/medium/high/critical)
            2. Confidence score (0-1)
            3. Key risk factors
            4. Recommendation (safe/caution/avoid)
            5. Reasoning for the assessment
            """
            
            response = await self.llm.ainvoke([
                SystemMessage(content=coordination_prompt),
                HumanMessage(content="Provide final risk assessment.")
            ])
            
            # Calculate final risk score
            final_risk_score = self._calculate_final_risk_score(
                pattern_analysis, market_analysis, social_analysis, blockchain_analysis
            )
            
            # Determine final recommendation
            final_recommendation = self._determine_final_recommendation(final_risk_score)
            
            return {
                'agent_role': AgentRole.RISK_COORDINATOR.value,
                'final_risk_score': final_risk_score,
                'final_recommendation': final_recommendation,
                'coordination_analysis': response.content,
                'confidence': self._calculate_overall_confidence(
                    pattern_analysis, market_analysis, social_analysis, blockchain_analysis
                ),
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"Risk coordination failed: {e}")
            return {
                'agent_role': AgentRole.RISK_COORDINATOR.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }
    
    def _calculate_final_risk_score(self, pattern_analysis: Dict,
                                   market_analysis: Dict,
                                   social_analysis: Dict,
                                   blockchain_analysis: Dict) -> float:
        """Calculate final risk score from all analyses."""
        # Weight the different analyses
        weights = {
            'pattern': 0.4,     # Pattern analysis is most important
            'blockchain': 0.25, # Blockchain metrics are critical
            'market': 0.2,      # Market conditions are significant
            'social': 0.15      # Social sentiment is supplementary
        }
        
        # Convert risk levels to scores
        risk_level_scores = {
            'minimal': 0.1,
            'low': 0.3,
            'medium': 0.5,
            'high': 0.7,
            'critical': 0.9,
            'unknown': 0.5
        }
        
        pattern_score = risk_level_scores.get(
            pattern_analysis.get('recommended_risk_level', 'unknown'), 0.5
        )
        market_score = risk_level_scores.get(
            market_analysis.get('market_risk_level', 'unknown'), 0.5
        )
        social_score = risk_level_scores.get(
            social_analysis.get('social_risk_level', 'unknown'), 0.5
        )
        blockchain_score = risk_level_scores.get(
            blockchain_analysis.get('blockchain_risk_level', 'unknown'), 0.5
        )

        # Calculate weighted average
        final_score = (
            pattern_score * weights['pattern'] +
            blockchain_score * weights['blockchain'] +
            market_score * weights['market'] +
            social_score * weights['social']
        )
        
        return min(final_score, 1.0)

    def _determine_final_recommendation(self, risk_score: float) -> str:
        """Determine final recommendation from risk score."""
        if risk_score >= 0.8:
            return 'avoid'
        elif risk_score >= 0.6:
            return 'high_caution'
        elif risk_score >= 0.4:
            return 'caution'
        elif risk_score >= 0.2:
            return 'low_risk'
        else:
            return 'safe'

    def _calculate_overall_confidence(self, pattern_analysis: Dict,
                                     market_analysis: Dict,
                                     social_analysis: Dict,
                                     blockchain_analysis: Dict) -> float:
        """Calculate overall confidence from all analyses."""
        confidences = [
            pattern_analysis.get('confidence', 0),
            market_analysis.get('confidence', 0),
            social_analysis.get('confidence', 0),
            blockchain_analysis.get('confidence', 0)
        ]

        # Remove zero confidences (failed analyses)
        valid_confidences = [c for c in confidences if c > 0]

        if not valid_confidences:
            return 0.0

        return sum(valid_confidences) / len(valid_confidences)


class BlockchainMonitorAgent:
    """Blockchain monitoring agent for real-time on-chain analysis."""

    def __init__(self, llm: ChatOllama):
        """Initialize blockchain monitor agent."""
        self.llm = llm
        self.logger = get_logger(__name__)

    async def analyze(self, state: MultiAgentState) -> Dict[str, Any]:
        """Perform blockchain monitoring and analysis."""
        try:
            self.logger.info(f"Blockchain monitoring for: {state.contract_address}")

            # Fetch on-chain metrics
            blockchain_metrics = await self._fetch_blockchain_metrics(state.contract_address)

            # Analyze with LLM
            monitoring_prompt = f"""
            You are a blockchain security analyst specializing in on-chain behavior analysis.

            Analyze the blockchain metrics for contract: {state.contract_address}

            On-Chain Metrics:
            - Transaction Count (24h): {blockchain_metrics.get('tx_count_24h', 'N/A')}
            - Unique Addresses (24h): {blockchain_metrics.get('unique_addresses_24h', 'N/A')}
            - Holder Count: {blockchain_metrics.get('holder_count', 'N/A')}
            - Top Holder Percentage: {blockchain_metrics.get('top_holder_percentage', 'N/A')}%
            - Contract Age: {blockchain_metrics.get('contract_age_days', 'N/A')} days

            Consider these blockchain risk indicators:
            1. Unusual transaction patterns
            2. Concentration of holdings
            3. Recent deployment (< 30 days)
            4. Low holder diversity
            5. Suspicious transfer patterns

            Assess:
            1. Blockchain risk level
            2. On-chain manipulation indicators
            3. Holder distribution health
            4. Transaction pattern anomalies
            """

            response = await self.llm.ainvoke([
                SystemMessage(content=monitoring_prompt),
                HumanMessage(content="Analyze blockchain metrics for rug-pull indicators.")
            ])

            return {
                'agent_role': AgentRole.BLOCKCHAIN_MONITOR.value,
                'blockchain_metrics': blockchain_metrics,
                'monitoring_analysis': response.content,
                'blockchain_risk_level': self._assess_blockchain_risk(blockchain_metrics),
                'confidence': 0.8,
                'timestamp': time.time()
            }

        except Exception as e:
            self.logger.error(f"Blockchain monitoring failed: {e}")
            return {
                'agent_role': AgentRole.BLOCKCHAIN_MONITOR.value,
                'error': str(e),
                'confidence': 0.0,
                'timestamp': time.time()
            }

    async def _fetch_blockchain_metrics(self, contract_address: str) -> Dict[str, Any]:
        """Fetch blockchain metrics for the contract."""
        # This would integrate with blockchain APIs
        # For now, return mock data based on contract characteristics
        return {
            'tx_count_24h': 150,
            'unique_addresses_24h': 45,
            'holder_count': 1250,
            'top_holder_percentage': 15.5,
            'contract_age_days': 45,
            'liquidity_locked': True,
            'ownership_renounced': False
        }

    def _assess_blockchain_risk(self, metrics: Dict[str, Any]) -> str:
        """Assess blockchain risk level from metrics."""
        risk_score = 0

        # Check contract age
        age_days = metrics.get('contract_age_days', 0)
        if age_days < 7:
            risk_score += 3
        elif age_days < 30:
            risk_score += 2
        elif age_days < 90:
            risk_score += 1

        # Check holder concentration
        top_holder_pct = metrics.get('top_holder_percentage', 0)
        if top_holder_pct > 50:
            risk_score += 3
        elif top_holder_pct > 30:
            risk_score += 2
        elif top_holder_pct > 20:
            risk_score += 1

        # Check holder count
        holder_count = metrics.get('holder_count', 0)
        if holder_count < 100:
            risk_score += 2
        elif holder_count < 500:
            risk_score += 1

        # Determine risk level
        if risk_score >= 6:
            return 'critical'
        elif risk_score >= 4:
            return 'high'
        elif risk_score >= 2:
            return 'medium'
        elif risk_score >= 1:
            return 'low'
        else:
            return 'minimal'


# Factory function for creating the multi-agent system
async def create_multi_agent_rug_detector() -> StateGraph:
    """Create the multi-agent rug detector system."""

    # Initialize Ollama LLM
    ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
    llm = ChatOllama(
        model="llama3.1:8b",  # Default model, can be configured
        base_url=ollama_base_url,
        temperature=0.1,
        num_predict=2000
    )
    
    # Initialize agents
    pattern_agent = PatternAnalysisAgent(llm)
    market_agent = MarketDataAgent(llm)
    social_agent = SocialSentimentAgent(llm)
    blockchain_agent = BlockchainMonitorAgent(llm)
    coordinator_agent = RiskCoordinatorAgent(llm)

    await pattern_agent.initialize()
    await coordinator_agent.initialize()
    
    # Define node functions
    async def pattern_analysis_node(state: MultiAgentState) -> MultiAgentState:
        """Pattern analysis node."""
        analysis = await pattern_agent.analyze(state)
        state.agent_analyses['pattern_analyst'] = analysis
        return state
    
    async def market_analysis_node(state: MultiAgentState) -> MultiAgentState:
        """Market analysis node."""
        analysis = await market_agent.analyze(state)
        state.agent_analyses['market_data_analyst'] = analysis
        return state
    
    async def social_analysis_node(state: MultiAgentState) -> MultiAgentState:
        """Social sentiment analysis node."""
        analysis = await social_agent.analyze(state)
        state.agent_analyses['social_sentiment_analyst'] = analysis
        return state

    async def blockchain_analysis_node(state: MultiAgentState) -> MultiAgentState:
        """Blockchain monitoring analysis node."""
        analysis = await blockchain_agent.analyze(state)
        state.agent_analyses['blockchain_monitor'] = analysis
        return state

    async def coordination_node(state: MultiAgentState) -> MultiAgentState:
        """Risk coordination node."""
        coordination = await coordinator_agent.coordinate(state)
        state.final_risk_score = coordination.get('final_risk_score')
        state.final_recommendation = coordination.get('final_recommendation')
        state.confidence_level = coordination.get('confidence')
        state.analysis_complete = True
        return state
    
    # Build the graph
    builder = StateGraph(MultiAgentState)

    # Add nodes
    builder.add_node("pattern_analysis", pattern_analysis_node)
    builder.add_node("market_analysis", market_analysis_node)
    builder.add_node("social_analysis", social_analysis_node)
    builder.add_node("blockchain_analysis", blockchain_analysis_node)
    builder.add_node("risk_coordination", coordination_node)

    # Add edges for parallel execution
    builder.add_edge(START, "pattern_analysis")
    builder.add_edge(START, "market_analysis")
    builder.add_edge(START, "social_analysis")
    builder.add_edge(START, "blockchain_analysis")

    # Coordinate after all analyses complete
    builder.add_edge(["pattern_analysis", "market_analysis", "social_analysis", "blockchain_analysis"], "risk_coordination")
    builder.add_edge("risk_coordination", END)
    
    # Add memory
    memory = MemorySaver()
    
    return builder.compile(checkpointer=memory)


# Example usage function
async def analyze_contract_with_agents(contract_address: str, 
                                     contract_code: Optional[str] = None) -> Dict[str, Any]:
    """Analyze a contract using the multi-agent system."""
    
    # Create the multi-agent system
    agent_system = await create_multi_agent_rug_detector()
    
    # Initialize state
    initial_state = MultiAgentState(
        messages=[],
        contract_address=contract_address,
        contract_code=contract_code,
        agent_analyses={},
        market_data={},
        social_signals={},
        blockchain_data={},
        final_risk_score=None,
        final_recommendation=None,
        confidence_level=None,
        analysis_complete=False,
        timestamp=time.time()
    )
    
    # Run the analysis
    final_state = await agent_system.ainvoke(initial_state)
    
    return {
        'contract_address': contract_address,
        'final_risk_score': final_state.final_risk_score,
        'final_recommendation': final_state.final_recommendation,
        'confidence_level': final_state.confidence_level,
        'agent_analyses': final_state.agent_analyses,
        'analysis_complete': final_state.analysis_complete,
        'timestamp': final_state.timestamp
    }
