"""
Analysis Module - Smart Contract Security Analysis Components

This module contains all analysis engines for detecting rug-pulls and suspicious
blockchain activities including static analysis, dynamic behavior detection,
and forensic investigation capabilities.

Components:
- static_analyzer: Slither-based static code analysis
- dynamic_analyzer: Real-time behavior pattern detection
- forensics: Liquidity and market manipulation analysis
- risk_scorer: Comprehensive risk assessment algorithms
"""

# Type imports will be added as needed

__all__ = ["StaticAnalyzer", "DynamicAnalyzer", "ForensicsEngine", "RiskScorer"]

# Analysis module configuration
ANALYSIS_CONFIG = {
    "static_analysis": {
        "enabled": True,
        "slither_timeout": 300,  # 5 minutes
        "max_contract_size": 50000,  # lines of code
    },
    "dynamic_analysis": {
        "enabled": True,
        "monitoring_window": 3600,  # 1 hour
        "pattern_threshold": 0.8,
    },
    "forensics": {
        "enabled": True,
        "liquidity_threshold": 0.1,  # 10% threshold
        "market_cap_minimum": 10000,  # $10k minimum
    },
    "risk_scoring": {
        "enabled": True,
        "high_risk_threshold": 0.8,
        "medium_risk_threshold": 0.5,
    },
}

# Supported analysis types
ANALYSIS_TYPES = [
    "honeypot_detection",
    "liquidity_analysis",
    "ownership_analysis",
    "trading_restriction_analysis",
    "market_manipulation_detection",
]
