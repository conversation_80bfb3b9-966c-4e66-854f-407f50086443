"""
Analysis Package - Smart Contract Analysis Engine

This module provides comprehensive smart contract analysis capabilities
for detecting rug-pull patterns, honeypot mechanisms, and suspicious behaviors.

Features:
- Static analysis with Slither integration
- Dynamic behavior analysis
- Liquidity and market analysis
- Risk scoring and alert generation
- Pattern detection and classification
- Forensic investigation tools

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

from .static_analyzer import (
    StaticAnalyzer, AnalysisResult, VulnerabilityType,
    analyze_contract_static, get_static_analyzer
)
from .risk_scorer import (
    RiskScorer, RiskLevel, RiskScore, AlertType,
    calculate_risk_score, generate_alerts
)

__all__ = [
    # Static analysis
    'StaticAnalyzer', 'AnalysisResult', 'VulnerabilityType',
    'analyze_contract_static', 'get_static_analyzer',

    # Risk scoring
    'RiskScorer', 'RiskLevel', 'RiskScore', 'AlertType',
    'calculate_risk_score', 'generate_alerts'
]

# Analysis module configuration
ANALYSIS_CONFIG = {
    "static_analysis": {
        "enabled": True,
        "slither_timeout": 300,  # 5 minutes
        "max_contract_size": 50000,  # lines of code
    },
    "dynamic_analysis": {
        "enabled": True,
        "monitoring_window": 3600,  # 1 hour
        "pattern_threshold": 0.8,
    },
    "forensics": {
        "enabled": True,
        "liquidity_threshold": 0.1,  # 10% threshold
        "market_cap_minimum": 10000,  # $10k minimum
    },
    "risk_scoring": {
        "enabled": True,
        "high_risk_threshold": 0.8,
        "medium_risk_threshold": 0.5,
    },
}

# Supported analysis types
ANALYSIS_TYPES = [
    "honeypot_detection",
    "liquidity_analysis",
    "ownership_analysis",
    "trading_restriction_analysis",
    "market_manipulation_detection",
]
