"""
Static Analyzer - Smart Contract Static Analysis Engine

This module provides comprehensive static analysis capabilities for smart contracts
using Slither and custom pattern detection to identify rug-pull vulnerabilities.

Features:
- Slither integration for comprehensive static analysis
- Custom rug-pull pattern detection
- Honeypot mechanism identification
- Ownership and access control analysis
- Trading restriction detection
- Gas optimization and efficiency analysis

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import json
import subprocess
import tempfile
import time
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import get_config
from logging_config import get_logger, get_metrics_collector
from cache import get_cache_manager


class VulnerabilityType(str, Enum):
    """Types of vulnerabilities detected by static analysis."""
    HONEYPOT = "honeypot"
    OWNERSHIP_CENTRALIZATION = "ownership_centralization"
    TRADING_RESTRICTIONS = "trading_restrictions"
    LIQUIDITY_LOCK = "liquidity_lock"
    HIDDEN_MINT = "hidden_mint"
    PROXY_MANIPULATION = "proxy_manipulation"
    REENTRANCY = "reentrancy"
    INTEGER_OVERFLOW = "integer_overflow"
    ACCESS_CONTROL = "access_control"
    BACKDOOR = "backdoor"


class SeverityLevel(str, Enum):
    """Severity levels for vulnerabilities."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class Vulnerability:
    """Individual vulnerability finding."""
    type: VulnerabilityType
    severity: SeverityLevel
    title: str
    description: str
    location: str
    confidence: float
    impact: str
    recommendation: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class AnalysisResult:
    """Static analysis result."""
    contract_address: str
    contract_name: str
    analysis_timestamp: float
    vulnerabilities: List[Vulnerability]
    slither_output: Optional[Dict[str, Any]]
    gas_analysis: Optional[Dict[str, Any]]
    complexity_metrics: Optional[Dict[str, Any]]
    overall_risk_score: float
    analysis_duration: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'contract_address': self.contract_address,
            'contract_name': self.contract_name,
            'analysis_timestamp': self.analysis_timestamp,
            'vulnerabilities': [v.to_dict() for v in self.vulnerabilities],
            'slither_output': self.slither_output,
            'gas_analysis': self.gas_analysis,
            'complexity_metrics': self.complexity_metrics,
            'overall_risk_score': self.overall_risk_score,
            'analysis_duration': self.analysis_duration
        }


class StaticAnalyzer:
    """Smart contract static analysis engine."""
    
    def __init__(self):
        """Initialize static analyzer."""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Analysis configuration
        self.slither_timeout = 300  # 5 minutes
        self.max_contract_size = 50000  # lines of code
        
        # Pattern detection rules
        self.rug_pull_patterns = self._load_rug_pull_patterns()
        
        # Statistics
        self.stats = {
            'contracts_analyzed': 0,
            'vulnerabilities_found': 0,
            'analysis_failures': 0,
            'total_analysis_time': 0.0
        }
        
        # Cache manager
        self.cache_manager = None
    
    async def initialize(self) -> bool:
        """Initialize static analyzer."""
        try:
            self.logger.info("Initializing static analyzer")
            
            # Initialize cache manager
            self.cache_manager = await get_cache_manager()
            
            # Check Slither installation
            if not self._check_slither_installation():
                self.logger.warning("Slither not available, using fallback analysis")
            
            self.logger.info("Static analyzer initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize static analyzer: {e}")
            return False
    
    def _check_slither_installation(self) -> bool:
        """Check if Slither is installed and available."""
        try:
            result = subprocess.run(
                ['slither', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _load_rug_pull_patterns(self) -> Dict[str, List[str]]:
        """Load rug-pull detection patterns."""
        return {
            'honeypot_patterns': [
                'function transfer(',
                'function transferFrom(',
                'require(balanceOf[msg.sender]',
                'if (msg.sender != owner)',
                'onlyOwner modifier'
            ],
            'ownership_patterns': [
                'function renounceOwnership(',
                'function transferOwnership(',
                'modifier onlyOwner',
                'address public owner',
                'mapping(address => bool) public authorized'
            ],
            'trading_restriction_patterns': [
                'function setTradingEnabled(',
                'bool public tradingEnabled',
                'require(tradingEnabled',
                'function blacklist(',
                'mapping(address => bool) public blacklisted'
            ],
            'liquidity_patterns': [
                'function removeLiquidity(',
                'function emergencyWithdraw(',
                'function rugPull(',
                'function drainContract(',
                'selfdestruct('
            ]
        }
    
    async def analyze_contract(self, contract_address: str, 
                              source_code: Optional[str] = None) -> AnalysisResult:
        """Analyze smart contract for vulnerabilities.
        
        Args:
            contract_address: Contract address to analyze
            source_code: Optional source code (if not provided, will be fetched)
            
        Returns:
            Analysis result with vulnerabilities and metrics
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"Starting static analysis for contract: {contract_address}")
            
            # Check cache first
            cache_key = f"static_analysis_{contract_address}"
            if self.cache_manager:
                cached_result = await self.cache_manager.get("analysis", cache_key)
                if cached_result:
                    self.logger.debug(f"Using cached analysis for {contract_address}")
                    return AnalysisResult(**cached_result)
            
            # Get source code if not provided
            if not source_code:
                source_code = await self._fetch_source_code(contract_address)
            
            if not source_code:
                raise ValueError("Could not obtain source code for analysis")
            
            # Perform analysis
            vulnerabilities = []
            slither_output = None
            gas_analysis = None
            complexity_metrics = None
            
            # Run Slither analysis if available
            if self._check_slither_installation():
                slither_output = await self._run_slither_analysis(source_code)
                if slither_output:
                    vulnerabilities.extend(self._parse_slither_results(slither_output))
            
            # Run custom pattern analysis
            pattern_vulnerabilities = self._analyze_patterns(source_code)
            vulnerabilities.extend(pattern_vulnerabilities)
            
            # Calculate complexity metrics
            complexity_metrics = self._calculate_complexity_metrics(source_code)
            
            # Perform gas analysis
            gas_analysis = self._analyze_gas_efficiency(source_code)
            
            # Calculate overall risk score
            overall_risk_score = self._calculate_risk_score(vulnerabilities)
            
            # Create analysis result
            analysis_duration = time.time() - start_time
            
            result = AnalysisResult(
                contract_address=contract_address,
                contract_name=self._extract_contract_name(source_code),
                analysis_timestamp=time.time(),
                vulnerabilities=vulnerabilities,
                slither_output=slither_output,
                gas_analysis=gas_analysis,
                complexity_metrics=complexity_metrics,
                overall_risk_score=overall_risk_score,
                analysis_duration=analysis_duration
            )
            
            # Cache result
            if self.cache_manager:
                await self.cache_manager.set("analysis", cache_key, result.to_dict(), ttl=3600)
            
            # Update statistics
            self.stats['contracts_analyzed'] += 1
            self.stats['vulnerabilities_found'] += len(vulnerabilities)
            self.stats['total_analysis_time'] += analysis_duration
            
            self.logger.info(
                f"Static analysis completed for {contract_address}",
                vulnerabilities_found=len(vulnerabilities),
                risk_score=overall_risk_score,
                duration_seconds=analysis_duration
            )
            
            return result
            
        except Exception as e:
            self.stats['analysis_failures'] += 1
            self.logger.error(f"Static analysis failed for {contract_address}: {e}")
            
            # Return empty result on failure
            return AnalysisResult(
                contract_address=contract_address,
                contract_name="Unknown",
                analysis_timestamp=time.time(),
                vulnerabilities=[],
                slither_output=None,
                gas_analysis=None,
                complexity_metrics=None,
                overall_risk_score=0.0,
                analysis_duration=time.time() - start_time
            )
    
    async def _fetch_source_code(self, contract_address: str) -> Optional[str]:
        """Fetch contract source code from blockchain explorer."""
        try:
            # This would integrate with Etherscan API or similar
            # For now, return None to indicate source code not available
            self.logger.warning(f"Source code fetching not implemented for {contract_address}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to fetch source code for {contract_address}: {e}")
            return None
    
    async def _run_slither_analysis(self, source_code: str) -> Optional[Dict[str, Any]]:
        """Run Slither static analysis on source code."""
        try:
            # Create temporary file for source code
            with tempfile.NamedTemporaryFile(mode='w', suffix='.sol', delete=False) as f:
                f.write(source_code)
                temp_file = f.name
            
            try:
                # Run Slither
                result = subprocess.run(
                    ['slither', temp_file, '--json', '-'],
                    capture_output=True,
                    text=True,
                    timeout=self.slither_timeout
                )
                
                if result.returncode == 0 and result.stdout:
                    return json.loads(result.stdout)
                else:
                    self.logger.warning(f"Slither analysis failed: {result.stderr}")
                    return None
                    
            finally:
                # Clean up temporary file
                Path(temp_file).unlink(missing_ok=True)
                
        except subprocess.TimeoutExpired:
            self.logger.warning("Slither analysis timed out")
            return None
        except Exception as e:
            self.logger.error(f"Slither analysis error: {e}")
            return None
    
    def _parse_slither_results(self, slither_output: Dict[str, Any]) -> List[Vulnerability]:
        """Parse Slither output into vulnerability objects."""
        vulnerabilities = []
        
        try:
            results = slither_output.get('results', {})
            detectors = results.get('detectors', [])
            
            for detector in detectors:
                vulnerability = Vulnerability(
                    type=self._map_slither_to_vulnerability_type(detector.get('check', '')),
                    severity=self._map_slither_severity(detector.get('impact', 'Low')),
                    title=detector.get('check', 'Unknown'),
                    description=detector.get('description', ''),
                    location=self._extract_location(detector.get('elements', [])),
                    confidence=self._map_slither_confidence(detector.get('confidence', 'Low')),
                    impact=detector.get('impact', 'Unknown'),
                    recommendation="Review and fix the identified issue"
                )
                vulnerabilities.append(vulnerability)
                
        except Exception as e:
            self.logger.error(f"Error parsing Slither results: {e}")
        
        return vulnerabilities
    
    def _analyze_patterns(self, source_code: str) -> List[Vulnerability]:
        """Analyze source code for rug-pull patterns."""
        vulnerabilities = []
        
        try:
            lines = source_code.split('\n')
            
            for pattern_type, patterns in self.rug_pull_patterns.items():
                for pattern in patterns:
                    for line_num, line in enumerate(lines, 1):
                        if pattern.lower() in line.lower():
                            vulnerability = Vulnerability(
                                type=self._map_pattern_to_vulnerability_type(pattern_type),
                                severity=SeverityLevel.MEDIUM,
                                title=f"Potential {pattern_type.replace('_', ' ').title()}",
                                description=f"Pattern '{pattern}' detected in contract",
                                location=f"Line {line_num}",
                                confidence=0.7,
                                impact="Potential rug-pull mechanism",
                                recommendation="Review the identified pattern for malicious intent"
                            )
                            vulnerabilities.append(vulnerability)
                            
        except Exception as e:
            self.logger.error(f"Error analyzing patterns: {e}")
        
        return vulnerabilities
    
    def _calculate_complexity_metrics(self, source_code: str) -> Dict[str, Any]:
        """Calculate code complexity metrics."""
        try:
            lines = source_code.split('\n')
            
            return {
                'total_lines': len(lines),
                'code_lines': len([line for line in lines if line.strip() and not line.strip().startswith('//')]),
                'function_count': source_code.count('function '),
                'modifier_count': source_code.count('modifier '),
                'event_count': source_code.count('event '),
                'complexity_score': min(len(lines) / 1000, 1.0)  # Normalized complexity
            }
        except Exception as e:
            self.logger.error(f"Error calculating complexity metrics: {e}")
            return {}
    
    def _analyze_gas_efficiency(self, source_code: str) -> Dict[str, Any]:
        """Analyze gas efficiency patterns."""
        try:
            # Simple gas analysis based on patterns
            gas_issues = []
            
            if 'for (' in source_code:
                gas_issues.append("Potential gas optimization: loops detected")
            
            if 'string' in source_code:
                gas_issues.append("Potential gas optimization: string usage detected")
            
            return {
                'gas_issues': gas_issues,
                'optimization_score': max(0.0, 1.0 - len(gas_issues) * 0.1)
            }
        except Exception as e:
            self.logger.error(f"Error analyzing gas efficiency: {e}")
            return {}
    
    def _calculate_risk_score(self, vulnerabilities: List[Vulnerability]) -> float:
        """Calculate overall risk score based on vulnerabilities."""
        if not vulnerabilities:
            return 0.0
        
        severity_weights = {
            SeverityLevel.CRITICAL: 1.0,
            SeverityLevel.HIGH: 0.8,
            SeverityLevel.MEDIUM: 0.5,
            SeverityLevel.LOW: 0.2,
            SeverityLevel.INFO: 0.1
        }
        
        total_score = 0.0
        for vuln in vulnerabilities:
            weight = severity_weights.get(vuln.severity, 0.1)
            total_score += weight * vuln.confidence
        
        # Normalize to 0-1 range
        return min(total_score / len(vulnerabilities), 1.0)
    
    def _extract_contract_name(self, source_code: str) -> str:
        """Extract contract name from source code."""
        try:
            lines = source_code.split('\n')
            for line in lines:
                if 'contract ' in line and '{' in line:
                    # Extract contract name
                    parts = line.split('contract ')[1].split('{')[0].strip()
                    return parts.split()[0]
            return "Unknown"
        except Exception:
            return "Unknown"
    
    def _map_slither_to_vulnerability_type(self, check: str) -> VulnerabilityType:
        """Map Slither check to vulnerability type."""
        mapping = {
            'reentrancy': VulnerabilityType.REENTRANCY,
            'access-control': VulnerabilityType.ACCESS_CONTROL,
            'integer-overflow': VulnerabilityType.INTEGER_OVERFLOW,
        }
        return mapping.get(check, VulnerabilityType.ACCESS_CONTROL)
    
    def _map_slither_severity(self, impact: str) -> SeverityLevel:
        """Map Slither impact to severity level."""
        mapping = {
            'High': SeverityLevel.HIGH,
            'Medium': SeverityLevel.MEDIUM,
            'Low': SeverityLevel.LOW,
            'Informational': SeverityLevel.INFO
        }
        return mapping.get(impact, SeverityLevel.LOW)
    
    def _map_slither_confidence(self, confidence: str) -> float:
        """Map Slither confidence to numeric value."""
        mapping = {
            'High': 0.9,
            'Medium': 0.7,
            'Low': 0.5
        }
        return mapping.get(confidence, 0.5)
    
    def _map_pattern_to_vulnerability_type(self, pattern_type: str) -> VulnerabilityType:
        """Map pattern type to vulnerability type."""
        mapping = {
            'honeypot_patterns': VulnerabilityType.HONEYPOT,
            'ownership_patterns': VulnerabilityType.OWNERSHIP_CENTRALIZATION,
            'trading_restriction_patterns': VulnerabilityType.TRADING_RESTRICTIONS,
            'liquidity_patterns': VulnerabilityType.LIQUIDITY_LOCK
        }
        return mapping.get(pattern_type, VulnerabilityType.ACCESS_CONTROL)
    
    def _extract_location(self, elements: List[Dict[str, Any]]) -> str:
        """Extract location from Slither elements."""
        try:
            if elements:
                element = elements[0]
                source_mapping = element.get('source_mapping', {})
                lines = source_mapping.get('lines', [])
                if lines:
                    return f"Line {lines[0]}"
            return "Unknown location"
        except Exception:
            return "Unknown location"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get static analyzer statistics."""
        stats = self.stats.copy()
        
        if stats['contracts_analyzed'] > 0:
            stats['avg_analysis_time'] = stats['total_analysis_time'] / stats['contracts_analyzed']
            stats['avg_vulnerabilities_per_contract'] = stats['vulnerabilities_found'] / stats['contracts_analyzed']
        else:
            stats['avg_analysis_time'] = 0.0
            stats['avg_vulnerabilities_per_contract'] = 0.0
        
        return stats


# Global static analyzer instance
_static_analyzer: Optional[StaticAnalyzer] = None


async def get_static_analyzer() -> StaticAnalyzer:
    """Get global static analyzer instance."""
    global _static_analyzer
    
    if _static_analyzer is None:
        _static_analyzer = StaticAnalyzer()
        await _static_analyzer.initialize()
    
    return _static_analyzer


async def analyze_contract_static(contract_address: str, 
                                 source_code: Optional[str] = None) -> AnalysisResult:
    """Analyze contract using global static analyzer."""
    analyzer = await get_static_analyzer()
    return await analyzer.analyze_contract(contract_address, source_code)
