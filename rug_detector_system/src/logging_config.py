"""
Logging & Observability Configuration Module

This module provides comprehensive logging and observability infrastructure
including structured logging, metrics collection, and monitoring integration.

Features:
- Structured JSON logging with contextual information
- Prometheus metrics integration
- OpenTelemetry tracing support
- Performance monitoring and profiling
- Error tracking and alerting
- Production-ready observability stack

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import logging
import logging.config
import os
import sys
import time
from contextlib import contextmanager
from functools import wraps
from pathlib import Path
from typing import Any, Dict, Optional, Union

import structlog
from prometheus_client import Counter, Histogram, Gauge, start_http_server
from prometheus_client import CollectorRegistry, CONTENT_TYPE_LATEST, generate_latest

from config import get_config, MonitoringConfig


# Prometheus metrics registry
METRICS_REGISTRY = CollectorRegistry()

# Application metrics
REQUEST_COUNT = Counter(
    'rug_detector_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status'],
    registry=METRICS_REGISTRY
)

REQUEST_DURATION = Histogram(
    'rug_detector_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint'],
    registry=METRICS_REGISTRY
)

API_CALL_COUNT = Counter(
    'rug_detector_api_calls_total',
    'Total number of external API calls',
    ['api_name', 'status'],
    registry=METRICS_REGISTRY
)

API_CALL_DURATION = Histogram(
    'rug_detector_api_call_duration_seconds',
    'External API call duration in seconds',
    ['api_name'],
    registry=METRICS_REGISTRY
)

BLOCKCHAIN_BLOCKS_PROCESSED = Counter(
    'rug_detector_blocks_processed_total',
    'Total number of blockchain blocks processed',
    ['chain'],
    registry=METRICS_REGISTRY
)

ANALYSIS_RESULTS = Counter(
    'rug_detector_analysis_results_total',
    'Total number of analysis results',
    ['result_type', 'risk_level'],
    registry=METRICS_REGISTRY
)

SYSTEM_HEALTH = Gauge(
    'rug_detector_system_health',
    'System health status (1=healthy, 0=unhealthy)',
    registry=METRICS_REGISTRY
)

DATABASE_CONNECTIONS = Gauge(
    'rug_detector_database_connections',
    'Number of active database connections',
    registry=METRICS_REGISTRY
)

CACHE_HIT_RATE = Gauge(
    'rug_detector_cache_hit_rate',
    'Cache hit rate percentage',
    ['cache_type'],
    registry=METRICS_REGISTRY
)


class StructuredLogger:
    """Enhanced structured logger with observability features."""
    
    def __init__(self, name: str, config: Optional[MonitoringConfig] = None):
        """Initialize structured logger.
        
        Args:
            name: Logger name
            config: Monitoring configuration
        """
        self.name = name
        self.config = config or get_config().monitoring
        self.logger = structlog.get_logger(name)
        
        # Configure structured logging if not already done
        if not structlog.is_configured():
            self._configure_structlog()
    
    def _configure_structlog(self):
        """Configure structured logging with production settings."""
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
        ]
        
        # Add JSON processor for production
        if self.config.log_format == "json":
            processors.append(structlog.processors.JSONRenderer())
        else:
            processors.append(structlog.dev.ConsoleRenderer())
        
        structlog.configure(
            processors=processors,
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # Configure standard library logging
        logging.basicConfig(
            format="%(message)s",
            stream=sys.stdout,
            level=getattr(logging, self.config.log_level.value),
        )
    
    def info(self, message: str, **kwargs):
        """Log info message with context."""
        self.logger.info(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with context."""
        self.logger.error(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with context."""
        self.logger.warning(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with context."""
        self.logger.debug(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with context."""
        self.logger.critical(message, **kwargs)
    
    def bind(self, **kwargs):
        """Bind context to logger."""
        return StructuredLogger(
            self.name,
            self.config
        )._with_bound_logger(self.logger.bind(**kwargs))
    
    def _with_bound_logger(self, bound_logger):
        """Create new instance with bound logger."""
        instance = StructuredLogger(self.name, self.config)
        instance.logger = bound_logger
        return instance


class MetricsCollector:
    """Comprehensive metrics collection and monitoring."""
    
    def __init__(self, config: Optional[MonitoringConfig] = None):
        """Initialize metrics collector.
        
        Args:
            config: Monitoring configuration
        """
        self.config = config or get_config().monitoring
        self.logger = StructuredLogger(__name__)
        self._server_started = False
    
    def start_metrics_server(self):
        """Start Prometheus metrics server."""
        if not self._server_started:
            try:
                start_http_server(
                    self.config.prometheus_port,
                    registry=METRICS_REGISTRY
                )
                self._server_started = True
                self.logger.info(
                    "Metrics server started",
                    port=self.config.prometheus_port
                )
            except OSError as e:
                if "Address already in use" in str(e):
                    # Port already in use, assume server is already running
                    self._server_started = True
                    self.logger.warning(
                        "Metrics server port already in use, assuming already running",
                        port=self.config.prometheus_port
                    )
                else:
                    self.logger.error(
                        "Failed to start metrics server",
                        error=str(e),
                        port=self.config.prometheus_port
                    )
                    raise
            except Exception as e:
                self.logger.error(
                    "Failed to start metrics server",
                    error=str(e),
                    port=self.config.prometheus_port
                )
                raise
    
    def get_metrics(self) -> str:
        """Get current metrics in Prometheus format."""
        return generate_latest(METRICS_REGISTRY).decode('utf-8')
    
    @contextmanager
    def time_operation(self, operation_name: str, labels: Optional[Dict[str, str]] = None):
        """Context manager for timing operations."""
        labels = labels or {}
        start_time = time.time()
        
        try:
            yield
        finally:
            duration = time.time() - start_time
            
            # Record duration based on operation type
            if 'api_name' in labels:
                API_CALL_DURATION.labels(**labels).observe(duration)
            elif 'endpoint' in labels:
                REQUEST_DURATION.labels(**labels).observe(duration)
            
            self.logger.debug(
                "Operation completed",
                operation=operation_name,
                duration_seconds=duration,
                **labels
            )
    
    def record_request(self, method: str, endpoint: str, status: str):
        """Record HTTP request metrics."""
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status=status
        ).inc()
    
    def record_api_call(self, api_name: str, status: str, duration: float):
        """Record external API call metrics."""
        API_CALL_COUNT.labels(
            api_name=api_name,
            status=status
        ).inc()
        
        API_CALL_DURATION.labels(
            api_name=api_name
        ).observe(duration)
    
    def record_block_processed(self, chain: str):
        """Record blockchain block processing."""
        BLOCKCHAIN_BLOCKS_PROCESSED.labels(chain=chain).inc()
    
    def record_analysis_result(self, result_type: str, risk_level: str):
        """Record analysis result metrics."""
        ANALYSIS_RESULTS.labels(
            result_type=result_type,
            risk_level=risk_level
        ).inc()
    
    def update_system_health(self, is_healthy: bool):
        """Update system health status."""
        SYSTEM_HEALTH.set(1 if is_healthy else 0)
    
    def update_database_connections(self, count: int):
        """Update database connection count."""
        DATABASE_CONNECTIONS.set(count)
    
    def update_cache_hit_rate(self, cache_type: str, hit_rate: float):
        """Update cache hit rate."""
        CACHE_HIT_RATE.labels(cache_type=cache_type).set(hit_rate)


def performance_monitor(operation_name: str = None):
    """Decorator for monitoring function performance."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            name = operation_name or f"{func.__module__}.{func.__name__}"
            metrics = MetricsCollector()
            logger = StructuredLogger(func.__module__)
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                logger.debug(
                    "Function completed",
                    function=name,
                    duration_seconds=duration,
                    success=True
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                logger.error(
                    "Function failed",
                    function=name,
                    duration_seconds=duration,
                    error=str(e),
                    success=False
                )
                raise
        
        return wrapper
    return decorator


def get_logger(name: str) -> StructuredLogger:
    """Get configured structured logger instance."""
    return StructuredLogger(name)


def get_metrics_collector() -> MetricsCollector:
    """Get metrics collector instance."""
    return MetricsCollector()


# Global instances
logger = get_logger(__name__)
metrics = get_metrics_collector()


def initialize_observability():
    """Initialize complete observability stack."""
    try:
        # Start metrics server
        metrics.start_metrics_server()
        
        # Set initial system health
        metrics.update_system_health(True)
        
        logger.info(
            "Observability stack initialized",
            prometheus_port=get_config().monitoring.prometheus_port,
            log_level=get_config().monitoring.log_level.value,
            log_format=get_config().monitoring.log_format
        )
        
        return True
        
    except Exception as e:
        logger.error(
            "Failed to initialize observability stack",
            error=str(e)
        )
        return False
