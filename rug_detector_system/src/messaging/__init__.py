"""
Messaging Package Initialization

This module provides Redis-based message queue and event processing infrastructure
for the Rug Detector System with Kafka-compatible interfaces for future migration.

Features:
- Redis-based message queues with Kafka-like semantics
- Event streaming and processing
- Schema registry and validation
- Dead letter queues for failed messages
- Consumer groups and load balancing
- Message persistence and replay capabilities

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

from .queue_manager import (
    MessageQueueManager, get_queue_manager,
    QueueConfig, MessagePriority
)
from .event_processor import (
    EventProcessor, EventHandler, EventType,
    BlockchainEvent, AnalysisEvent, AlertEvent, BaseEvent
)
# Schema registry and consumer/producer modules to be implemented
# from .schema_registry import (
#     SchemaRegistry, MessageSchema, SchemaVersion,
#     validate_message, register_schema
# )
# from .consumer import (
#     MessageConsumer, ConsumerGroup, ConsumerConfig,
#     consume_messages, create_consumer_group
# )
# from .producer import (
#     MessageProducer, ProducerConfig,
#     publish_message, publish_event
# )

__all__ = [
    # Core queue management
    'MessageQueueManager', 'get_queue_manager',
    'QueueConfig', 'MessagePriority',
    
    # Event processing
    'EventProcessor', 'EventHandler', 'EventType',
    'BlockchainEvent', 'AnalysisEvent', 'AlertEvent', 'BaseEvent',
    
    # Schema management (to be implemented)
    # 'SchemaRegistry', 'MessageSchema', 'SchemaVersion',
    # 'validate_message', 'register_schema',

    # Consumer functionality (to be implemented)
    # 'MessageConsumer', 'ConsumerGroup', 'ConsumerConfig',
    # 'consume_messages', 'create_consumer_group',

    # Producer functionality (to be implemented)
    # 'MessageProducer', 'ProducerConfig',
    # 'publish_message', 'publish_event'
]
