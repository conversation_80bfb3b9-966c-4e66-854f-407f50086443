"""
Message Queue Manager - Redis-based Event Streaming

This module provides Redis-based message queue functionality with Kafka-like
semantics for event streaming and processing in the Rug Detector System.

Features:
- Redis Streams for message queuing
- Consumer groups and load balancing
- Message persistence and replay
- Dead letter queues for failed messages
- Priority queues and message routing
- Monitoring and metrics collection

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import json
import time
import uuid
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable
import sys
from pathlib import Path

import redis.asyncio as redis

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import get_config
from logging_config import get_logger, get_metrics_collector


class MessagePriority(str, Enum):
    """Message priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class QueueConfig:
    """Queue configuration settings."""
    name: str
    max_length: int = 10000
    retention_ms: int = ********  # 24 hours
    consumer_group: Optional[str] = None
    dead_letter_queue: Optional[str] = None
    priority_enabled: bool = False


@dataclass
class Message:
    """Message structure for queue operations."""
    id: str
    topic: str
    payload: Dict[str, Any]
    headers: Dict[str, str]
    timestamp: float
    priority: MessagePriority = MessagePriority.NORMAL
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary."""
        return cls(**data)


class MessageQueueManager:
    """Redis-based message queue manager with Kafka-like semantics."""
    
    def __init__(self, redis_url: Optional[str] = None):
        """Initialize message queue manager.
        
        Args:
            redis_url: Redis connection URL (defaults to config)
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        self.redis_url = redis_url or self.config.redis.url
        self.redis_client: Optional[redis.Redis] = None
        self._initialized = False
        
        # Queue configurations
        self.queues: Dict[str, QueueConfig] = {}
        
        # Message statistics
        self._stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'messages_failed': 0,
            'dead_letters': 0
        }
    
    async def initialize(self) -> bool:
        """Initialize Redis connection and queue infrastructure."""
        try:
            self.logger.info("Initializing message queue manager")
            
            # Create Redis client
            self.redis_client = redis.from_url(
                self.redis_url,
                max_connections=self.config.redis.max_connections,
                socket_timeout=self.config.redis.socket_timeout,
                socket_connect_timeout=self.config.redis.socket_connect_timeout,
                retry_on_timeout=True,
                decode_responses=False
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self._initialized = True
            
            self.logger.info("Message queue manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to initialize message queue manager",
                error=str(e)
            )
            return False
    
    async def create_queue(self, config: QueueConfig) -> bool:
        """Create a new message queue.
        
        Args:
            config: Queue configuration
            
        Returns:
            True if successful
        """
        if not self._initialized or not self.redis_client:
            return False
        
        try:
            # Store queue configuration
            self.queues[config.name] = config
            
            # Create consumer group if specified
            if config.consumer_group:
                try:
                    await self.redis_client.xgroup_create(
                        config.name,
                        config.consumer_group,
                        id='0',
                        mkstream=True
                    )
                except redis.ResponseError as e:
                    if "BUSYGROUP" not in str(e):
                        raise
            
            # Create dead letter queue if specified
            if config.dead_letter_queue:
                dlq_config = QueueConfig(
                    name=config.dead_letter_queue,
                    max_length=config.max_length // 10,  # Smaller DLQ
                    retention_ms=config.retention_ms * 7  # Longer retention
                )
                self.queues[config.dead_letter_queue] = dlq_config
            
            self.logger.info(
                "Queue created successfully",
                queue_name=config.name,
                consumer_group=config.consumer_group,
                dead_letter_queue=config.dead_letter_queue
            )
            
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to create queue",
                queue_name=config.name,
                error=str(e)
            )
            return False
    
    async def send_message(self, topic: str, payload: Dict[str, Any],
                          headers: Optional[Dict[str, str]] = None,
                          priority: MessagePriority = MessagePriority.NORMAL) -> Optional[str]:
        """Send message to queue.
        
        Args:
            topic: Queue topic/name
            payload: Message payload
            headers: Optional message headers
            priority: Message priority
            
        Returns:
            Message ID if successful, None otherwise
        """
        if not self._initialized or not self.redis_client:
            return None
        
        try:
            # Create message
            message = Message(
                id=str(uuid.uuid4()),
                topic=topic,
                payload=payload,
                headers=headers or {},
                timestamp=time.time(),
                priority=priority
            )
            
            # Serialize message
            message_data = {
                'id': message.id,
                'payload': json.dumps(message.payload),
                'headers': json.dumps(message.headers),
                'timestamp': str(message.timestamp),
                'priority': message.priority.value
            }
            
            # Send to appropriate queue based on priority
            queue_name = topic
            if priority != MessagePriority.NORMAL:
                queue_name = f"{topic}:{priority.value}"
            
            # Add to Redis stream
            stream_id = await self.redis_client.xadd(
                queue_name,
                message_data,
                maxlen=self.queues.get(topic, QueueConfig(topic)).max_length
            )
            
            self._stats['messages_sent'] += 1
            
            self.logger.debug(
                "Message sent successfully",
                topic=topic,
                message_id=message.id,
                stream_id=stream_id,
                priority=priority.value
            )
            
            return message.id
            
        except Exception as e:
            self._stats['messages_failed'] += 1
            self.logger.error(
                "Failed to send message",
                topic=topic,
                error=str(e)
            )
            return None
    
    async def receive_messages(self, topic: str, consumer_group: str,
                              consumer_name: str, count: int = 1,
                              block_ms: int = 1000) -> List[Message]:
        """Receive messages from queue.
        
        Args:
            topic: Queue topic/name
            consumer_group: Consumer group name
            consumer_name: Consumer name
            count: Number of messages to receive
            block_ms: Blocking timeout in milliseconds
            
        Returns:
            List of received messages
        """
        if not self._initialized or not self.redis_client:
            return []
        
        try:
            # Read from stream
            streams = await self.redis_client.xreadgroup(
                consumer_group,
                consumer_name,
                {topic: '>'},
                count=count,
                block=block_ms
            )
            
            messages = []
            for stream_name, stream_messages in streams:
                for message_id, fields in stream_messages:
                    try:
                        # Deserialize message
                        message = Message(
                            id=fields.get(b'id', b'').decode(),
                            topic=topic,
                            payload=json.loads(fields.get(b'payload', b'{}').decode()),
                            headers=json.loads(fields.get(b'headers', b'{}').decode()),
                            timestamp=float(fields.get(b'timestamp', b'0').decode()),
                            priority=MessagePriority(fields.get(b'priority', b'normal').decode())
                        )
                        
                        messages.append(message)
                        self._stats['messages_received'] += 1
                        
                    except Exception as e:
                        self.logger.error(
                            "Failed to deserialize message",
                            message_id=message_id,
                            error=str(e)
                        )
            
            return messages
            
        except Exception as e:
            self.logger.error(
                "Failed to receive messages",
                topic=topic,
                consumer_group=consumer_group,
                error=str(e)
            )
            return []
    
    async def acknowledge_message(self, topic: str, consumer_group: str,
                                 message_id: str) -> bool:
        """Acknowledge message processing.
        
        Args:
            topic: Queue topic/name
            consumer_group: Consumer group name
            message_id: Message ID to acknowledge
            
        Returns:
            True if successful
        """
        if not self._initialized or not self.redis_client:
            return False
        
        try:
            result = await self.redis_client.xack(topic, consumer_group, message_id)
            
            self.logger.debug(
                "Message acknowledged",
                topic=topic,
                consumer_group=consumer_group,
                message_id=message_id
            )
            
            return bool(result)
            
        except Exception as e:
            self.logger.error(
                "Failed to acknowledge message",
                topic=topic,
                message_id=message_id,
                error=str(e)
            )
            return False
    
    async def send_to_dead_letter_queue(self, message: Message, error: str) -> bool:
        """Send failed message to dead letter queue.
        
        Args:
            message: Failed message
            error: Error description
            
        Returns:
            True if successful
        """
        queue_config = self.queues.get(message.topic)
        if not queue_config or not queue_config.dead_letter_queue:
            return False
        
        # Add error information to message
        dlq_payload = message.payload.copy()
        dlq_payload['_error'] = error
        dlq_payload['_original_topic'] = message.topic
        dlq_payload['_failed_at'] = time.time()
        
        dlq_headers = message.headers.copy()
        dlq_headers['x-death-reason'] = error
        
        result = await self.send_message(
            queue_config.dead_letter_queue,
            dlq_payload,
            dlq_headers,
            MessagePriority.LOW
        )
        
        if result:
            self._stats['dead_letters'] += 1
            
        return result is not None
    
    async def get_queue_info(self, topic: str) -> Optional[Dict[str, Any]]:
        """Get queue information and statistics.
        
        Args:
            topic: Queue topic/name
            
        Returns:
            Queue information dictionary
        """
        if not self._initialized or not self.redis_client:
            return None
        
        try:
            # Get stream info
            info = await self.redis_client.xinfo_stream(topic)
            
            return {
                'name': topic,
                'length': info.get('length', 0),
                'first_entry': info.get('first-entry'),
                'last_entry': info.get('last-entry'),
                'groups': info.get('groups', 0),
                'config': asdict(self.queues.get(topic, QueueConfig(topic)))
            }
            
        except Exception as e:
            self.logger.error(
                "Failed to get queue info",
                topic=topic,
                error=str(e)
            )
            return None
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get message queue statistics."""
        stats = self._stats.copy()
        
        # Add queue-specific stats
        queue_stats = {}
        for topic in self.queues.keys():
            queue_info = await self.get_queue_info(topic)
            if queue_info:
                queue_stats[topic] = {
                    'length': queue_info['length'],
                    'groups': queue_info['groups']
                }
        
        stats['queues'] = queue_stats
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Check message queue health."""
        health_data = {
            "status": "unknown",
            "response_time_ms": None,
            "error": None
        }
        
        if not self._initialized or not self.redis_client:
            health_data["status"] = "not_initialized"
            health_data["error"] = "Message queue not initialized"
            return health_data
        
        start_time = time.time()
        
        try:
            # Test basic connectivity
            await self.redis_client.ping()
            
            # Test queue operations
            test_topic = "health_check"
            test_payload = {"test": True, "timestamp": time.time()}
            
            message_id = await self.send_message(test_topic, test_payload)
            
            if message_id:
                health_data["status"] = "healthy"
            else:
                health_data["status"] = "degraded"
                health_data["error"] = "Failed to send test message"
                
        except Exception as e:
            health_data["status"] = "unhealthy"
            health_data["error"] = f"Message queue error: {str(e)}"
        
        finally:
            health_data["response_time_ms"] = (time.time() - start_time) * 1000
        
        return health_data
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            self._initialized = False
            self.logger.info("Message queue manager closed")


# Global queue manager instance
_queue_manager: Optional[MessageQueueManager] = None


async def get_queue_manager() -> MessageQueueManager:
    """Get global queue manager instance."""
    global _queue_manager
    
    if _queue_manager is None:
        _queue_manager = MessageQueueManager()
        
        # Initialize if not already done
        if not _queue_manager._initialized:
            await _queue_manager.initialize()
    
    return _queue_manager
