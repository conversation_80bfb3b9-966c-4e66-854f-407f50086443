"""
Event Processor - Real-time Event Processing and Handling

This module provides event processing infrastructure for handling blockchain
events, analysis results, and system alerts in real-time.

Features:
- Event type definitions and schemas
- Event handlers and processors
- Async event processing pipeline
- Event filtering and routing
- Error handling and retry logic
- Event correlation and aggregation

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Union
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from logging_config import get_logger, get_metrics_collector
from .queue_manager import get_queue_manager, Message, MessagePriority


class EventType(str, Enum):
    """Event type definitions."""
    # Blockchain events
    BLOCK_MINED = "block_mined"
    TRANSACTION_DETECTED = "transaction_detected"
    CONTRACT_DEPLOYED = "contract_deployed"
    TOKEN_TRANSFER = "token_transfer"
    LIQUIDITY_ADDED = "liquidity_added"
    LIQUIDITY_REMOVED = "liquidity_removed"
    
    # Analysis events
    ANALYSIS_STARTED = "analysis_started"
    ANALYSIS_COMPLETED = "analysis_completed"
    RISK_SCORE_UPDATED = "risk_score_updated"
    PATTERN_DETECTED = "pattern_detected"
    
    # Alert events
    ALERT_TRIGGERED = "alert_triggered"
    ALERT_RESOLVED = "alert_resolved"
    THRESHOLD_EXCEEDED = "threshold_exceeded"
    
    # System events
    SYSTEM_STARTUP = "system_startup"
    SYSTEM_SHUTDOWN = "system_shutdown"
    ERROR_OCCURRED = "error_occurred"


@dataclass
class BaseEvent:
    """Base event structure."""
    event_id: str
    event_type: EventType
    timestamp: float
    source: str
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        data = asdict(self)
        data['event_type'] = self.event_type.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseEvent':
        """Create event from dictionary."""
        data['event_type'] = EventType(data['event_type'])
        return cls(**data)


@dataclass
class BlockchainEvent(BaseEvent):
    """Blockchain-specific event."""
    chain: str
    block_number: Optional[int] = None
    transaction_hash: Optional[str] = None
    contract_address: Optional[str] = None
    gas_used: Optional[int] = None
    gas_price: Optional[int] = None


@dataclass
class AnalysisEvent(BaseEvent):
    """Analysis-specific event."""
    contract_address: str
    analysis_type: str
    risk_level: str
    confidence_score: float
    analysis_results: Dict[str, Any]


@dataclass
class AlertEvent(BaseEvent):
    """Alert-specific event."""
    alert_type: str
    severity: str
    title: str
    description: str
    affected_entities: List[str]
    recommended_actions: List[str]


class EventHandler(ABC):
    """Abstract base class for event handlers."""
    
    def __init__(self, name: str, event_types: List[EventType]):
        """Initialize event handler.
        
        Args:
            name: Handler name
            event_types: List of event types this handler processes
        """
        self.name = name
        self.event_types = event_types
        self.logger = get_logger(f"{__name__}.{name}")
        self.metrics = get_metrics_collector()
        
        # Handler statistics
        self.stats = {
            'events_processed': 0,
            'events_failed': 0,
            'processing_time_total': 0.0
        }
    
    @abstractmethod
    async def handle_event(self, event: BaseEvent) -> bool:
        """Handle an event.
        
        Args:
            event: Event to handle
            
        Returns:
            True if handled successfully
        """
        pass
    
    def can_handle(self, event_type: EventType) -> bool:
        """Check if handler can process event type.
        
        Args:
            event_type: Event type to check
            
        Returns:
            True if handler can process this event type
        """
        return event_type in self.event_types
    
    async def process_event(self, event: BaseEvent) -> bool:
        """Process event with error handling and metrics.
        
        Args:
            event: Event to process
            
        Returns:
            True if processed successfully
        """
        if not self.can_handle(event.event_type):
            return False
        
        start_time = time.time()
        
        try:
            success = await self.handle_event(event)
            
            processing_time = time.time() - start_time
            self.stats['processing_time_total'] += processing_time
            
            if success:
                self.stats['events_processed'] += 1
                self.logger.debug(
                    "Event processed successfully",
                    event_id=event.event_id,
                    event_type=event.event_type.value,
                    processing_time_ms=processing_time * 1000
                )
            else:
                self.stats['events_failed'] += 1
                self.logger.warning(
                    "Event processing failed",
                    event_id=event.event_id,
                    event_type=event.event_type.value
                )
            
            return success
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.stats['processing_time_total'] += processing_time
            self.stats['events_failed'] += 1
            
            self.logger.error(
                "Event processing error",
                event_id=event.event_id,
                event_type=event.event_type.value,
                error=str(e)
            )
            
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get handler statistics."""
        stats = self.stats.copy()
        
        # Calculate averages
        total_events = stats['events_processed'] + stats['events_failed']
        if total_events > 0:
            stats['avg_processing_time_ms'] = (stats['processing_time_total'] / total_events) * 1000
            stats['success_rate'] = (stats['events_processed'] / total_events) * 100
        else:
            stats['avg_processing_time_ms'] = 0.0
            stats['success_rate'] = 0.0
        
        return stats


class EventProcessor:
    """Main event processor for handling and routing events."""
    
    def __init__(self):
        """Initialize event processor."""
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Event handlers registry
        self.handlers: Dict[str, EventHandler] = {}
        self.event_type_handlers: Dict[EventType, List[EventHandler]] = {}
        
        # Processing configuration
        self.max_concurrent_events = 100
        self.processing_timeout = 30.0
        
        # Statistics
        self.stats = {
            'events_received': 0,
            'events_processed': 0,
            'events_failed': 0,
            'handlers_registered': 0
        }
        
        # Processing semaphore
        self.processing_semaphore = asyncio.Semaphore(self.max_concurrent_events)
    
    def register_handler(self, handler: EventHandler) -> bool:
        """Register an event handler.
        
        Args:
            handler: Event handler to register
            
        Returns:
            True if registered successfully
        """
        try:
            # Store handler
            self.handlers[handler.name] = handler
            
            # Update event type mapping
            for event_type in handler.event_types:
                if event_type not in self.event_type_handlers:
                    self.event_type_handlers[event_type] = []
                self.event_type_handlers[event_type].append(handler)
            
            self.stats['handlers_registered'] += 1
            
            self.logger.info(
                "Event handler registered",
                handler_name=handler.name,
                event_types=[et.value for et in handler.event_types]
            )
            
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to register event handler",
                handler_name=handler.name,
                error=str(e)
            )
            return False
    
    def unregister_handler(self, handler_name: str) -> bool:
        """Unregister an event handler.
        
        Args:
            handler_name: Name of handler to unregister
            
        Returns:
            True if unregistered successfully
        """
        try:
            if handler_name not in self.handlers:
                return False
            
            handler = self.handlers[handler_name]
            
            # Remove from event type mapping
            for event_type in handler.event_types:
                if event_type in self.event_type_handlers:
                    self.event_type_handlers[event_type].remove(handler)
                    if not self.event_type_handlers[event_type]:
                        del self.event_type_handlers[event_type]
            
            # Remove handler
            del self.handlers[handler_name]
            self.stats['handlers_registered'] -= 1
            
            self.logger.info(
                "Event handler unregistered",
                handler_name=handler_name
            )
            
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to unregister event handler",
                handler_name=handler_name,
                error=str(e)
            )
            return False
    
    async def process_event(self, event: BaseEvent) -> bool:
        """Process a single event.
        
        Args:
            event: Event to process
            
        Returns:
            True if processed successfully by at least one handler
        """
        async with self.processing_semaphore:
            self.stats['events_received'] += 1
            
            # Get handlers for this event type
            handlers = self.event_type_handlers.get(event.event_type, [])
            
            if not handlers:
                self.logger.warning(
                    "No handlers registered for event type",
                    event_type=event.event_type.value,
                    event_id=event.event_id
                )
                return False
            
            # Process event with all applicable handlers
            tasks = []
            for handler in handlers:
                task = asyncio.create_task(
                    asyncio.wait_for(
                        handler.process_event(event),
                        timeout=self.processing_timeout
                    )
                )
                tasks.append(task)
            
            try:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Check results
                success_count = 0
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        self.logger.error(
                            "Handler processing timeout or error",
                            handler_name=handlers[i].name,
                            event_id=event.event_id,
                            error=str(result)
                        )
                    elif result:
                        success_count += 1
                
                if success_count > 0:
                    self.stats['events_processed'] += 1
                    return True
                else:
                    self.stats['events_failed'] += 1
                    return False
                    
            except Exception as e:
                self.stats['events_failed'] += 1
                self.logger.error(
                    "Event processing failed",
                    event_id=event.event_id,
                    error=str(e)
                )
                return False
    
    async def publish_event(self, event: BaseEvent, topic: str = "events") -> bool:
        """Publish event to message queue.
        
        Args:
            event: Event to publish
            topic: Queue topic
            
        Returns:
            True if published successfully
        """
        try:
            queue_manager = await get_queue_manager()
            
            # Determine message priority based on event type
            priority = MessagePriority.NORMAL
            if event.event_type in [EventType.ALERT_TRIGGERED, EventType.ERROR_OCCURRED]:
                priority = MessagePriority.HIGH
            elif event.event_type in [EventType.THRESHOLD_EXCEEDED]:
                priority = MessagePriority.CRITICAL
            
            message_id = await queue_manager.send_message(
                topic,
                event.to_dict(),
                headers={'event_type': event.event_type.value},
                priority=priority
            )
            
            return message_id is not None
            
        except Exception as e:
            self.logger.error(
                "Failed to publish event",
                event_id=event.event_id,
                error=str(e)
            )
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processor statistics."""
        stats = self.stats.copy()
        
        # Add handler statistics
        handler_stats = {}
        for name, handler in self.handlers.items():
            handler_stats[name] = handler.get_stats()
        
        stats['handlers'] = handler_stats
        
        # Calculate success rate
        total_events = stats['events_processed'] + stats['events_failed']
        if total_events > 0:
            stats['success_rate'] = (stats['events_processed'] / total_events) * 100
        else:
            stats['success_rate'] = 0.0
        
        return stats


# Global event processor instance
_event_processor: Optional[EventProcessor] = None


def get_event_processor() -> EventProcessor:
    """Get global event processor instance."""
    global _event_processor
    
    if _event_processor is None:
        _event_processor = EventProcessor()
    
    return _event_processor
