"""
Health Check & System Monitoring Module

This module provides comprehensive health checking capabilities for all
system components including database, Redis, external APIs, and blockchain
connectivity with detailed status reporting and alerting.

Features:
- Component-level health checks
- Dependency validation
- Performance monitoring
- Alert generation
- Status reporting
- Graceful degradation handling

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any

import httpx
import structlog

from config import get_config
from logging_config import get_logger, get_metrics_collector


class HealthStatus(str, Enum):
    """Health check status levels."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class ComponentHealth:
    """Health status of a system component."""
    name: str
    status: HealthStatus
    message: str
    response_time_ms: Optional[float] = None
    last_check: Optional[float] = None
    details: Optional[Dict[str, Any]] = None


@dataclass
class SystemHealth:
    """Overall system health status."""
    status: HealthStatus
    components: List[ComponentHealth]
    timestamp: float
    uptime_seconds: float
    version: str


class HealthChecker:
    """Comprehensive system health checker."""
    
    def __init__(self):
        """Initialize health checker."""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        self.start_time = time.time()
        
    async def check_all_components(self) -> SystemHealth:
        """Check health of all system components."""
        self.logger.info("Starting comprehensive health check")
        
        # Run all health checks concurrently
        checks = await asyncio.gather(
            self._check_database(),
            self._check_redis(),
            self._check_blockchain_connectivity(),
            self._check_external_apis(),
            self._check_system_resources(),
            return_exceptions=True
        )
        
        # Process results
        components = []
        for check in checks:
            if isinstance(check, ComponentHealth):
                components.append(check)
            elif isinstance(check, Exception):
                components.append(ComponentHealth(
                    name="unknown",
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {str(check)}"
                ))
        
        # Determine overall system status
        overall_status = self._calculate_overall_status(components)
        
        # Update metrics
        self.metrics.update_system_health(overall_status == HealthStatus.HEALTHY)
        
        system_health = SystemHealth(
            status=overall_status,
            components=components,
            timestamp=time.time(),
            uptime_seconds=time.time() - self.start_time,
            version=self.config.app_version
        )
        
        self.logger.info(
            "Health check completed",
            overall_status=overall_status.value,
            healthy_components=sum(1 for c in components if c.status == HealthStatus.HEALTHY),
            total_components=len(components)
        )
        
        return system_health
    
    async def _check_database(self) -> ComponentHealth:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            # Import here to avoid circular imports
            import asyncpg
            
            # Test connection
            conn = await asyncpg.connect(self.config.database.url)
            
            # Test query
            result = await conn.fetchval("SELECT 1")
            await conn.close()
            
            response_time = (time.time() - start_time) * 1000
            
            if result == 1:
                return ComponentHealth(
                    name="database",
                    status=HealthStatus.HEALTHY,
                    message="Database connection successful",
                    response_time_ms=response_time,
                    last_check=time.time()
                )
            else:
                return ComponentHealth(
                    name="database",
                    status=HealthStatus.UNHEALTHY,
                    message="Database query returned unexpected result",
                    response_time_ms=response_time,
                    last_check=time.time()
                )
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {str(e)}",
                response_time_ms=response_time,
                last_check=time.time()
            )
    
    async def _check_redis(self) -> ComponentHealth:
        """Check Redis connectivity and performance."""
        start_time = time.time()
        
        try:
            import redis.asyncio as redis
            
            # Create Redis client
            client = redis.from_url(self.config.redis.url)
            
            # Test connection
            await client.ping()
            
            # Test set/get
            test_key = "health_check_test"
            test_value = "test_value"
            
            await client.set(test_key, test_value, ex=60)
            result = await client.get(test_key)
            await client.delete(test_key)
            await client.close()
            
            response_time = (time.time() - start_time) * 1000
            
            if result and result.decode() == test_value:
                return ComponentHealth(
                    name="redis",
                    status=HealthStatus.HEALTHY,
                    message="Redis connection and operations successful",
                    response_time_ms=response_time,
                    last_check=time.time()
                )
            else:
                return ComponentHealth(
                    name="redis",
                    status=HealthStatus.DEGRADED,
                    message="Redis connection successful but operations failed",
                    response_time_ms=response_time,
                    last_check=time.time()
                )
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ComponentHealth(
                name="redis",
                status=HealthStatus.UNHEALTHY,
                message=f"Redis connection failed: {str(e)}",
                response_time_ms=response_time,
                last_check=time.time()
            )
    
    async def _check_blockchain_connectivity(self) -> ComponentHealth:
        """Check blockchain RPC connectivity."""
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Test Web3 provider
                payload = {
                    "jsonrpc": "2.0",
                    "method": "eth_blockNumber",
                    "params": [],
                    "id": 1
                }
                
                response = await client.post(
                    self.config.blockchain.web3_provider_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    data = response.json()
                    if 'result' in data:
                        block_number = int(data['result'], 16)
                        return ComponentHealth(
                            name="blockchain",
                            status=HealthStatus.HEALTHY,
                            message=f"Blockchain RPC connected, latest block: {block_number:,}",
                            response_time_ms=response_time,
                            last_check=time.time(),
                            details={"latest_block": block_number}
                        )
                    else:
                        return ComponentHealth(
                            name="blockchain",
                            status=HealthStatus.DEGRADED,
                            message="Blockchain RPC responded but with invalid data",
                            response_time_ms=response_time,
                            last_check=time.time()
                        )
                else:
                    return ComponentHealth(
                        name="blockchain",
                        status=HealthStatus.UNHEALTHY,
                        message=f"Blockchain RPC returned HTTP {response.status_code}",
                        response_time_ms=response_time,
                        last_check=time.time()
                    )
                    
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ComponentHealth(
                name="blockchain",
                status=HealthStatus.UNHEALTHY,
                message=f"Blockchain connectivity failed: {str(e)}",
                response_time_ms=response_time,
                last_check=time.time()
            )
    
    async def _check_external_apis(self) -> ComponentHealth:
        """Check external API connectivity."""
        start_time = time.time()
        
        try:
            from api_validator import APIKeyValidator
            
            validator = APIKeyValidator(self.config.api_keys, timeout=5)
            result = await validator.validate_all_apis()
            
            response_time = (time.time() - start_time) * 1000
            
            # Consider healthy if at least 50% of APIs are working
            success_rate = result.valid_apis / result.total_apis
            
            if success_rate >= 0.8:
                status = HealthStatus.HEALTHY
                message = f"External APIs healthy ({result.valid_apis}/{result.total_apis} working)"
            elif success_rate >= 0.5:
                status = HealthStatus.DEGRADED
                message = f"External APIs degraded ({result.valid_apis}/{result.total_apis} working)"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"External APIs unhealthy ({result.valid_apis}/{result.total_apis} working)"
            
            return ComponentHealth(
                name="external_apis",
                status=status,
                message=message,
                response_time_ms=response_time,
                last_check=time.time(),
                details={
                    "total_apis": result.total_apis,
                    "working_apis": result.valid_apis,
                    "success_rate": success_rate
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ComponentHealth(
                name="external_apis",
                status=HealthStatus.UNHEALTHY,
                message=f"External API check failed: {str(e)}",
                response_time_ms=response_time,
                last_check=time.time()
            )
    
    async def _check_system_resources(self) -> ComponentHealth:
        """Check system resource utilization."""
        start_time = time.time()
        
        try:
            import psutil
            
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            response_time = (time.time() - start_time) * 1000
            
            # Determine status based on resource usage
            if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
                status = HealthStatus.UNHEALTHY
                message = "System resources critically high"
            elif cpu_percent > 70 or memory.percent > 70 or disk.percent > 80:
                status = HealthStatus.DEGRADED
                message = "System resources elevated"
            else:
                status = HealthStatus.HEALTHY
                message = "System resources normal"
            
            return ComponentHealth(
                name="system_resources",
                status=status,
                message=message,
                response_time_ms=response_time,
                last_check=time.time(),
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent,
                    "memory_available_gb": memory.available / (1024**3)
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ComponentHealth(
                name="system_resources",
                status=HealthStatus.UNKNOWN,
                message=f"System resource check failed: {str(e)}",
                response_time_ms=response_time,
                last_check=time.time()
            )
    
    def _calculate_overall_status(self, components: List[ComponentHealth]) -> HealthStatus:
        """Calculate overall system status from component statuses."""
        if not components:
            return HealthStatus.UNKNOWN
        
        # Count status types
        healthy_count = sum(1 for c in components if c.status == HealthStatus.HEALTHY)
        degraded_count = sum(1 for c in components if c.status == HealthStatus.DEGRADED)
        unhealthy_count = sum(1 for c in components if c.status == HealthStatus.UNHEALTHY)
        
        total_count = len(components)
        
        # Determine overall status
        if unhealthy_count > 0:
            # Any unhealthy component makes system unhealthy
            return HealthStatus.UNHEALTHY
        elif degraded_count > 0:
            # Any degraded component makes system degraded
            return HealthStatus.DEGRADED
        elif healthy_count == total_count:
            # All components healthy
            return HealthStatus.HEALTHY
        else:
            # Unknown state
            return HealthStatus.UNKNOWN


async def get_system_health() -> SystemHealth:
    """Get current system health status."""
    checker = HealthChecker()
    return await checker.check_all_components()


def health_to_dict(health: SystemHealth) -> Dict[str, Any]:
    """Convert SystemHealth to dictionary for JSON serialization."""
    return {
        "status": health.status.value,
        "timestamp": health.timestamp,
        "uptime_seconds": health.uptime_seconds,
        "version": health.version,
        "components": [
            {
                "name": c.name,
                "status": c.status.value,
                "message": c.message,
                "response_time_ms": c.response_time_ms,
                "last_check": c.last_check,
                "details": c.details
            }
            for c in health.components
        ]
    }
