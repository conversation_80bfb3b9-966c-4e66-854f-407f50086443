"""
Database Models for Rug Detector System

This module defines the SQLAlchemy models for storing blockchain data,
smart contract analysis results, and detection metadata.

Models:
- SmartContract: Contract metadata and basic information
- Transaction: Blockchain transaction data
- AnalysisResult: Rug pull detection results
- RiskScore: Risk scoring and historical data
- LiquidityPool: DEX liquidity pool information
- Alert: System alerts and notifications

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional, Dict, Any

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, Numeric,
    ForeignKey, Index, JSON, BigInteger, Float
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func

Base = declarative_base()


class ChainType(str, Enum):
    """Supported blockchain networks."""
    ETHEREUM = "ethereum"
    POLYGON = "polygon"
    BSC = "bsc"
    ARBITRUM = "arbitrum"
    OPTIMISM = "optimism"


class RiskLevel(str, Enum):
    """Risk assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AnalysisStatus(str, Enum):
    """Analysis processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AlertSeverity(str, Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class SmartContract(Base):
    """Smart contract metadata and information."""
    
    __tablename__ = "smart_contracts"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Contract identification
    address = Column(String(42), nullable=False, index=True)
    chain = Column(String(20), nullable=False, index=True)
    name = Column(String(255))
    symbol = Column(String(50))
    
    # Contract metadata
    creation_block = Column(BigInteger)
    creation_tx_hash = Column(String(66))
    creator_address = Column(String(42), index=True)
    deployment_timestamp = Column(DateTime)
    
    # Contract code and verification
    bytecode = Column(Text)
    source_code = Column(Text)
    is_verified = Column(Boolean, default=False)
    compiler_version = Column(String(50))
    
    # Token information (if applicable)
    total_supply = Column(Numeric(precision=78, scale=0))
    decimals = Column(Integer)
    token_type = Column(String(20))  # ERC20, ERC721, etc.
    
    # Analysis metadata
    first_analyzed = Column(DateTime, default=func.now())
    last_analyzed = Column(DateTime, default=func.now())
    analysis_count = Column(Integer, default=0)
    
    # Risk assessment
    current_risk_level = Column(String(20), default=RiskLevel.LOW.value)
    is_flagged = Column(Boolean, default=False)
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Relationships
    transactions = relationship("Transaction", back_populates="contract")
    analysis_results = relationship("AnalysisResult", back_populates="contract")
    risk_scores = relationship("RiskScore", back_populates="contract")
    liquidity_pools = relationship("LiquidityPool", back_populates="contract")
    
    # Indexes
    __table_args__ = (
        Index('idx_contract_address_chain', 'address', 'chain'),
        Index('idx_contract_creator', 'creator_address'),
        Index('idx_contract_risk', 'current_risk_level', 'is_flagged'),
        Index('idx_contract_analyzed', 'last_analyzed'),
    )
    
    def __repr__(self):
        return f"<SmartContract(address='{self.address}', chain='{self.chain}', name='{self.name}')>"


class Transaction(Base):
    """Blockchain transaction data."""
    
    __tablename__ = "transactions"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Transaction identification
    hash = Column(String(66), nullable=False, unique=True, index=True)
    chain = Column(String(20), nullable=False, index=True)
    block_number = Column(BigInteger, nullable=False, index=True)
    block_hash = Column(String(66))
    transaction_index = Column(Integer)
    
    # Transaction details
    from_address = Column(String(42), nullable=False, index=True)
    to_address = Column(String(42), index=True)
    value = Column(Numeric(precision=78, scale=0))
    gas_limit = Column(BigInteger)
    gas_used = Column(BigInteger)
    gas_price = Column(Numeric(precision=78, scale=0))
    
    # Timing
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Contract interaction
    contract_id = Column(Integer, ForeignKey('smart_contracts.id'), index=True)
    input_data = Column(Text)
    function_signature = Column(String(10))
    
    # Analysis flags
    is_suspicious = Column(Boolean, default=False)
    risk_indicators = Column(JSON)
    
    # Additional data
    logs = Column(JSON)
    extra_metadata = Column(JSON)
    
    # Relationships
    contract = relationship("SmartContract", back_populates="transactions")
    analysis_results = relationship("AnalysisResult", back_populates="transaction")
    
    # Indexes
    __table_args__ = (
        Index('idx_tx_hash_chain', 'hash', 'chain'),
        Index('idx_tx_from_to', 'from_address', 'to_address'),
        Index('idx_tx_block', 'block_number', 'timestamp'),
        Index('idx_tx_contract', 'contract_id', 'timestamp'),
        Index('idx_tx_suspicious', 'is_suspicious', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<Transaction(hash='{self.hash}', chain='{self.chain}', block={self.block_number})>"


class AnalysisResult(Base):
    """Rug pull detection analysis results."""
    
    __tablename__ = "analysis_results"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Analysis identification
    analysis_id = Column(String(36), nullable=False, unique=True, index=True)
    analysis_type = Column(String(50), nullable=False)
    
    # Target references
    contract_id = Column(Integer, ForeignKey('smart_contracts.id'), nullable=False, index=True)
    transaction_id = Column(Integer, ForeignKey('transactions.id'), index=True)
    
    # Analysis metadata
    analyzer_version = Column(String(20))
    analysis_timestamp = Column(DateTime, default=func.now(), index=True)
    processing_time_ms = Column(Integer)
    
    # Results
    status = Column(String(20), default=AnalysisStatus.PENDING.value)
    risk_level = Column(String(20), nullable=False, index=True)
    confidence_score = Column(Float)  # 0.0 to 1.0
    
    # Detection results
    is_rug_pull = Column(Boolean, default=False)
    rug_pull_probability = Column(Float)
    detected_patterns = Column(JSON)
    risk_factors = Column(JSON)
    
    # Analysis details
    static_analysis = Column(JSON)
    dynamic_analysis = Column(JSON)
    liquidity_analysis = Column(JSON)
    ownership_analysis = Column(JSON)
    
    # Recommendations
    recommendations = Column(JSON)
    mitigation_steps = Column(JSON)
    
    # Additional data
    raw_results = Column(JSON)
    error_details = Column(Text)
    
    # Relationships
    contract = relationship("SmartContract", back_populates="analysis_results")
    transaction = relationship("Transaction", back_populates="analysis_results")
    alerts = relationship("Alert", back_populates="analysis_result")
    
    # Indexes
    __table_args__ = (
        Index('idx_analysis_contract_time', 'contract_id', 'analysis_timestamp'),
        Index('idx_analysis_risk', 'risk_level', 'is_rug_pull'),
        Index('idx_analysis_status', 'status', 'analysis_timestamp'),
        Index('idx_analysis_confidence', 'confidence_score', 'risk_level'),
    )
    
    def __repr__(self):
        return f"<AnalysisResult(id='{self.analysis_id}', risk='{self.risk_level}', rug_pull={self.is_rug_pull})>"


class RiskScore(Base):
    """Historical risk scoring data."""
    
    __tablename__ = "risk_scores"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Target reference
    contract_id = Column(Integer, ForeignKey('smart_contracts.id'), nullable=False, index=True)
    
    # Scoring metadata
    score_timestamp = Column(DateTime, default=func.now(), index=True)
    score_version = Column(String(20))
    
    # Risk scores (0-100 scale)
    overall_score = Column(Integer, nullable=False, index=True)
    liquidity_score = Column(Integer)
    ownership_score = Column(Integer)
    code_quality_score = Column(Integer)
    trading_pattern_score = Column(Integer)
    social_sentiment_score = Column(Integer)
    
    # Score components
    score_breakdown = Column(JSON)
    contributing_factors = Column(JSON)
    
    # Historical tracking
    previous_score = Column(Integer)
    score_change = Column(Integer)
    trend_direction = Column(String(10))  # up, down, stable
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Relationships
    contract = relationship("SmartContract", back_populates="risk_scores")
    
    # Indexes
    __table_args__ = (
        Index('idx_risk_contract_time', 'contract_id', 'score_timestamp'),
        Index('idx_risk_overall', 'overall_score', 'score_timestamp'),
        Index('idx_risk_trend', 'trend_direction', 'score_change'),
    )
    
    def __repr__(self):
        return f"<RiskScore(contract_id={self.contract_id}, score={self.overall_score}, trend='{self.trend_direction}')>"


class LiquidityPool(Base):
    """DEX liquidity pool information."""
    
    __tablename__ = "liquidity_pools"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Pool identification
    pool_address = Column(String(42), nullable=False, index=True)
    chain = Column(String(20), nullable=False, index=True)
    dex_name = Column(String(50), nullable=False)
    
    # Token references
    contract_id = Column(Integer, ForeignKey('smart_contracts.id'), nullable=False, index=True)
    paired_token_address = Column(String(42), index=True)
    paired_token_symbol = Column(String(50))
    
    # Pool metrics
    total_liquidity_usd = Column(Numeric(precision=20, scale=2))
    token_reserve = Column(Numeric(precision=78, scale=0))
    paired_token_reserve = Column(Numeric(precision=78, scale=0))
    
    # Trading metrics
    volume_24h_usd = Column(Numeric(precision=20, scale=2))
    price_usd = Column(Numeric(precision=20, scale=8))
    price_change_24h = Column(Float)
    
    # Liquidity analysis
    liquidity_locked = Column(Boolean, default=False)
    lock_duration_days = Column(Integer)
    lock_expiry = Column(DateTime)
    
    # Risk indicators
    is_honeypot = Column(Boolean, default=False)
    has_mint_function = Column(Boolean, default=False)
    ownership_renounced = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), index=True)
    
    # Additional data
    extra_metadata = Column(JSON)
    
    # Relationships
    contract = relationship("SmartContract", back_populates="liquidity_pools")
    
    # Indexes
    __table_args__ = (
        Index('idx_pool_address_chain', 'pool_address', 'chain'),
        Index('idx_pool_contract', 'contract_id', 'dex_name'),
        Index('idx_pool_liquidity', 'total_liquidity_usd', 'last_updated'),
        Index('idx_pool_risk', 'is_honeypot', 'has_mint_function'),
    )
    
    def __repr__(self):
        return f"<LiquidityPool(address='{self.pool_address}', dex='{self.dex_name}', liquidity=${self.total_liquidity_usd})>"


class Alert(Base):
    """System alerts and notifications."""
    
    __tablename__ = "alerts"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Alert identification
    alert_id = Column(String(36), nullable=False, unique=True, index=True)
    alert_type = Column(String(50), nullable=False, index=True)
    severity = Column(String(20), nullable=False, index=True)
    
    # Target references
    contract_id = Column(Integer, ForeignKey('smart_contracts.id'), index=True)
    analysis_result_id = Column(Integer, ForeignKey('analysis_results.id'), index=True)
    
    # Alert content
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    description = Column(Text)
    
    # Alert metadata
    created_at = Column(DateTime, default=func.now(), index=True)
    triggered_by = Column(String(100))
    trigger_conditions = Column(JSON)
    
    # Status tracking
    is_active = Column(Boolean, default=True, index=True)
    is_acknowledged = Column(Boolean, default=False)
    acknowledged_by = Column(String(100))
    acknowledged_at = Column(DateTime)
    
    # Resolution
    is_resolved = Column(Boolean, default=False)
    resolved_by = Column(String(100))
    resolved_at = Column(DateTime)
    resolution_notes = Column(Text)
    
    # Additional data
    extra_metadata = Column(JSON)
    
    # Relationships
    contract = relationship("SmartContract")
    analysis_result = relationship("AnalysisResult", back_populates="alerts")
    
    # Indexes
    __table_args__ = (
        Index('idx_alert_severity_active', 'severity', 'is_active'),
        Index('idx_alert_type_created', 'alert_type', 'created_at'),
        Index('idx_alert_contract', 'contract_id', 'is_active'),
        Index('idx_alert_status', 'is_active', 'is_resolved'),
    )
    
    def __repr__(self):
        return f"<Alert(id='{self.alert_id}', type='{self.alert_type}', severity='{self.severity}')>"
