"""
Database Connection and Session Management

This module provides comprehensive database connectivity, session management,
and connection pooling for the Rug Detector System using SQLAlchemy.

Features:
- Connection pooling with configurable parameters
- Session lifecycle management
- Health checking and monitoring
- Transaction management
- Database initialization utilities
- Connection retry logic

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import time
from contextlib import contextmanager
from typing import Optional, Generator, Dict, Any

from sqlalchemy import create_engine, text, event
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError, DisconnectionError
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import get_config
from logging_config import get_logger, get_metrics_collector
from .models import Base


class DatabaseManager:
    """Comprehensive database connection and session manager."""
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize database manager.
        
        Args:
            database_url: Database connection URL (defaults to config)
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        self.database_url = database_url or self.config.database.url
        self.engine: Optional[Engine] = None
        self.session_factory: Optional[sessionmaker] = None
        self._initialized = False
        
    def initialize(self) -> bool:
        """Initialize database connection and session factory."""
        try:
            self.logger.info("Initializing database connection")
            
            # Create engine with connection pooling
            self.engine = create_engine(
                self.database_url,
                poolclass=QueuePool,
                pool_size=self.config.database.pool_size,
                max_overflow=self.config.database.max_overflow,
                pool_timeout=self.config.database.pool_timeout,
                pool_recycle=self.config.database.pool_recycle,
                pool_pre_ping=True,  # Validate connections before use
                echo=self.config.debug,  # Log SQL in debug mode
                future=True  # Use SQLAlchemy 2.0 style
            )
            
            # Set up event listeners for monitoring
            self._setup_event_listeners()
            
            # Create session factory
            self.session_factory = sessionmaker(
                bind=self.engine,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False
            )
            
            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self._initialized = True
            
            self.logger.info(
                "Database connection initialized successfully",
                pool_size=self.config.database.pool_size,
                max_overflow=self.config.database.max_overflow
            )
            
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to initialize database connection",
                error=str(e),
                database_url=self.database_url.split('@')[-1] if '@' in self.database_url else "unknown"
            )
            return False
    
    def _setup_event_listeners(self):
        """Set up SQLAlchemy event listeners for monitoring."""
        
        @event.listens_for(self.engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            """Handle new database connections."""
            self.logger.debug("New database connection established")
            self.metrics.update_database_connections(
                self.engine.pool.checkedout()
            )
        
        @event.listens_for(self.engine, "checkout")
        def on_checkout(dbapi_connection, connection_record, connection_proxy):
            """Handle connection checkout from pool."""
            self.metrics.update_database_connections(
                self.engine.pool.checkedout()
            )
        
        @event.listens_for(self.engine, "checkin")
        def on_checkin(dbapi_connection, connection_record):
            """Handle connection checkin to pool."""
            self.metrics.update_database_connections(
                self.engine.pool.checkedout()
            )
        
        @event.listens_for(self.engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Track query execution start time."""
            context._query_start_time = time.time()
        
        @event.listens_for(self.engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Track query execution time."""
            if hasattr(context, '_query_start_time'):
                duration = time.time() - context._query_start_time
                self.logger.debug(
                    "Database query executed",
                    duration_seconds=duration,
                    statement_type=statement.split()[0].upper() if statement else "UNKNOWN"
                )
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get database session with automatic cleanup.
        
        Yields:
            SQLAlchemy session
            
        Raises:
            RuntimeError: If database not initialized
        """
        if not self._initialized or not self.session_factory:
            raise RuntimeError("Database not initialized. Call initialize() first.")
        
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(
                "Database session error, rolling back",
                error=str(e)
            )
            raise
        finally:
            session.close()
    
    def create_tables(self) -> bool:
        """Create all database tables."""
        try:
            if not self._initialized or not self.engine:
                raise RuntimeError("Database not initialized")
            
            self.logger.info("Creating database tables")
            Base.metadata.create_all(self.engine)
            
            self.logger.info("Database tables created successfully")
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to create database tables",
                error=str(e)
            )
            return False
    
    def drop_tables(self) -> bool:
        """Drop all database tables."""
        try:
            if not self._initialized or not self.engine:
                raise RuntimeError("Database not initialized")
            
            self.logger.warning("Dropping all database tables")
            Base.metadata.drop_all(self.engine)
            
            self.logger.warning("Database tables dropped successfully")
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to drop database tables",
                error=str(e)
            )
            return False
    
    async def check_health(self) -> Dict[str, Any]:
        """Check database health and connectivity.
        
        Returns:
            Health status dictionary
        """
        health_data = {
            "status": "unknown",
            "response_time_ms": None,
            "pool_status": {},
            "error": None
        }
        
        if not self._initialized or not self.engine:
            health_data["status"] = "not_initialized"
            health_data["error"] = "Database not initialized"
            return health_data
        
        start_time = time.time()
        
        try:
            # Test basic connectivity
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1 as health_check"))
                row = result.fetchone()
                
                if row and row[0] == 1:
                    health_data["status"] = "healthy"
                else:
                    health_data["status"] = "unhealthy"
                    health_data["error"] = "Unexpected query result"
            
            # Get pool status
            pool = self.engine.pool
            health_data["pool_status"] = {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
            
        except DisconnectionError as e:
            health_data["status"] = "disconnected"
            health_data["error"] = f"Database disconnected: {str(e)}"
            
        except SQLAlchemyError as e:
            health_data["status"] = "error"
            health_data["error"] = f"Database error: {str(e)}"
            
        except Exception as e:
            health_data["status"] = "error"
            health_data["error"] = f"Unexpected error: {str(e)}"
        
        finally:
            health_data["response_time_ms"] = (time.time() - start_time) * 1000
        
        return health_data
    
    def close(self):
        """Close database connections and cleanup."""
        if self.engine:
            self.logger.info("Closing database connections")
            self.engine.dispose()
            self.engine = None
            self.session_factory = None
            self._initialized = False


# Global database manager instance
_database_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get global database manager instance."""
    global _database_manager
    
    if _database_manager is None:
        _database_manager = DatabaseManager()
        
        # Initialize if not already done
        if not _database_manager._initialized:
            _database_manager.initialize()
    
    return _database_manager


@contextmanager
def get_session() -> Generator[Session, None, None]:
    """Get database session using global manager.
    
    Yields:
        SQLAlchemy session
    """
    manager = get_database_manager()
    with manager.get_session() as session:
        yield session


def create_tables() -> bool:
    """Create all database tables using global manager."""
    manager = get_database_manager()
    return manager.create_tables()


def drop_tables() -> bool:
    """Drop all database tables using global manager."""
    manager = get_database_manager()
    return manager.drop_tables()


async def check_database_health() -> Dict[str, Any]:
    """Check database health using global manager."""
    manager = get_database_manager()
    return await manager.check_health()


def initialize_database() -> bool:
    """Initialize database connection and create tables if needed."""
    logger = get_logger(__name__)
    
    try:
        # Get database manager (will initialize connection)
        manager = get_database_manager()
        
        if not manager._initialized:
            logger.error("Failed to initialize database manager")
            return False
        
        # Create tables
        if not manager.create_tables():
            logger.error("Failed to create database tables")
            return False
        
        logger.info("Database initialization completed successfully")
        return True
        
    except Exception as e:
        logger.error(
            "Database initialization failed",
            error=str(e)
        )
        return False
