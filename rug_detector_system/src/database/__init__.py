"""
Database Package Initialization

This module provides database connectivity, session management, and
initialization utilities for the Rug Detector System.

Features:
- SQLAlchemy engine and session management
- Connection pooling and optimization
- Database initialization and migration support
- Health checking and monitoring
- Transaction management utilities

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

from .models import (
    Base, SmartContract, Transaction, AnalysisResult, 
    RiskScore, LiquidityPool, Alert,
    ChainType, RiskLevel, AnalysisStatus, AlertSeverity
)
from .connection import (
    DatabaseManager, get_database_manager, get_session,
    create_tables, drop_tables, check_database_health
)

__all__ = [
    # Models
    'Base', 'SmartContract', 'Transaction', 'AnalysisResult',
    'RiskScore', 'LiquidityPool', 'Alert',
    
    # Enums
    'ChainType', 'RiskLevel', 'AnalysisStatus', 'AlertSeverity',
    
    # Connection management
    'DatabaseManager', 'get_database_manager', 'get_session',
    'create_tables', 'drop_tables', 'check_database_health'
]
