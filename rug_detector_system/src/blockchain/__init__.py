"""
Blockchain Module - Web3 Integration and Real-time Data Processing

This module handles all blockchain interactions including Web3 connectivity,
real-time event streaming, multi-chain support, and external API integrations.

Components:
- listener: WebSocket-based real-time blockchain event monitoring
- web3_client: Robust Web3 provider integration with failover
- multi_chain: Unified interface for multiple blockchain networks
- api_client: External API integration (Etherscan, CoinGecko, Dune)
"""

# Type imports will be added as needed

__all__ = ["BlockchainListener", "Web3Client", "MultiChainManager", "ExternalAPIClient"]

# Blockchain module configuration
BLOCKCHAIN_CONFIG = {
    "web3": {
        "connection_timeout": 30,
        "request_timeout": 15,
        "max_retries": 3,
        "retry_delay": 1.0,
    },
    "websocket": {
        "ping_interval": 20,
        "ping_timeout": 10,
        "close_timeout": 10,
        "max_reconnect_attempts": 10,
    },
    "rate_limiting": {
        "requests_per_second": 10,
        "burst_limit": 50,
        "backoff_factor": 2.0,
    },
    "caching": {
        "enabled": True,
        "ttl_seconds": 300,  # 5 minutes
        "max_cache_size": 10000,
    },
}

# Supported blockchain networks with their configurations
NETWORK_CONFIGS = {
    "ethereum": {
        "chain_id": 1,
        "name": "Ethereum Mainnet",
        "currency": "ETH",
        "block_time": 12,
        "confirmation_blocks": 12,
    },
    "polygon": {
        "chain_id": 137,
        "name": "Polygon Mainnet",
        "currency": "MATIC",
        "block_time": 2,
        "confirmation_blocks": 20,
    },
    "bsc": {
        "chain_id": 56,
        "name": "Binance Smart Chain",
        "currency": "BNB",
        "block_time": 3,
        "confirmation_blocks": 15,
    },
}
