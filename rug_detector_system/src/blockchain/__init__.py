"""
Blockchain Package - Web3 Integration and Real-time Data Processing

This module provides comprehensive blockchain connectivity and Web3 integration
for the Rug Detector System with multi-chain support and robust error handling.

Features:
- Web3 provider management with connection pooling
- Multi-chain support (Ethereum, Polygon, BSC)
- Automatic failover and retry mechanisms
- Rate limiting and request optimization
- Real-time event monitoring
- Transaction and contract analysis

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

from .web3_manager import (
    Web3Manager, ChainConfig, ConnectionPool, ChainType,
    get_web3_manager, create_web3_connection
)

__all__ = [
    # Web3 management
    'Web3Manager', 'ChainConfig', 'ConnectionPool', 'ChainType',
    'get_web3_manager', 'create_web3_connection'
]

# Blockchain module configuration
BLOCKCHAIN_CONFIG = {
    "web3": {
        "connection_timeout": 30,
        "request_timeout": 15,
        "max_retries": 3,
        "retry_delay": 1.0,
    },
    "websocket": {
        "ping_interval": 20,
        "ping_timeout": 10,
        "close_timeout": 10,
        "max_reconnect_attempts": 10,
    },
    "rate_limiting": {
        "requests_per_second": 10,
        "burst_limit": 50,
        "backoff_factor": 2.0,
    },
    "caching": {
        "enabled": True,
        "ttl_seconds": 300,  # 5 minutes
        "max_cache_size": 10000,
    },
}

# Supported blockchain networks with their configurations
NETWORK_CONFIGS = {
    "ethereum": {
        "chain_id": 1,
        "name": "Ethereum Mainnet",
        "currency": "ETH",
        "block_time": 12,
        "confirmation_blocks": 12,
    },
    "polygon": {
        "chain_id": 137,
        "name": "Polygon Mainnet",
        "currency": "MATIC",
        "block_time": 2,
        "confirmation_blocks": 20,
    },
    "bsc": {
        "chain_id": 56,
        "name": "Binance Smart Chain",
        "currency": "BNB",
        "block_time": 3,
        "confirmation_blocks": 15,
    },
}
