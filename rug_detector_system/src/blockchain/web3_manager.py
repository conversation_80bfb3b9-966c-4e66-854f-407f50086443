"""
Web3 Manager - Robust Blockchain Connectivity

This module provides comprehensive Web3 provider management with connection
pooling, automatic failover, and multi-chain support for the Rug Detector System.

Features:
- Connection pooling and load balancing
- Automatic failover and retry mechanisms
- Rate limiting and request optimization
- Multi-chain support with unified interface
- Health monitoring and metrics collection
- Caching for frequently accessed data

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import time
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Any, Dict, List, Optional, Union
import sys
from pathlib import Path

from web3 import Web3
from web3.providers import HTTPProvider, WebSocketProvider
try:
    from web3.middleware import geth_poa_middleware
except ImportError:
    # Fallback for newer web3.py versions
    try:
        from web3.middleware.geth_poa import geth_poa_middleware
    except ImportError:
        geth_poa_middleware = None
import httpx

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import get_config
from logging_config import get_logger, get_metrics_collector
from cache import get_cache_manager


class ChainType(str, Enum):
    """Supported blockchain networks."""
    ETHEREUM = "ethereum"
    POLYGON = "polygon"
    BSC = "bsc"
    ARBITRUM = "arbitrum"
    OPTIMISM = "optimism"


@dataclass
class ChainConfig:
    """Blockchain network configuration."""
    chain_id: int
    name: str
    currency: str
    rpc_urls: List[str]
    ws_urls: List[str]
    block_time: int
    confirmation_blocks: int
    is_poa: bool = False  # Proof of Authority chains need special middleware
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class ConnectionPool:
    """Web3 connection pool for load balancing."""
    chain: ChainType
    connections: List[Web3]
    current_index: int = 0
    failed_connections: List[int] = None
    
    def __post_init__(self):
        if self.failed_connections is None:
            self.failed_connections = []
    
    def get_next_connection(self) -> Optional[Web3]:
        """Get next available connection with round-robin."""
        if not self.connections:
            return None
        
        # Filter out failed connections
        available_indices = [i for i in range(len(self.connections)) 
                           if i not in self.failed_connections]
        
        if not available_indices:
            # Reset failed connections if all are failed
            self.failed_connections = []
            available_indices = list(range(len(self.connections)))
        
        if not available_indices:
            return None
        
        # Round-robin through available connections
        self.current_index = (self.current_index + 1) % len(available_indices)
        connection_index = available_indices[self.current_index]
        
        return self.connections[connection_index]
    
    def mark_connection_failed(self, connection: Web3):
        """Mark a connection as failed."""
        try:
            index = self.connections.index(connection)
            if index not in self.failed_connections:
                self.failed_connections.append(index)
        except ValueError:
            pass
    
    def reset_failed_connections(self):
        """Reset failed connection tracking."""
        self.failed_connections = []


class Web3Manager:
    """Comprehensive Web3 provider manager with multi-chain support."""
    
    def __init__(self):
        """Initialize Web3 manager."""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Connection pools for each chain
        self.connection_pools: Dict[ChainType, ConnectionPool] = {}
        
        # Chain configurations
        self.chain_configs: Dict[ChainType, ChainConfig] = {}
        
        # Request statistics
        self.stats = {
            'requests_total': 0,
            'requests_failed': 0,
            'connections_created': 0,
            'failovers': 0
        }
        
        # Initialize default chain configurations
        self._initialize_chain_configs()
        
        # Cache manager for frequently accessed data
        self.cache_manager = None
    
    async def initialize(self) -> bool:
        """Initialize Web3 connections and cache manager."""
        try:
            self.logger.info("Initializing Web3 manager")
            
            # Initialize cache manager
            self.cache_manager = await get_cache_manager()
            
            # Create connection pools for configured chains
            for chain_type in [ChainType.ETHEREUM, ChainType.POLYGON, ChainType.BSC]:
                success = await self._create_connection_pool(chain_type)
                if not success:
                    self.logger.warning(f"Failed to create connection pool for {chain_type.value}")
            
            self.logger.info(
                "Web3 manager initialized",
                active_chains=len(self.connection_pools),
                total_connections=sum(len(pool.connections) for pool in self.connection_pools.values())
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Web3 manager: {e}")
            return False
    
    def _initialize_chain_configs(self):
        """Initialize default chain configurations."""
        self.chain_configs = {
            ChainType.ETHEREUM: ChainConfig(
                chain_id=1,
                name="Ethereum Mainnet",
                currency="ETH",
                rpc_urls=[
                    self.config.blockchain.ethereum_rpc_url,
                    "https://eth-mainnet.alchemyapi.io/v2/demo",
                    "https://mainnet.infura.io/v3/********************************"
                ],
                ws_urls=[
                    "wss://eth-mainnet.alchemyapi.io/v2/demo",
                    "wss://mainnet.infura.io/ws/v3/********************************"
                ],
                block_time=12,
                confirmation_blocks=12,
                is_poa=False
            ),
            ChainType.POLYGON: ChainConfig(
                chain_id=137,
                name="Polygon Mainnet",
                currency="MATIC",
                rpc_urls=[
                    self.config.blockchain.polygon_rpc_url,
                    "https://polygon-rpc.com",
                    "https://rpc-mainnet.matic.network"
                ],
                ws_urls=[
                    "wss://polygon-rpc.com",
                    "wss://rpc-mainnet.matic.network"
                ],
                block_time=2,
                confirmation_blocks=20,
                is_poa=True
            ),
            ChainType.BSC: ChainConfig(
                chain_id=56,
                name="Binance Smart Chain",
                currency="BNB",
                rpc_urls=[
                    self.config.blockchain.bsc_rpc_url,
                    "https://bsc-dataseed.binance.org",
                    "https://bsc-dataseed1.defibit.io"
                ],
                ws_urls=[
                    "wss://bsc-ws-node.nariox.org:443"
                ],
                block_time=3,
                confirmation_blocks=15,
                is_poa=True
            )
        }
    
    async def _create_connection_pool(self, chain_type: ChainType) -> bool:
        """Create connection pool for a specific chain."""
        try:
            chain_config = self.chain_configs.get(chain_type)
            if not chain_config:
                return False
            
            connections = []
            
            # Create HTTP connections
            for rpc_url in chain_config.rpc_urls:
                try:
                    # Test connection first
                    if await self._test_rpc_connection(rpc_url):
                        provider = HTTPProvider(
                            rpc_url,
                            request_kwargs={'timeout': 30}
                        )
                        w3 = Web3(provider)
                        
                        # Add PoA middleware if needed
                        if chain_config.is_poa and geth_poa_middleware:
                            w3.middleware_onion.inject(geth_poa_middleware, layer=0)
                        
                        connections.append(w3)
                        self.stats['connections_created'] += 1
                        
                        self.logger.debug(f"Created connection for {chain_type.value}: {rpc_url}")
                        
                except Exception as e:
                    self.logger.warning(f"Failed to create connection for {rpc_url}: {e}")
            
            if connections:
                self.connection_pools[chain_type] = ConnectionPool(
                    chain=chain_type,
                    connections=connections
                )
                
                self.logger.info(
                    f"Connection pool created for {chain_type.value}",
                    connection_count=len(connections)
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to create connection pool for {chain_type.value}: {e}")
            return False
    
    async def _test_rpc_connection(self, rpc_url: str) -> bool:
        """Test RPC connection health."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    "jsonrpc": "2.0",
                    "method": "eth_blockNumber",
                    "params": [],
                    "id": 1
                }
                
                response = await client.post(
                    rpc_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                return response.status_code == 200 and 'result' in response.json()
                
        except Exception:
            return False
    
    def get_web3_connection(self, chain: ChainType) -> Optional[Web3]:
        """Get Web3 connection for specified chain.
        
        Args:
            chain: Blockchain network
            
        Returns:
            Web3 connection or None if unavailable
        """
        pool = self.connection_pools.get(chain)
        if not pool:
            self.logger.warning(f"No connection pool for {chain.value}")
            return None
        
        connection = pool.get_next_connection()
        if not connection:
            self.logger.error(f"No available connections for {chain.value}")
            return None
        
        # Test connection health
        try:
            connection.eth.block_number
            return connection
        except Exception as e:
            self.logger.warning(f"Connection health check failed for {chain.value}: {e}")
            pool.mark_connection_failed(connection)
            self.stats['failovers'] += 1
            
            # Try next connection
            return self.get_web3_connection(chain)
    
    async def get_latest_block(self, chain: ChainType) -> Optional[Dict[str, Any]]:
        """Get latest block for specified chain.
        
        Args:
            chain: Blockchain network
            
        Returns:
            Block information or None
        """
        # Check cache first
        cache_key = f"latest_block_{chain.value}"
        if self.cache_manager:
            cached_block = await self.cache_manager.get("blockchain", cache_key)
            if cached_block:
                return cached_block
        
        w3 = self.get_web3_connection(chain)
        if not w3:
            return None
        
        try:
            self.stats['requests_total'] += 1
            
            block = w3.eth.get_block('latest')
            block_data = {
                'number': block.number,
                'hash': block.hash.hex(),
                'timestamp': block.timestamp,
                'gas_used': block.gasUsed,
                'gas_limit': block.gasLimit,
                'transaction_count': len(block.transactions)
            }
            
            # Cache for 30 seconds
            if self.cache_manager:
                await self.cache_manager.set("blockchain", cache_key, block_data, ttl=30)
            
            return block_data
            
        except Exception as e:
            self.stats['requests_failed'] += 1
            self.logger.error(f"Failed to get latest block for {chain.value}: {e}")
            return None
    
    async def get_transaction(self, chain: ChainType, tx_hash: str) -> Optional[Dict[str, Any]]:
        """Get transaction details.
        
        Args:
            chain: Blockchain network
            tx_hash: Transaction hash
            
        Returns:
            Transaction information or None
        """
        # Check cache first
        cache_key = f"transaction_{tx_hash}"
        if self.cache_manager:
            cached_tx = await self.cache_manager.get("blockchain", cache_key)
            if cached_tx:
                return cached_tx
        
        w3 = self.get_web3_connection(chain)
        if not w3:
            return None
        
        try:
            self.stats['requests_total'] += 1
            
            tx = w3.eth.get_transaction(tx_hash)
            receipt = w3.eth.get_transaction_receipt(tx_hash)
            
            tx_data = {
                'hash': tx.hash.hex(),
                'block_number': tx.blockNumber,
                'from': tx['from'],
                'to': tx.to,
                'value': str(tx.value),
                'gas': tx.gas,
                'gas_price': str(tx.gasPrice),
                'gas_used': receipt.gasUsed,
                'status': receipt.status,
                'logs': [log for log in receipt.logs]
            }
            
            # Cache transactions permanently (they don't change)
            if self.cache_manager:
                await self.cache_manager.set("blockchain", cache_key, tx_data, ttl=86400)
            
            return tx_data
            
        except Exception as e:
            self.stats['requests_failed'] += 1
            self.logger.error(f"Failed to get transaction {tx_hash} for {chain.value}: {e}")
            return None
    
    def get_chain_config(self, chain: ChainType) -> Optional[ChainConfig]:
        """Get chain configuration.
        
        Args:
            chain: Blockchain network
            
        Returns:
            Chain configuration or None
        """
        return self.chain_configs.get(chain)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Web3 manager statistics."""
        stats = self.stats.copy()
        
        # Add connection pool stats
        pool_stats = {}
        for chain, pool in self.connection_pools.items():
            pool_stats[chain.value] = {
                'total_connections': len(pool.connections),
                'failed_connections': len(pool.failed_connections),
                'current_index': pool.current_index
            }
        
        stats['connection_pools'] = pool_stats
        
        # Calculate success rate
        if stats['requests_total'] > 0:
            stats['success_rate'] = ((stats['requests_total'] - stats['requests_failed']) / 
                                   stats['requests_total']) * 100
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Web3 manager health."""
        health_data = {
            "status": "unknown",
            "chains": {},
            "total_connections": 0,
            "healthy_connections": 0
        }
        
        try:
            for chain_type, pool in self.connection_pools.items():
                chain_health = {
                    "status": "unknown",
                    "connections": len(pool.connections),
                    "failed_connections": len(pool.failed_connections),
                    "latest_block": None
                }
                
                # Test getting latest block
                try:
                    latest_block = await self.get_latest_block(chain_type)
                    if latest_block:
                        chain_health["status"] = "healthy"
                        chain_health["latest_block"] = latest_block["number"]
                        health_data["healthy_connections"] += len(pool.connections) - len(pool.failed_connections)
                    else:
                        chain_health["status"] = "unhealthy"
                except Exception:
                    chain_health["status"] = "unhealthy"
                
                health_data["chains"][chain_type.value] = chain_health
                health_data["total_connections"] += len(pool.connections)
            
            # Determine overall status
            if health_data["healthy_connections"] > 0:
                if health_data["healthy_connections"] == health_data["total_connections"]:
                    health_data["status"] = "healthy"
                else:
                    health_data["status"] = "degraded"
            else:
                health_data["status"] = "unhealthy"
            
        except Exception as e:
            health_data["status"] = "error"
            health_data["error"] = str(e)
        
        return health_data


# Global Web3 manager instance
_web3_manager: Optional[Web3Manager] = None


async def get_web3_manager() -> Web3Manager:
    """Get global Web3 manager instance."""
    global _web3_manager
    
    if _web3_manager is None:
        _web3_manager = Web3Manager()
        await _web3_manager.initialize()
    
    return _web3_manager


def create_web3_connection(rpc_url: str, is_poa: bool = False) -> Web3:
    """Create a single Web3 connection.
    
    Args:
        rpc_url: RPC endpoint URL
        is_poa: Whether chain uses Proof of Authority
        
    Returns:
        Web3 connection
    """
    provider = HTTPProvider(rpc_url, request_kwargs={'timeout': 30})
    w3 = Web3(provider)
    
    if is_poa and geth_poa_middleware:
        w3.middleware_onion.inject(geth_poa_middleware, layer=0)
    
    return w3
