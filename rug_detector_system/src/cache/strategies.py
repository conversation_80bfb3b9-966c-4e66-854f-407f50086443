"""
Cache Strategies - Intelligent Cache Invalidation and Management

This module provides various caching strategies for different use cases
including TTL-based, LRU, event-based, and hybrid strategies.

Features:
- Time-based expiration (TTL)
- Least Recently Used (LRU) eviction
- Event-driven invalidation
- Hybrid strategies combining multiple approaches
- Performance-optimized cache warming
- Intelligent prefetching

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Callable
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from logging_config import get_logger


class CacheEventType(str, Enum):
    """Cache event types for event-based invalidation."""
    CONTRACT_UPDATED = "contract_updated"
    TRANSACTION_ADDED = "transaction_added"
    ANALYSIS_COMPLETED = "analysis_completed"
    RISK_SCORE_CHANGED = "risk_score_changed"
    LIQUIDITY_CHANGED = "liquidity_changed"
    ALERT_TRIGGERED = "alert_triggered"


@dataclass
class CacheEvent:
    """Cache event for event-based invalidation."""
    event_type: CacheEventType
    entity_id: str
    timestamp: float
    metadata: Optional[Dict[str, Any]] = None


class CacheStrategy(ABC):
    """Abstract base class for cache strategies."""
    
    def __init__(self, name: str):
        """Initialize cache strategy.
        
        Args:
            name: Strategy name
        """
        self.name = name
        self.logger = get_logger(f"{__name__}.{name}")
        
    @abstractmethod
    async def should_cache(self, namespace: str, key: str, value: Any) -> bool:
        """Determine if value should be cached.
        
        Args:
            namespace: Cache namespace
            key: Cache key
            value: Value to potentially cache
            
        Returns:
            True if value should be cached
        """
        pass
    
    @abstractmethod
    async def get_ttl(self, namespace: str, key: str, value: Any) -> Optional[int]:
        """Get TTL for cache entry.
        
        Args:
            namespace: Cache namespace
            key: Cache key
            value: Value being cached
            
        Returns:
            TTL in seconds or None for no expiration
        """
        pass
    
    @abstractmethod
    async def should_invalidate(self, namespace: str, key: str, event: CacheEvent) -> bool:
        """Determine if cache entry should be invalidated based on event.
        
        Args:
            namespace: Cache namespace
            key: Cache key
            event: Cache event
            
        Returns:
            True if entry should be invalidated
        """
        pass


class TTLStrategy(CacheStrategy):
    """Time-to-live based cache strategy."""
    
    def __init__(self, default_ttl: int = 3600, ttl_map: Optional[Dict[str, int]] = None):
        """Initialize TTL strategy.
        
        Args:
            default_ttl: Default TTL in seconds
            ttl_map: Namespace-specific TTL mapping
        """
        super().__init__("TTL")
        self.default_ttl = default_ttl
        self.ttl_map = ttl_map or {}
        
    async def should_cache(self, namespace: str, key: str, value: Any) -> bool:
        """Always cache with TTL strategy."""
        return True
    
    async def get_ttl(self, namespace: str, key: str, value: Any) -> Optional[int]:
        """Get TTL based on namespace configuration."""
        return self.ttl_map.get(namespace, self.default_ttl)
    
    async def should_invalidate(self, namespace: str, key: str, event: CacheEvent) -> bool:
        """TTL strategy relies on time-based expiration."""
        return False


class LRUStrategy(CacheStrategy):
    """Least Recently Used cache strategy."""
    
    def __init__(self, max_size: int = 10000, size_map: Optional[Dict[str, int]] = None):
        """Initialize LRU strategy.
        
        Args:
            max_size: Maximum number of entries per namespace
            size_map: Namespace-specific size limits
        """
        super().__init__("LRU")
        self.max_size = max_size
        self.size_map = size_map or {}
        self.access_times: Dict[str, float] = {}
        
    async def should_cache(self, namespace: str, key: str, value: Any) -> bool:
        """Check if we should cache based on size limits."""
        max_entries = self.size_map.get(namespace, self.max_size)
        current_entries = len([k for k in self.access_times.keys() if k.startswith(f"{namespace}:")])
        
        return current_entries < max_entries
    
    async def get_ttl(self, namespace: str, key: str, value: Any) -> Optional[int]:
        """LRU strategy doesn't use TTL."""
        return None
    
    async def should_invalidate(self, namespace: str, key: str, event: CacheEvent) -> bool:
        """LRU strategy relies on access-based eviction."""
        return False
    
    def record_access(self, namespace: str, key: str):
        """Record access time for LRU tracking."""
        cache_key = f"{namespace}:{key}"
        self.access_times[cache_key] = time.time()
    
    def get_lru_keys(self, namespace: str, count: int) -> List[str]:
        """Get least recently used keys for eviction."""
        namespace_keys = {k: v for k, v in self.access_times.items() 
                         if k.startswith(f"{namespace}:")}
        
        # Sort by access time (oldest first)
        sorted_keys = sorted(namespace_keys.items(), key=lambda x: x[1])
        
        return [k for k, _ in sorted_keys[:count]]


class EventBasedStrategy(CacheStrategy):
    """Event-driven cache invalidation strategy."""
    
    def __init__(self, invalidation_rules: Optional[Dict[CacheEventType, List[str]]] = None):
        """Initialize event-based strategy.
        
        Args:
            invalidation_rules: Mapping of event types to namespace patterns to invalidate
        """
        super().__init__("EventBased")
        self.invalidation_rules = invalidation_rules or self._default_rules()
        
    def _default_rules(self) -> Dict[CacheEventType, List[str]]:
        """Default invalidation rules."""
        return {
            CacheEventType.CONTRACT_UPDATED: ["contracts:*", "analysis:*"],
            CacheEventType.TRANSACTION_ADDED: ["transactions:*", "blocks:*"],
            CacheEventType.ANALYSIS_COMPLETED: ["analysis:*", "risk_scores:*"],
            CacheEventType.RISK_SCORE_CHANGED: ["risk_scores:*", "alerts:*"],
            CacheEventType.LIQUIDITY_CHANGED: ["liquidity:*", "prices:*"],
            CacheEventType.ALERT_TRIGGERED: ["alerts:*"]
        }
    
    async def should_cache(self, namespace: str, key: str, value: Any) -> bool:
        """Always cache with event-based strategy."""
        return True
    
    async def get_ttl(self, namespace: str, key: str, value: Any) -> Optional[int]:
        """Event-based strategy uses long TTL as fallback."""
        return 86400  # 24 hours
    
    async def should_invalidate(self, namespace: str, key: str, event: CacheEvent) -> bool:
        """Check if entry should be invalidated based on event."""
        patterns = self.invalidation_rules.get(event.event_type, [])
        
        for pattern in patterns:
            if self._matches_pattern(f"{namespace}:{key}", pattern):
                self.logger.debug(
                    "Cache invalidation triggered by event",
                    namespace=namespace,
                    key=key,
                    event_type=event.event_type.value,
                    pattern=pattern
                )
                return True
        
        return False
    
    def _matches_pattern(self, cache_key: str, pattern: str) -> bool:
        """Check if cache key matches invalidation pattern."""
        if pattern.endswith("*"):
            return cache_key.startswith(pattern[:-1])
        else:
            return cache_key == pattern


class HybridStrategy(CacheStrategy):
    """Hybrid strategy combining multiple cache strategies."""
    
    def __init__(self, strategies: List[CacheStrategy], 
                 primary_strategy: Optional[str] = None):
        """Initialize hybrid strategy.
        
        Args:
            strategies: List of strategies to combine
            primary_strategy: Name of primary strategy for TTL decisions
        """
        super().__init__("Hybrid")
        self.strategies = {s.name: s for s in strategies}
        self.primary_strategy = primary_strategy or (strategies[0].name if strategies else None)
        
    async def should_cache(self, namespace: str, key: str, value: Any) -> bool:
        """Check if any strategy allows caching."""
        for strategy in self.strategies.values():
            if await strategy.should_cache(namespace, key, value):
                return True
        return False
    
    async def get_ttl(self, namespace: str, key: str, value: Any) -> Optional[int]:
        """Get TTL from primary strategy."""
        if self.primary_strategy and self.primary_strategy in self.strategies:
            return await self.strategies[self.primary_strategy].get_ttl(namespace, key, value)
        
        # Fallback to first strategy
        if self.strategies:
            first_strategy = next(iter(self.strategies.values()))
            return await first_strategy.get_ttl(namespace, key, value)
        
        return None
    
    async def should_invalidate(self, namespace: str, key: str, event: CacheEvent) -> bool:
        """Check if any strategy requires invalidation."""
        for strategy in self.strategies.values():
            if await strategy.should_invalidate(namespace, key, event):
                return True
        return False


class SmartCacheStrategy(CacheStrategy):
    """Smart cache strategy with adaptive behavior."""
    
    def __init__(self, base_ttl: int = 3600, min_ttl: int = 300, max_ttl: int = 86400):
        """Initialize smart cache strategy.
        
        Args:
            base_ttl: Base TTL in seconds
            min_ttl: Minimum TTL in seconds
            max_ttl: Maximum TTL in seconds
        """
        super().__init__("Smart")
        self.base_ttl = base_ttl
        self.min_ttl = min_ttl
        self.max_ttl = max_ttl
        
        # Track cache performance
        self.hit_rates: Dict[str, float] = {}
        self.access_patterns: Dict[str, List[float]] = {}
        
    async def should_cache(self, namespace: str, key: str, value: Any) -> bool:
        """Decide caching based on value characteristics."""
        # Don't cache None values
        if value is None:
            return False
        
        # Don't cache very large objects (>1MB serialized)
        try:
            import json
            serialized_size = len(json.dumps(value, default=str))
            if serialized_size > 1024 * 1024:  # 1MB
                return False
        except:
            pass
        
        return True
    
    async def get_ttl(self, namespace: str, key: str, value: Any) -> Optional[int]:
        """Calculate adaptive TTL based on access patterns."""
        cache_key = f"{namespace}:{key}"
        
        # Get hit rate for this namespace
        hit_rate = self.hit_rates.get(namespace, 0.5)
        
        # Get access frequency
        access_times = self.access_patterns.get(cache_key, [])
        if len(access_times) >= 2:
            # Calculate average time between accesses
            intervals = [access_times[i] - access_times[i-1] 
                        for i in range(1, len(access_times))]
            avg_interval = sum(intervals) / len(intervals)
            
            # Adjust TTL based on access frequency
            if avg_interval < 300:  # Very frequent access
                ttl = min(self.max_ttl, self.base_ttl * 2)
            elif avg_interval > 3600:  # Infrequent access
                ttl = max(self.min_ttl, self.base_ttl // 2)
            else:
                ttl = self.base_ttl
        else:
            ttl = self.base_ttl
        
        # Adjust based on hit rate
        if hit_rate > 0.8:  # High hit rate
            ttl = min(self.max_ttl, int(ttl * 1.5))
        elif hit_rate < 0.3:  # Low hit rate
            ttl = max(self.min_ttl, int(ttl * 0.7))
        
        return ttl
    
    async def should_invalidate(self, namespace: str, key: str, event: CacheEvent) -> bool:
        """Smart invalidation based on event relevance."""
        # Check if event is related to the cached data
        if event.metadata:
            entity_id = event.metadata.get('entity_id')
            if entity_id and entity_id in key:
                return True
        
        # Time-based invalidation for stale data
        if event.event_type in [CacheEventType.CONTRACT_UPDATED, CacheEventType.ANALYSIS_COMPLETED]:
            return namespace in ["contracts", "analysis", "risk_scores"]
        
        return False
    
    def record_hit(self, namespace: str):
        """Record cache hit for adaptive behavior."""
        current_hits = self.hit_rates.get(namespace, 0.5)
        # Simple exponential moving average
        self.hit_rates[namespace] = current_hits * 0.9 + 1.0 * 0.1
    
    def record_miss(self, namespace: str):
        """Record cache miss for adaptive behavior."""
        current_hits = self.hit_rates.get(namespace, 0.5)
        # Simple exponential moving average
        self.hit_rates[namespace] = current_hits * 0.9 + 0.0 * 0.1
    
    def record_access(self, namespace: str, key: str):
        """Record access time for pattern analysis."""
        cache_key = f"{namespace}:{key}"
        current_time = time.time()
        
        if cache_key not in self.access_patterns:
            self.access_patterns[cache_key] = []
        
        self.access_patterns[cache_key].append(current_time)
        
        # Keep only recent access times (last 100)
        if len(self.access_patterns[cache_key]) > 100:
            self.access_patterns[cache_key] = self.access_patterns[cache_key][-100:]
