"""
Cache Manager - Redis-based Caching Infrastructure

This module provides comprehensive Redis-based caching with intelligent
invalidation strategies, performance monitoring, and distributed support.

Features:
- Redis connection pooling and management
- Automatic serialization/deserialization
- TTL-based and event-based cache invalidation
- Cache hit/miss metrics and monitoring
- Distributed cache coordination
- Cache warming and preloading strategies

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import hashlib
import json
import pickle
import time
from contextlib import asynccontextmanager
from functools import wraps
from typing import Any, Dict, List, Optional, Union, Callable, Tuple

import redis.asyncio as redis
import structlog
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import get_config
from logging_config import get_logger, get_metrics_collector


class CacheManager:
    """Comprehensive Redis-based cache manager."""
    
    def __init__(self, redis_url: Optional[str] = None):
        """Initialize cache manager.
        
        Args:
            redis_url: Redis connection URL (defaults to config)
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        self.redis_url = redis_url or self.config.redis.url
        self.redis_client: Optional[redis.Redis] = None
        self._initialized = False
        
        # Cache statistics
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
        
        # Default configuration
        self.default_ttl = 3600  # 1 hour
        self.key_prefix = "rug_detector"
        self.serialization_format = "json"  # json, pickle, or msgpack
        
    async def initialize(self) -> bool:
        """Initialize Redis connection."""
        try:
            self.logger.info("Initializing Redis cache connection")
            
            # Create Redis client with connection pooling
            self.redis_client = redis.from_url(
                self.redis_url,
                max_connections=self.config.redis.max_connections,
                socket_timeout=self.config.redis.socket_timeout,
                socket_connect_timeout=self.config.redis.socket_connect_timeout,
                retry_on_timeout=True,
                health_check_interval=30,
                decode_responses=False  # We handle encoding ourselves
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self._initialized = True
            
            self.logger.info(
                "Redis cache connection initialized successfully",
                max_connections=self.config.redis.max_connections,
                socket_timeout=self.config.redis.socket_timeout
            )
            
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to initialize Redis cache connection",
                error=str(e),
                redis_url=self.redis_url.split('@')[-1] if '@' in self.redis_url else "unknown"
            )
            return False
    
    def _generate_key(self, namespace: str, key: str, **kwargs) -> str:
        """Generate cache key with namespace and optional parameters.
        
        Args:
            namespace: Cache namespace
            key: Base key
            **kwargs: Additional parameters to include in key
            
        Returns:
            Generated cache key
        """
        # Create base key
        base_key = f"{self.key_prefix}:{namespace}:{key}"
        
        # Add parameters if provided
        if kwargs:
            # Sort parameters for consistent key generation
            param_str = "&".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
            param_hash = hashlib.md5(param_str.encode()).hexdigest()[:8]
            base_key = f"{base_key}:{param_hash}"
        
        return base_key
    
    def _serialize(self, data: Any) -> bytes:
        """Serialize data for storage.
        
        Args:
            data: Data to serialize
            
        Returns:
            Serialized data as bytes
        """
        try:
            if self.serialization_format == "json":
                return json.dumps(data, default=str).encode('utf-8')
            elif self.serialization_format == "pickle":
                return pickle.dumps(data)
            else:
                # Default to JSON
                return json.dumps(data, default=str).encode('utf-8')
        except Exception as e:
            self.logger.error("Serialization failed", error=str(e), data_type=type(data).__name__)
            raise
    
    def _deserialize(self, data: bytes) -> Any:
        """Deserialize data from storage.
        
        Args:
            data: Serialized data
            
        Returns:
            Deserialized data
        """
        try:
            if self.serialization_format == "json":
                return json.loads(data.decode('utf-8'))
            elif self.serialization_format == "pickle":
                return pickle.loads(data)
            else:
                # Default to JSON
                return json.loads(data.decode('utf-8'))
        except Exception as e:
            self.logger.error("Deserialization failed", error=str(e))
            raise
    
    async def get(self, namespace: str, key: str, **kwargs) -> Optional[Any]:
        """Get value from cache.
        
        Args:
            namespace: Cache namespace
            key: Cache key
            **kwargs: Additional parameters for key generation
            
        Returns:
            Cached value or None if not found
        """
        if not self._initialized or not self.redis_client:
            return None
        
        cache_key = self._generate_key(namespace, key, **kwargs)
        
        try:
            start_time = time.time()
            data = await self.redis_client.get(cache_key)
            
            if data is not None:
                value = self._deserialize(data)
                self._stats['hits'] += 1
                
                # Update metrics
                self.metrics.update_cache_hit_rate(namespace, 
                    self._stats['hits'] / (self._stats['hits'] + self._stats['misses']) * 100)
                
                self.logger.debug(
                    "Cache hit",
                    namespace=namespace,
                    key=key,
                    cache_key=cache_key,
                    duration_ms=(time.time() - start_time) * 1000
                )
                
                return value
            else:
                self._stats['misses'] += 1
                
                # Update metrics
                self.metrics.update_cache_hit_rate(namespace,
                    self._stats['hits'] / (self._stats['hits'] + self._stats['misses']) * 100)
                
                self.logger.debug(
                    "Cache miss",
                    namespace=namespace,
                    key=key,
                    cache_key=cache_key
                )
                
                return None
                
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(
                "Cache get failed",
                namespace=namespace,
                key=key,
                cache_key=cache_key,
                error=str(e)
            )
            return None
    
    async def set(self, namespace: str, key: str, value: Any, 
                  ttl: Optional[int] = None, **kwargs) -> bool:
        """Set value in cache.
        
        Args:
            namespace: Cache namespace
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (defaults to default_ttl)
            **kwargs: Additional parameters for key generation
            
        Returns:
            True if successful, False otherwise
        """
        if not self._initialized or not self.redis_client:
            return False
        
        cache_key = self._generate_key(namespace, key, **kwargs)
        ttl = ttl or self.default_ttl
        
        try:
            start_time = time.time()
            serialized_data = self._serialize(value)
            
            await self.redis_client.setex(cache_key, ttl, serialized_data)
            
            self._stats['sets'] += 1
            
            self.logger.debug(
                "Cache set",
                namespace=namespace,
                key=key,
                cache_key=cache_key,
                ttl=ttl,
                size_bytes=len(serialized_data),
                duration_ms=(time.time() - start_time) * 1000
            )
            
            return True
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(
                "Cache set failed",
                namespace=namespace,
                key=key,
                cache_key=cache_key,
                error=str(e)
            )
            return False
    
    async def delete(self, namespace: str, key: str, **kwargs) -> bool:
        """Delete value from cache.
        
        Args:
            namespace: Cache namespace
            key: Cache key
            **kwargs: Additional parameters for key generation
            
        Returns:
            True if successful, False otherwise
        """
        if not self._initialized or not self.redis_client:
            return False
        
        cache_key = self._generate_key(namespace, key, **kwargs)
        
        try:
            result = await self.redis_client.delete(cache_key)
            self._stats['deletes'] += 1
            
            self.logger.debug(
                "Cache delete",
                namespace=namespace,
                key=key,
                cache_key=cache_key,
                deleted=bool(result)
            )
            
            return bool(result)
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(
                "Cache delete failed",
                namespace=namespace,
                key=key,
                cache_key=cache_key,
                error=str(e)
            )
            return False
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate all keys matching a pattern.
        
        Args:
            pattern: Redis pattern (e.g., "namespace:*")
            
        Returns:
            Number of keys deleted
        """
        if not self._initialized or not self.redis_client:
            return 0
        
        try:
            full_pattern = f"{self.key_prefix}:{pattern}"
            keys = await self.redis_client.keys(full_pattern)
            
            if keys:
                deleted = await self.redis_client.delete(*keys)
                self._stats['deletes'] += deleted
                
                self.logger.info(
                    "Cache pattern invalidation",
                    pattern=full_pattern,
                    keys_deleted=deleted
                )
                
                return deleted
            
            return 0
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(
                "Cache pattern invalidation failed",
                pattern=pattern,
                error=str(e)
            )
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Cache statistics dictionary
        """
        stats = self._stats.copy()
        
        # Calculate hit rate
        total_requests = stats['hits'] + stats['misses']
        stats['hit_rate'] = (stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        # Get Redis info if available
        if self._initialized and self.redis_client:
            try:
                redis_info = await self.redis_client.info()
                stats['redis_info'] = {
                    'used_memory': redis_info.get('used_memory', 0),
                    'used_memory_human': redis_info.get('used_memory_human', '0B'),
                    'connected_clients': redis_info.get('connected_clients', 0),
                    'total_commands_processed': redis_info.get('total_commands_processed', 0),
                    'keyspace_hits': redis_info.get('keyspace_hits', 0),
                    'keyspace_misses': redis_info.get('keyspace_misses', 0)
                }
            except Exception as e:
                self.logger.warning("Failed to get Redis info", error=str(e))
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Check cache health.
        
        Returns:
            Health status dictionary
        """
        health_data = {
            "status": "unknown",
            "response_time_ms": None,
            "error": None
        }
        
        if not self._initialized or not self.redis_client:
            health_data["status"] = "not_initialized"
            health_data["error"] = "Cache not initialized"
            return health_data
        
        start_time = time.time()
        
        try:
            # Test basic connectivity
            await self.redis_client.ping()
            
            # Test set/get operations
            test_key = f"{self.key_prefix}:health_check"
            test_value = {"timestamp": time.time(), "test": True}
            
            await self.redis_client.setex(test_key, 60, json.dumps(test_value))
            result = await self.redis_client.get(test_key)
            await self.redis_client.delete(test_key)
            
            if result:
                retrieved_value = json.loads(result)
                if retrieved_value.get("test") is True:
                    health_data["status"] = "healthy"
                else:
                    health_data["status"] = "degraded"
                    health_data["error"] = "Test operation returned unexpected result"
            else:
                health_data["status"] = "degraded"
                health_data["error"] = "Test operation failed"
                
        except Exception as e:
            health_data["status"] = "unhealthy"
            health_data["error"] = f"Cache error: {str(e)}"
        
        finally:
            health_data["response_time_ms"] = (time.time() - start_time) * 1000
        
        return health_data
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            self._initialized = False
            self.logger.info("Redis cache connection closed")


# Global cache manager instance
_cache_manager: Optional[CacheManager] = None


async def get_cache_manager() -> CacheManager:
    """Get global cache manager instance."""
    global _cache_manager
    
    if _cache_manager is None:
        _cache_manager = CacheManager()
        
        # Initialize if not already done
        if not _cache_manager._initialized:
            await _cache_manager.initialize()
    
    return _cache_manager


def cache_key(namespace: str, key: str, **kwargs) -> str:
    """Generate cache key (synchronous helper)."""
    manager = CacheManager()
    return manager._generate_key(namespace, key, **kwargs)


def cached(namespace: str, ttl: Optional[int] = None, key_func: Optional[Callable] = None):
    """Decorator for caching function results.
    
    Args:
        namespace: Cache namespace
        ttl: Time to live in seconds
        key_func: Function to generate cache key from arguments
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key_str = key_func(*args, **kwargs)
            else:
                # Default key generation from function name and arguments
                arg_str = "_".join(str(arg) for arg in args)
                kwarg_str = "_".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key_str = f"{func.__name__}_{arg_str}_{kwarg_str}"
            
            # Try to get from cache
            cache_manager = await get_cache_manager()
            cached_result = await cache_manager.get(namespace, cache_key_str)
            
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(namespace, cache_key_str, result, ttl)
            
            return result
        
        return wrapper
    return decorator


async def invalidate_cache(namespace: str, pattern: str = "*") -> int:
    """Invalidate cache entries matching pattern."""
    cache_manager = await get_cache_manager()
    return await cache_manager.invalidate_pattern(f"{namespace}:{pattern}")


async def warm_cache(namespace: str, data: Dict[str, Any], ttl: Optional[int] = None) -> int:
    """Warm cache with provided data.
    
    Args:
        namespace: Cache namespace
        data: Dictionary of key-value pairs to cache
        ttl: Time to live in seconds
        
    Returns:
        Number of items successfully cached
    """
    cache_manager = await get_cache_manager()
    success_count = 0
    
    for key, value in data.items():
        if await cache_manager.set(namespace, key, value, ttl):
            success_count += 1
    
    return success_count
