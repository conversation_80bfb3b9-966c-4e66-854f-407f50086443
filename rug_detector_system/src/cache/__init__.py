"""
Cache Package Initialization

This module provides Redis-based caching infrastructure for the Rug Detector System
with intelligent cache invalidation, performance optimization, and monitoring.

Features:
- Redis connection management
- Cache key management and namespacing
- TTL-based and event-based invalidation
- Performance monitoring and metrics
- Distributed caching support
- Cache warming and preloading

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

from .manager import (
    CacheManager, get_cache_manager, cache_key,
    cached, invalidate_cache, warm_cache
)
from .strategies import (
    CacheStrategy, TTLStrategy, LRUStrategy, 
    EventBasedStrategy, HybridStrategy
)
from .performance import (
    PerformanceCache, QueryCache, APICache,
    BlockchainCache, AnalysisCache
)

__all__ = [
    # Core cache management
    'CacheManager', 'get_cache_manager', 'cache_key',
    'cached', 'invalidate_cache', 'warm_cache',
    
    # Cache strategies
    'CacheStrategy', 'TTLStrategy', 'LRUStrategy',
    'EventBasedStrategy', 'HybridStrategy',
    
    # Performance caches
    'PerformanceCache', 'QueryCache', 'APICache',
    'BlockchainCache', 'AnalysisCache'
]
