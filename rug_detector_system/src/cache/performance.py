"""
Performance Cache - Specialized Caching for Different Data Types

This module provides specialized cache implementations optimized for
different types of data in the Rug Detector System.

Features:
- Query result caching with intelligent invalidation
- API response caching with rate limit awareness
- Blockchain data caching with block-based invalidation
- Analysis result caching with dependency tracking
- Performance monitoring and optimization

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import hashlib
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union, Callable
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from logging_config import get_logger, get_metrics_collector
from .manager import get_cache_manager
from .strategies import CacheStrategy, TTLStrategy, SmartCacheStrategy


@dataclass
class CacheMetrics:
    """Cache performance metrics."""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    errors: int = 0
    total_response_time_ms: float = 0.0
    
    @property
    def hit_rate(self) -> float:
        """Calculate hit rate percentage."""
        total = self.hits + self.misses
        return (self.hits / total * 100) if total > 0 else 0.0
    
    @property
    def avg_response_time_ms(self) -> float:
        """Calculate average response time."""
        total_ops = self.hits + self.misses + self.sets
        return (self.total_response_time_ms / total_ops) if total_ops > 0 else 0.0


class PerformanceCache(ABC):
    """Abstract base class for performance-optimized caches."""
    
    def __init__(self, namespace: str, strategy: Optional[CacheStrategy] = None):
        """Initialize performance cache.
        
        Args:
            namespace: Cache namespace
            strategy: Cache strategy (defaults to SmartCacheStrategy)
        """
        self.namespace = namespace
        self.strategy = strategy or SmartCacheStrategy()
        self.logger = get_logger(f"{__name__}.{namespace}")
        self.metrics_collector = get_metrics_collector()
        self.metrics = CacheMetrics()
        
    async def get(self, key: str, **kwargs) -> Optional[Any]:
        """Get value from cache with performance tracking.
        
        Args:
            key: Cache key
            **kwargs: Additional parameters
            
        Returns:
            Cached value or None
        """
        start_time = time.time()
        
        try:
            cache_manager = await get_cache_manager()
            result = await cache_manager.get(self.namespace, key, **kwargs)
            
            response_time = (time.time() - start_time) * 1000
            self.metrics.total_response_time_ms += response_time
            
            if result is not None:
                self.metrics.hits += 1
                self.logger.debug(
                    "Cache hit",
                    key=key,
                    response_time_ms=response_time
                )
            else:
                self.metrics.misses += 1
                self.logger.debug(
                    "Cache miss",
                    key=key,
                    response_time_ms=response_time
                )
            
            return result
            
        except Exception as e:
            self.metrics.errors += 1
            self.logger.error(
                "Cache get error",
                key=key,
                error=str(e)
            )
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, **kwargs) -> bool:
        """Set value in cache with performance tracking.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live (uses strategy if not provided)
            **kwargs: Additional parameters
            
        Returns:
            True if successful
        """
        start_time = time.time()
        
        try:
            # Check if we should cache this value
            if not await self.strategy.should_cache(self.namespace, key, value):
                return False
            
            # Get TTL from strategy if not provided
            if ttl is None:
                ttl = await self.strategy.get_ttl(self.namespace, key, value)
            
            cache_manager = await get_cache_manager()
            success = await cache_manager.set(self.namespace, key, value, ttl, **kwargs)
            
            response_time = (time.time() - start_time) * 1000
            self.metrics.total_response_time_ms += response_time
            
            if success:
                self.metrics.sets += 1
                self.logger.debug(
                    "Cache set",
                    key=key,
                    ttl=ttl,
                    response_time_ms=response_time
                )
            else:
                self.metrics.errors += 1
            
            return success
            
        except Exception as e:
            self.metrics.errors += 1
            self.logger.error(
                "Cache set error",
                key=key,
                error=str(e)
            )
            return False
    
    async def delete(self, key: str, **kwargs) -> bool:
        """Delete value from cache.
        
        Args:
            key: Cache key
            **kwargs: Additional parameters
            
        Returns:
            True if successful
        """
        try:
            cache_manager = await get_cache_manager()
            success = await cache_manager.delete(self.namespace, key, **kwargs)
            
            if success:
                self.metrics.deletes += 1
            
            return success
            
        except Exception as e:
            self.metrics.errors += 1
            self.logger.error(
                "Cache delete error",
                key=key,
                error=str(e)
            )
            return False
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate cache entries matching pattern.
        
        Args:
            pattern: Pattern to match
            
        Returns:
            Number of entries invalidated
        """
        try:
            cache_manager = await get_cache_manager()
            deleted_count = await cache_manager.invalidate_pattern(f"{self.namespace}:{pattern}")
            self.metrics.deletes += deleted_count
            return deleted_count
            
        except Exception as e:
            self.metrics.errors += 1
            self.logger.error(
                "Cache pattern invalidation error",
                pattern=pattern,
                error=str(e)
            )
            return 0
    
    def get_metrics(self) -> CacheMetrics:
        """Get cache performance metrics."""
        return self.metrics


class QueryCache(PerformanceCache):
    """Cache for database query results."""
    
    def __init__(self):
        """Initialize query cache."""
        # Use shorter TTL for query results as they can become stale
        strategy = TTLStrategy(default_ttl=1800, ttl_map={
            "contracts": 3600,      # 1 hour for contract data
            "transactions": 1800,   # 30 minutes for transaction data
            "analysis": 900,        # 15 minutes for analysis results
            "risk_scores": 600,     # 10 minutes for risk scores
            "alerts": 300           # 5 minutes for alerts
        })
        super().__init__("query", strategy)
    
    async def cache_query_result(self, query_hash: str, result: Any, 
                                table_name: Optional[str] = None) -> bool:
        """Cache database query result.
        
        Args:
            query_hash: Hash of the SQL query
            result: Query result to cache
            table_name: Primary table name for TTL optimization
            
        Returns:
            True if cached successfully
        """
        # Use table-specific TTL if available
        ttl = None
        if table_name and hasattr(self.strategy, 'ttl_map'):
            ttl = self.strategy.ttl_map.get(table_name)
        
        return await self.set(query_hash, result, ttl)
    
    async def get_query_result(self, query_hash: str) -> Optional[Any]:
        """Get cached query result.
        
        Args:
            query_hash: Hash of the SQL query
            
        Returns:
            Cached result or None
        """
        return await self.get(query_hash)


class APICache(PerformanceCache):
    """Cache for external API responses."""
    
    def __init__(self):
        """Initialize API cache."""
        # Use longer TTL for API responses to reduce external calls
        strategy = TTLStrategy(default_ttl=3600, ttl_map={
            "etherscan": 1800,      # 30 minutes for Etherscan
            "coingecko": 300,       # 5 minutes for CoinGecko (price data)
            "dune": 3600,           # 1 hour for Dune Analytics
            "crossref": 86400,      # 24 hours for CrossRef (static data)
            "alpha_vantage": 900    # 15 minutes for Alpha Vantage
        })
        super().__init__("api", strategy)
    
    async def cache_api_response(self, api_name: str, endpoint: str, 
                                params: Dict[str, Any], response: Any) -> bool:
        """Cache API response.
        
        Args:
            api_name: Name of the API
            endpoint: API endpoint
            params: Request parameters
            response: API response to cache
            
        Returns:
            True if cached successfully
        """
        # Generate cache key from endpoint and parameters
        param_str = "&".join(f"{k}={v}" for k, v in sorted(params.items()))
        key_data = f"{endpoint}?{param_str}"
        cache_key = hashlib.md5(key_data.encode()).hexdigest()
        
        # Use API-specific TTL
        ttl = None
        if hasattr(self.strategy, 'ttl_map'):
            ttl = self.strategy.ttl_map.get(api_name)
        
        return await self.set(cache_key, response, ttl, api_name=api_name)
    
    async def get_api_response(self, api_name: str, endpoint: str, 
                              params: Dict[str, Any]) -> Optional[Any]:
        """Get cached API response.
        
        Args:
            api_name: Name of the API
            endpoint: API endpoint
            params: Request parameters
            
        Returns:
            Cached response or None
        """
        # Generate same cache key
        param_str = "&".join(f"{k}={v}" for k, v in sorted(params.items()))
        key_data = f"{endpoint}?{param_str}"
        cache_key = hashlib.md5(key_data.encode()).hexdigest()
        
        return await self.get(cache_key, api_name=api_name)


class BlockchainCache(PerformanceCache):
    """Cache for blockchain data."""
    
    def __init__(self):
        """Initialize blockchain cache."""
        # Use very long TTL for immutable blockchain data
        strategy = TTLStrategy(default_ttl=86400, ttl_map={
            "blocks": 86400 * 7,    # 1 week for block data (immutable)
            "transactions": 86400 * 7,  # 1 week for transaction data (immutable)
            "contracts": 3600,      # 1 hour for contract state (can change)
            "balances": 300,        # 5 minutes for balance data (changes frequently)
            "prices": 60            # 1 minute for price data (very volatile)
        })
        super().__init__("blockchain", strategy)
    
    async def cache_block_data(self, chain: str, block_number: int, data: Any) -> bool:
        """Cache block data.
        
        Args:
            chain: Blockchain name
            block_number: Block number
            data: Block data to cache
            
        Returns:
            True if cached successfully
        """
        cache_key = f"{chain}_block_{block_number}"
        return await self.set(cache_key, data, chain=chain, block_number=block_number)
    
    async def get_block_data(self, chain: str, block_number: int) -> Optional[Any]:
        """Get cached block data.
        
        Args:
            chain: Blockchain name
            block_number: Block number
            
        Returns:
            Cached block data or None
        """
        cache_key = f"{chain}_block_{block_number}"
        return await self.get(cache_key, chain=chain, block_number=block_number)
    
    async def cache_transaction_data(self, chain: str, tx_hash: str, data: Any) -> bool:
        """Cache transaction data.
        
        Args:
            chain: Blockchain name
            tx_hash: Transaction hash
            data: Transaction data to cache
            
        Returns:
            True if cached successfully
        """
        cache_key = f"{chain}_tx_{tx_hash}"
        return await self.set(cache_key, data, chain=chain, tx_hash=tx_hash)
    
    async def get_transaction_data(self, chain: str, tx_hash: str) -> Optional[Any]:
        """Get cached transaction data.
        
        Args:
            chain: Blockchain name
            tx_hash: Transaction hash
            
        Returns:
            Cached transaction data or None
        """
        cache_key = f"{chain}_tx_{tx_hash}"
        return await self.get(cache_key, chain=chain, tx_hash=tx_hash)


class AnalysisCache(PerformanceCache):
    """Cache for analysis results."""
    
    def __init__(self):
        """Initialize analysis cache."""
        # Use medium TTL for analysis results
        strategy = SmartCacheStrategy(base_ttl=1800, min_ttl=300, max_ttl=7200)
        super().__init__("analysis", strategy)
    
    async def cache_analysis_result(self, contract_address: str, analysis_type: str, 
                                   result: Any, version: str = "1.0") -> bool:
        """Cache analysis result.
        
        Args:
            contract_address: Contract address
            analysis_type: Type of analysis
            result: Analysis result to cache
            version: Analyzer version
            
        Returns:
            True if cached successfully
        """
        cache_key = f"{contract_address}_{analysis_type}_{version}"
        return await self.set(cache_key, result, 
                            contract_address=contract_address,
                            analysis_type=analysis_type,
                            version=version)
    
    async def get_analysis_result(self, contract_address: str, analysis_type: str,
                                 version: str = "1.0") -> Optional[Any]:
        """Get cached analysis result.
        
        Args:
            contract_address: Contract address
            analysis_type: Type of analysis
            version: Analyzer version
            
        Returns:
            Cached analysis result or None
        """
        cache_key = f"{contract_address}_{analysis_type}_{version}"
        return await self.get(cache_key,
                            contract_address=contract_address,
                            analysis_type=analysis_type,
                            version=version)
    
    async def invalidate_contract_analysis(self, contract_address: str) -> int:
        """Invalidate all analysis results for a contract.
        
        Args:
            contract_address: Contract address
            
        Returns:
            Number of entries invalidated
        """
        pattern = f"{contract_address}_*"
        return await self.invalidate_pattern(pattern)
