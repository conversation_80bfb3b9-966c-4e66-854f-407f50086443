"""
Rug Detector System - Production-Grade Blockchain Security Analysis Platform

A comprehensive, modular system for detecting rug-pulls and suspicious activities
on EVM-compatible blockchains in real-time.

Author: MLDevOps Architect
Version: 2.0.0
Python: 3.11+
Architecture: Production-ready, M1 Mac optimized
"""

__version__ = "2.0.0"
__author__ = "MLDevOps Architect"
__email__ = "<EMAIL>"

# Package metadata
__title__ = "rug-detector-system"
__description__ = "Production-grade blockchain security analysis platform"
__url__ = "https://github.com/rugdetector/system"
__license__ = "MIT"
__copyright__ = "Copyright 2025 Rug Detector System"

# Version info tuple for programmatic access
VERSION_INFO = tuple(map(int, __version__.split(".")))

# Supported blockchain networks
SUPPORTED_NETWORKS = [
    "ethereum",
    "polygon",
    "bsc",
]

# Quality assurance metadata
QUALITY_STANDARD = "99.9th percentile"
ARCHITECTURE_PATTERN = "MLDevOps"
TESTING_COVERAGE_TARGET = 95.0
