version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: rug_detector_postgres
    environment:
      POSTGRES_DB: rug_detector
      POSTGRES_USER: rug_detector_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rug_detector_user -d rug_detector"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - rug_detector_network

  # Redis Cache & Message Queue
  redis:
    image: redis:7-alpine
    container_name: rug_detector_redis
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - rug_detector_network

  # Rug Detector Main Application
  rug_detector:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: rug_detector_app
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://rug_detector_user:${POSTGRES_PASSWORD}@postgres:5432/rug_detector
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app/src
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - rug_detector_network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: rug_detector_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - rug_detector_network

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: rug_detector_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - rug_detector_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: rug_detector_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - rug_detector
    restart: unless-stopped
    networks:
      - rug_detector_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  rug_detector_network:
    driver: bridge
