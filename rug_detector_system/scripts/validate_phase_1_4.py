#!/usr/bin/env python3
"""
Phase 1.4 Validation Script - Logging & Observability Foundation

This script validates the logging and observability infrastructure
implemented in Phase 1.4.

Validation Criteria:
- Structured logging functionality
- Prometheus metrics collection
- Health check system
- Performance monitoring
- Error tracking and alerting
- Integration with existing infrastructure

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import sys
import asyncio
import time
import requests
from pathlib import Path
from typing import List, Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from logging_config import (
    get_logger, get_metrics_collector, initialize_observability,
    performance_monitor, StructuredLogger, MetricsCollector
)
from health_check import get_system_health, health_to_dict, HealthStatus


class Phase14Validator:
    """Phase 1.4 specific validation for logging and observability."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
        
    def validate_all(self) -> bool:
        """Run all Phase 1.4 validation checks."""
        print("🔍 Phase 1.4 Validation: Logging & Observability Foundation")
        print("=" * 70)
        
        # Core validation checks
        self._validate_structured_logging()
        self._validate_metrics_collection()
        asyncio.run(self._validate_health_checks())
        self._validate_performance_monitoring()
        self._validate_observability_integration()
        
        # Summary
        self._print_summary()
        
        return len(self.errors) == 0
    
    def _check(self, description: str, condition: bool, 
               error_msg: str = None, warning_msg: str = None) -> bool:
        """Helper method to perform a validation check."""
        self.total_checks += 1
        
        if condition:
            print(f"✅ {description}")
            self.success_count += 1
            return True
        else:
            print(f"❌ {description}")
            if error_msg:
                self.errors.append(error_msg)
            if warning_msg:
                self.warnings.append(warning_msg)
            return False
    
    def _validate_structured_logging(self):
        """Validate structured logging functionality."""
        print("\n📝 Structured Logging Validation")
        print("-" * 40)
        
        try:
            # Test logger creation
            logger = get_logger("test_logger")
            self._check(
                "Logger instantiation",
                isinstance(logger, StructuredLogger),
                "Failed to create structured logger"
            )
            
            # Test logging methods
            logger.info("Test info message", test_field="test_value")
            logger.error("Test error message", error_code=500)
            logger.warning("Test warning message", component="test")
            logger.debug("Test debug message", debug_info={"key": "value"})
            
            self._check(
                "Logging methods functional",
                True,  # If we get here, logging worked
                None
            )
            
            # Test context binding
            bound_logger = logger.bind(request_id="test-123", user_id="user-456")
            bound_logger.info("Test bound logging")
            
            self._check(
                "Context binding functional",
                True,  # If we get here, binding worked
                None
            )
            
        except Exception as e:
            self._check("Structured logging", False, f"Logging validation failed: {e}")
    
    def _validate_metrics_collection(self):
        """Validate Prometheus metrics collection."""
        print("\n📊 Metrics Collection Validation")
        print("-" * 40)
        
        try:
            # Test metrics collector creation
            metrics = get_metrics_collector()
            self._check(
                "Metrics collector instantiation",
                isinstance(metrics, MetricsCollector),
                "Failed to create metrics collector"
            )
            
            # Test metrics recording
            metrics.record_request("GET", "/test", "200")
            metrics.record_api_call("test_api", "success", 0.5)
            metrics.record_block_processed("ethereum")
            metrics.record_analysis_result("rug_pull", "high")
            
            self._check(
                "Metrics recording functional",
                True,  # If we get here, metrics recording worked
                None
            )
            
            # Test metrics export
            metrics_output = metrics.get_metrics()
            self._check(
                "Metrics export functional",
                isinstance(metrics_output, str) and len(metrics_output) > 0,
                "Failed to export metrics"
            )
            
            # Check for expected metrics
            expected_metrics = [
                "rug_detector_requests_total",
                "rug_detector_api_calls_total",
                "rug_detector_blocks_processed_total",
                "rug_detector_analysis_results_total"
            ]
            
            for metric_name in expected_metrics:
                self._check(
                    f"Metric '{metric_name}' present",
                    metric_name in metrics_output,
                    f"Expected metric {metric_name} not found in output"
                )
            
        except Exception as e:
            self._check("Metrics collection", False, f"Metrics validation failed: {e}")
    
    async def _validate_health_checks(self):
        """Validate health check system."""
        print("\n🏥 Health Check System Validation")
        print("-" * 40)
        
        try:
            # Test health check execution
            health = await get_system_health()
            
            self._check(
                "Health check execution",
                health is not None,
                "Failed to execute health checks"
            )
            
            self._check(
                f"Health status determined: {health.status.value}",
                health.status in HealthStatus,
                "Invalid health status returned"
            )
            
            # Check component coverage
            expected_components = [
                "database", "redis", "blockchain", 
                "external_apis", "system_resources"
            ]
            
            component_names = [c.name for c in health.components]
            
            for component in expected_components:
                self._check(
                    f"Component '{component}' checked",
                    component in component_names,
                    f"Expected component {component} not found in health check"
                )
            
            # Test health serialization
            health_dict = health_to_dict(health)
            self._check(
                "Health status serialization",
                isinstance(health_dict, dict) and "status" in health_dict,
                "Failed to serialize health status"
            )
            
            # Check response times
            components_with_timing = [c for c in health.components if c.response_time_ms is not None]
            self._check(
                f"Response time tracking ({len(components_with_timing)}/{len(health.components)} components)",
                len(components_with_timing) > 0,
                "No components have response time tracking"
            )
            
        except Exception as e:
            self._check("Health checks", False, f"Health check validation failed: {e}")
    
    def _validate_performance_monitoring(self):
        """Validate performance monitoring capabilities."""
        print("\n⚡ Performance Monitoring Validation")
        print("-" * 40)
        
        try:
            # Test performance decorator
            @performance_monitor("test_operation")
            def test_function():
                time.sleep(0.1)  # Simulate work
                return "test_result"
            
            result = test_function()
            
            self._check(
                "Performance monitoring decorator",
                result == "test_result",
                "Performance monitoring decorator failed"
            )
            
            # Test context manager timing
            metrics = get_metrics_collector()
            
            with metrics.time_operation("test_context_operation", {"test": "label"}):
                time.sleep(0.05)  # Simulate work
            
            self._check(
                "Context manager timing",
                True,  # If we get here, timing worked
                None
            )
            
            # Test metrics updates
            metrics.update_system_health(True)
            metrics.update_database_connections(5)
            metrics.update_cache_hit_rate("redis", 0.85)
            
            self._check(
                "Metrics updates functional",
                True,  # If we get here, updates worked
                None
            )
            
        except Exception as e:
            self._check("Performance monitoring", False, f"Performance monitoring failed: {e}")
    
    def _validate_observability_integration(self):
        """Validate observability stack integration."""
        print("\n🔭 Observability Integration Validation")
        print("-" * 40)
        
        try:
            # Test observability initialization
            init_success = initialize_observability()
            
            self._check(
                "Observability stack initialization",
                init_success,
                "Failed to initialize observability stack"
            )
            
            # Test metrics server (if running)
            try:
                from config import get_config
                config = get_config()
                
                # Try to access metrics endpoint
                response = requests.get(
                    f"http://localhost:{config.monitoring.prometheus_port}/metrics",
                    timeout=5
                )
                
                self._check(
                    f"Metrics server accessible on port {config.monitoring.prometheus_port}",
                    response.status_code == 200,
                    "Metrics server not accessible"
                )
                
                self._check(
                    "Metrics endpoint returns Prometheus format",
                    "rug_detector_" in response.text,
                    "Metrics endpoint doesn't return expected format"
                )
                
            except requests.exceptions.RequestException:
                self._check(
                    "Metrics server accessibility",
                    False,
                    None,
                    "Metrics server not accessible (may not be running)"
                )
            
            # Test configuration integration
            config = get_config()
            
            self._check(
                "Monitoring configuration loaded",
                hasattr(config, 'monitoring'),
                "Monitoring configuration not available"
            )
            
            if hasattr(config, 'monitoring'):
                self._check(
                    f"Prometheus port configured: {config.monitoring.prometheus_port}",
                    1024 <= config.monitoring.prometheus_port <= 65535,
                    "Invalid Prometheus port configuration"
                )
                
                self._check(
                    f"Log level configured: {config.monitoring.log_level.value}",
                    config.monitoring.log_level.value in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                    "Invalid log level configuration"
                )
            
        except Exception as e:
            self._check("Observability integration", False, f"Integration validation failed: {e}")
    
    def _print_summary(self):
        """Print validation summary."""
        print("\n" + "=" * 70)
        print("📊 PHASE 1.4 VALIDATION SUMMARY")
        print("=" * 70)
        
        success_rate = (self.success_count / self.total_checks) * 100
        
        print(f"✅ Successful checks: {self.success_count}/{self.total_checks}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        print("\n" + "=" * 70)
        
        if len(self.errors) == 0:
            print("🎉 PHASE 1.4 VALIDATION SUCCESSFUL!")
            print("✨ Logging & observability foundation fully operational")
            print("📊 Metrics collection and monitoring active")
            print("🏥 Health check system validated")
            print("⚡ Performance monitoring confirmed")
            print("🎯 Ready to proceed to Phase 2")
        else:
            print("❌ PHASE 1.4 VALIDATION FAILED")
            print("🔧 Please address the errors above before proceeding")
        
        print("=" * 70)


def main():
    """Main validation entry point."""
    validator = Phase14Validator()
    success = validator.validate_all()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
