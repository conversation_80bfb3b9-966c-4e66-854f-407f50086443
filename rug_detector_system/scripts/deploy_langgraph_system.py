#!/usr/bin/env python3
"""
LangGraph Multi-Agent System Deployment Script

This script handles the complete deployment of the LangGraph multi-agent
rug detection system with Ollama integration.

Author: MLDevOps Architect
Version: 4.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import subprocess
import sys
import time
import requests
import json
from pathlib import Path
from typing import Dict, List, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from logging_config import get_logger


class LangGraphDeployment:
    """LangGraph multi-agent system deployment manager."""
    
    def __init__(self):
        """Initialize deployment manager."""
        self.logger = get_logger(__name__)
        self.project_root = Path(__file__).parent.parent
        
        # Deployment configuration
        self.services = [
            'postgres',
            'redis', 
            'ollama',
            'rug_detector'
        ]
        
        # Health check endpoints
        self.health_checks = {
            'postgres': 'postgresql://rug_detector_user:rug_detector_password@localhost:5432/rug_detector',
            'redis': 'redis://localhost:6379/0',
            'ollama': 'http://localhost:11434/api/tags',
            'rug_detector': 'http://localhost:8000/health'
        }
        
        # Required models for Ollama
        self.required_models = ['llama3.1:8b', 'mistral:7b']
    
    async def deploy_system(self) -> bool:
        """Deploy the complete LangGraph multi-agent system."""
        try:
            print("🚀 LANGGRAPH MULTI-AGENT SYSTEM DEPLOYMENT")
            print("=" * 70)
            print(f"Project Root: {self.project_root}")
            print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Pre-deployment checks
            if not await self._pre_deployment_checks():
                return False
            
            # Build and start services
            if not await self._build_and_start_services():
                return False
            
            # Setup Ollama models
            if not await self._setup_ollama_models():
                return False
            
            # Validate system health
            if not await self._validate_system_health():
                return False
            
            # Run validation tests
            if not await self._run_validation_tests():
                return False
            
            print("\n🎉 DEPLOYMENT SUCCESSFUL!")
            print("✅ LangGraph multi-agent system is ready")
            print("✅ All services are healthy")
            print("✅ Validation tests passed")
            
            await self._print_deployment_summary()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Deployment failed: {e}")
            print(f"❌ Deployment failed: {e}")
            return False
    
    async def _pre_deployment_checks(self) -> bool:
        """Perform pre-deployment checks."""
        print("\n🔍 Pre-Deployment Checks")
        print("-" * 40)
        
        checks = [
            ("Docker installed", self._check_docker),
            ("Docker Compose available", self._check_docker_compose),
            ("Project files present", self._check_project_files),
            ("Environment configured", self._check_environment),
            ("Ports available", self._check_ports)
        ]
        
        for check_name, check_func in checks:
            print(f"  Checking {check_name}...", end=" ")
            
            if await check_func():
                print("✅")
            else:
                print("❌")
                return False
        
        print("✅ All pre-deployment checks passed")
        return True
    
    async def _check_docker(self) -> bool:
        """Check if Docker is installed and running."""
        try:
            result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False
    
    async def _check_docker_compose(self) -> bool:
        """Check if Docker Compose is available."""
        try:
            result = subprocess.run(
                ["docker-compose", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False
    
    async def _check_project_files(self) -> bool:
        """Check if required project files exist."""
        required_files = [
            'docker-compose.yml',
            'Dockerfile',
            'requirements.txt',
            '.env',
            'src/agents/langgraph_integration.py'
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                print(f"\n❌ Missing required file: {file_path}")
                return False
        
        return True
    
    async def _check_environment(self) -> bool:
        """Check if environment is properly configured."""
        env_file = self.project_root / '.env'
        
        if not env_file.exists():
            return False
        
        # Check for required environment variables
        required_vars = [
            'OLLAMA_BASE_URL',
            'OLLAMA_MODEL',
            'DATABASE_URL',
            'REDIS_URL'
        ]
        
        env_content = env_file.read_text()
        
        for var in required_vars:
            if var not in env_content:
                print(f"\n❌ Missing environment variable: {var}")
                return False
        
        return True
    
    async def _check_ports(self) -> bool:
        """Check if required ports are available."""
        required_ports = [5432, 6379, 11434, 8000]
        
        for port in required_ports:
            if not await self._is_port_available(port):
                print(f"\n❌ Port {port} is not available")
                return False
        
        return True
    
    async def _is_port_available(self, port: int) -> bool:
        """Check if a port is available."""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0  # Port is available if connection fails
        except Exception:
            return True
    
    async def _build_and_start_services(self) -> bool:
        """Build and start all services."""
        print("\n🔧 Building and Starting Services")
        print("-" * 40)
        
        try:
            # Build services
            print("  Building Docker images...")
            build_result = subprocess.run(
                ["docker-compose", "build"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if build_result.returncode != 0:
                print(f"❌ Build failed: {build_result.stderr}")
                return False
            
            print("  ✅ Docker images built successfully")
            
            # Start services
            print("  Starting services...")
            start_result = subprocess.run(
                ["docker-compose", "up", "-d"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if start_result.returncode != 0:
                print(f"❌ Start failed: {start_result.stderr}")
                return False
            
            print("  ✅ Services started successfully")
            
            # Wait for services to initialize
            print("  Waiting for services to initialize...")
            await asyncio.sleep(30)
            
            return True
            
        except Exception as e:
            print(f"❌ Service startup failed: {e}")
            return False
    
    async def _setup_ollama_models(self) -> bool:
        """Setup required Ollama models."""
        print("\n🤖 Setting Up Ollama Models")
        print("-" * 40)
        
        try:
            # Wait for Ollama service to be ready
            for attempt in range(10):
                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=5)
                    if response.status_code == 200:
                        break
                except requests.RequestException:
                    pass
                
                print(f"  Waiting for Ollama service... (attempt {attempt + 1}/10)")
                await asyncio.sleep(10)
            else:
                print("❌ Ollama service not ready")
                return False
            
            print("  ✅ Ollama service is ready")
            
            # Check and pull required models
            for model in self.required_models:
                print(f"  Checking model: {model}")
                
                if await self._is_model_available(model):
                    print(f"    ✅ {model} already available")
                else:
                    print(f"    🔄 Pulling {model}...")
                    
                    if await self._pull_model(model):
                        print(f"    ✅ {model} pulled successfully")
                    else:
                        print(f"    ❌ Failed to pull {model}")
                        return False
            
            return True
            
        except Exception as e:
            print(f"❌ Ollama setup failed: {e}")
            return False
    
    async def _is_model_available(self, model_name: str) -> bool:
        """Check if a model is available locally."""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get('models', [])
                return any(model.get('name', '').startswith(model_name) for model in models)
            return False
        except Exception:
            return False
    
    async def _pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry."""
        try:
            process = subprocess.Popen(
                ["docker", "exec", "rug_detector_ollama_dev", "ollama", "pull", model_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # Wait for completion with timeout
            try:
                process.wait(timeout=600)  # 10 minutes timeout
                return process.returncode == 0
            except subprocess.TimeoutExpired:
                process.kill()
                return False
                
        except Exception:
            return False
    
    async def _validate_system_health(self) -> bool:
        """Validate system health."""
        print("\n🏥 System Health Validation")
        print("-" * 40)
        
        for service, endpoint in self.health_checks.items():
            print(f"  Checking {service}...", end=" ")
            
            if await self._check_service_health(service, endpoint):
                print("✅")
            else:
                print("❌")
                return False
        
        print("✅ All services are healthy")
        return True
    
    async def _check_service_health(self, service: str, endpoint: str) -> bool:
        """Check individual service health."""
        try:
            if service == 'postgres':
                # Check PostgreSQL connection
                import psycopg2
                conn = psycopg2.connect(endpoint)
                conn.close()
                return True
            
            elif service == 'redis':
                # Check Redis connection
                import redis
                r = redis.from_url(endpoint)
                r.ping()
                return True
            
            elif service in ['ollama', 'rug_detector']:
                # Check HTTP endpoints
                response = requests.get(endpoint, timeout=10)
                return response.status_code == 200
            
            return False
            
        except Exception:
            return False
    
    async def _run_validation_tests(self) -> bool:
        """Run validation tests."""
        print("\n🧪 Running Validation Tests")
        print("-" * 40)
        
        try:
            # Run LangGraph validation
            print("  Running LangGraph multi-agent validation...")
            
            validation_result = subprocess.run(
                [sys.executable, "scripts/langgraph_validation.py"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=600
            )
            
            if validation_result.returncode == 0:
                print("  ✅ Validation tests passed")
                return True
            else:
                print(f"  ❌ Validation tests failed: {validation_result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Validation test execution failed: {e}")
            return False
    
    async def _print_deployment_summary(self):
        """Print deployment summary."""
        print("\n📋 DEPLOYMENT SUMMARY")
        print("=" * 70)
        print("🎯 LangGraph Multi-Agent System Status: OPERATIONAL")
        print("")
        print("🔗 Service Endpoints:")
        print("  • Rug Detector API: http://localhost:8000")
        print("  • Ollama API: http://localhost:11434")
        print("  • PostgreSQL: localhost:5432")
        print("  • Redis: localhost:6379")
        print("")
        print("🤖 Available AI Models:")
        for model in self.required_models:
            print(f"  • {model}")
        print("")
        print("📊 Multi-Agent Architecture:")
        print("  • Pattern Analysis Agent: ✅ Active")
        print("  • Market Data Agent: ✅ Active")
        print("  • Social Sentiment Agent: ✅ Active")
        print("  • Blockchain Monitor Agent: ✅ Active")
        print("  • Risk Coordinator Agent: ✅ Active")
        print("")
        print("🚀 Next Steps:")
        print("  1. Test the system: python scripts/langgraph_validation.py")
        print("  2. Monitor logs: docker-compose logs -f")
        print("  3. Access API docs: http://localhost:8000/docs")
        print("")
        print("🎉 LangGraph Multi-Agent Rug Detection System is READY!")


async def main():
    """Main deployment entry point."""
    deployment = LangGraphDeployment()
    
    success = await deployment.deploy_system()
    
    if success:
        print("\n✅ DEPLOYMENT COMPLETED SUCCESSFULLY")
        return True
    else:
        print("\n❌ DEPLOYMENT FAILED")
        print("Check logs and resolve issues before retrying.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
