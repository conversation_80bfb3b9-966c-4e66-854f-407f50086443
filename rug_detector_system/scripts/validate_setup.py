#!/usr/bin/env python3
"""
Phase 1.1 Validation Script - Project Structure & Environment Setup

This script validates that Phase 1.1 has been completed successfully with
99.9th percentile quality standards for M1 Mac development.

Validation Criteria:
- Production-grade directory structure
- Python 3.11+ virtual environment
- Development tools properly configured
- Pre-commit hooks installed and working
- M1 Mac compatibility verified
- All quality checks passing
"""

import os
import os
import sys
import subprocess
import platform
from pathlib import Path
from typing import List, Tuple, Dict, Any


class SetupValidator:
    """Comprehensive setup validation for Phase 1.1 completion."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.success_count = 0
        self.total_checks = 0
    
    def validate_all(self) -> bool:
        """Run all validation checks."""
        print("🔍 Phase 1.1 Validation: Project Structure & Environment Setup")
        print("=" * 70)
        
        # Core validation checks
        self._validate_directory_structure()
        self._validate_python_environment()
        self._validate_virtual_environment()
        self._validate_development_tools()
        self._validate_configuration_files()
        self._validate_pre_commit_setup()
        self._validate_m1_compatibility()
        self._validate_code_quality()
        
        # Summary
        self._print_summary()
        
        return len(self.errors) == 0
    
    def _check(self, description: str, condition: bool, 
               error_msg: str = None, warning_msg: str = None) -> bool:
        """Helper method to perform a validation check."""
        self.total_checks += 1
        
        if condition:
            print(f"✅ {description}")
            self.success_count += 1
            return True
        else:
            print(f"❌ {description}")
            if error_msg:
                self.errors.append(error_msg)
            if warning_msg:
                self.warnings.append(warning_msg)
            return False
    
    def _validate_directory_structure(self):
        """Validate production-grade directory structure."""
        print("\n📁 Directory Structure Validation")
        print("-" * 40)
        
        required_dirs = [
            "src",
            "src/analysis", 
            "src/blockchain",
            "tests",
            "docs",
            "scripts",
            "config"
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            self._check(
                f"Directory exists: {dir_path}",
                full_path.exists() and full_path.is_dir(),
                f"Missing required directory: {dir_path}"
            )
        
        # Check for __init__.py files
        init_files = [
            "src/__init__.py",
            "src/analysis/__init__.py", 
            "src/blockchain/__init__.py"
        ]
        
        for init_file in init_files:
            full_path = self.project_root / init_file
            self._check(
                f"Python package file: {init_file}",
                full_path.exists() and full_path.is_file(),
                f"Missing __init__.py file: {init_file}"
            )
    
    def _validate_python_environment(self):
        """Validate Python version and M1 Mac compatibility."""
        print("\n🐍 Python Environment Validation")
        print("-" * 40)
        
        # Python version
        python_version = sys.version_info
        self._check(
            f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}",
            python_version >= (3, 11),
            f"Python 3.11+ required, found {python_version.major}.{python_version.minor}"
        )
        
        # Architecture check
        arch = platform.machine()
        self._check(
            f"Architecture: {arch}",
            arch == "arm64",
            None,
            f"Expected arm64 for M1 Mac, found {arch}"
        )
        
        # Platform check
        system = platform.system()
        self._check(
            f"Operating System: {system}",
            system == "Darwin",
            None,
            f"Expected Darwin (macOS), found {system}"
        )
    
    def _validate_virtual_environment(self):
        """Validate virtual environment setup."""
        print("\n🔧 Virtual Environment Validation")
        print("-" * 40)
        
        # Check if in virtual environment
        venv_active = hasattr(sys, 'real_prefix') or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
        
        self._check(
            "Virtual environment active",
            venv_active,
            "Virtual environment not activated"
        )
        
        # Check venv directory exists
        venv_path = self.project_root / "venv"
        self._check(
            "Virtual environment directory exists",
            venv_path.exists() and venv_path.is_dir(),
            "Virtual environment directory not found"
        )
        
        # Check pip is available
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True)
            self._check(
                "pip available in virtual environment",
                result.returncode == 0,
                "pip not available in virtual environment"
            )
        except Exception as e:
            self._check("pip available", False, f"Error checking pip: {e}")
    
    def _validate_development_tools(self):
        """Validate development tools installation."""
        print("\n🛠️  Development Tools Validation")
        print("-" * 40)
        
        tools = {
            "black": "Code formatter",
            "isort": "Import sorter", 
            "flake8": "Linter",
            "mypy": "Type checker",
            "pytest": "Test runner",
            "bandit": "Security scanner",
            "safety": "Dependency scanner",
            "pre-commit": "Pre-commit hooks"
        }
        
        for tool, description in tools.items():
            try:
                result = subprocess.run([sys.executable, "-m", tool, "--version"],
                                      capture_output=True, text=True)
                if result.returncode != 0:
                    # Try direct command
                    result = subprocess.run([tool, "--version"],
                                          capture_output=True, text=True)
                
                self._check(
                    f"{description} ({tool})",
                    result.returncode == 0,
                    f"{tool} not installed or not working"
                )
            except Exception:
                self._check(
                    f"{description} ({tool})",
                    False,
                    f"{tool} not available"
                )
    
    def _validate_configuration_files(self):
        """Validate configuration files."""
        print("\n📋 Configuration Files Validation")
        print("-" * 40)
        
        config_files = {
            "pyproject.toml": "Project configuration",
            ".gitignore": "Git ignore rules",
            ".flake8": "Flake8 configuration", 
            ".pre-commit-config.yaml": "Pre-commit configuration",
            "Makefile": "Development automation",
            "README.md": "Project documentation"
        }
        
        for file_name, description in config_files.items():
            file_path = self.project_root / file_name
            self._check(
                f"{description} ({file_name})",
                file_path.exists() and file_path.is_file(),
                f"Missing configuration file: {file_name}"
            )
    
    def _validate_pre_commit_setup(self):
        """Validate pre-commit hooks setup."""
        print("\n🪝 Pre-commit Hooks Validation")
        print("-" * 40)
        
        # Check .git directory exists
        git_dir = self.project_root / ".git"
        self._check(
            "Git repository initialized",
            git_dir.exists() and git_dir.is_dir(),
            "Git repository not initialized"
        )
        
        # Check pre-commit hook installed
        pre_commit_hook = git_dir / "hooks" / "pre-commit"
        self._check(
            "Pre-commit hook installed",
            pre_commit_hook.exists(),
            "Pre-commit hook not installed"
        )
    
    def _validate_m1_compatibility(self):
        """Validate M1 Mac specific optimizations."""
        print("\n🚀 M1 Mac Compatibility Validation")
        print("-" * 40)
        
        # Check architecture
        arch = platform.machine()
        self._check(
            "ARM64 architecture detected",
            arch == "arm64",
            None,
            "Not running on ARM64 architecture"
        )
        
        # Check Python is ARM64 native
        try:
            import sysconfig
            platform_tag = sysconfig.get_platform()
            self._check(
                f"Python platform: {platform_tag}",
                "arm64" in platform_tag or "universal2" in platform_tag,
                None,
                "Python may not be ARM64 optimized"
            )
        except Exception:
            self._check("Python platform check", False, "Could not determine Python platform")
    
    def _validate_code_quality(self):
        """Validate code quality tools are working."""
        print("\n✨ Code Quality Validation")
        print("-" * 40)
        
        # Test formatting check
        try:
            result = subprocess.run([
                sys.executable, "-m", "black", "--check", "src/"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            self._check(
                "Code formatting (Black)",
                result.returncode == 0,
                "Code formatting issues detected"
            )
        except Exception as e:
            self._check("Code formatting check", False, f"Error running Black: {e}")
        
        # Test linting
        try:
            result = subprocess.run([
                sys.executable, "-m", "flake8", "src/"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            self._check(
                "Code linting (flake8)",
                result.returncode == 0,
                "Linting issues detected"
            )
        except Exception as e:
            self._check("Code linting check", False, f"Error running flake8: {e}")
        
        # Test type checking
        try:
            env = os.environ.copy()
            env['MYPYPATH'] = 'src'
            result = subprocess.run([
                sys.executable, "-m", "mypy", "--explicit-package-bases", "src/"
            ], capture_output=True, text=True, cwd=self.project_root, env=env)

            self._check(
                "Type checking (mypy)",
                result.returncode == 0,
                "Type checking issues detected"
            )
        except Exception as e:
            self._check("Type checking", False, f"Error running mypy: {e}")
    
    def _print_summary(self):
        """Print validation summary."""
        print("\n" + "=" * 70)
        print("📊 VALIDATION SUMMARY")
        print("=" * 70)
        
        success_rate = (self.success_count / self.total_checks) * 100
        
        print(f"✅ Successful checks: {self.success_count}/{self.total_checks}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        print("\n" + "=" * 70)
        
        if len(self.errors) == 0:
            print("🎉 PHASE 1.1 VALIDATION SUCCESSFUL!")
            print("✨ 99.9th percentile quality standards achieved")
            print("🚀 Ready to proceed to Phase 1.2")
        else:
            print("❌ PHASE 1.1 VALIDATION FAILED")
            print("🔧 Please address the errors above before proceeding")
        
        print("=" * 70)


def main():
    """Main validation entry point."""
    validator = SetupValidator()
    success = validator.validate_all()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
