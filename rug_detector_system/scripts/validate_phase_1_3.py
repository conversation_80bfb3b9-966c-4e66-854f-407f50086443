#!/usr/bin/env python3
"""
Phase 1.3 Validation Script - Configuration Management System

This script validates the core functionality of the configuration management
system implemented in Phase 1.3.

Validation Criteria:
- Configuration loading and validation
- Environment file processing
- API key management
- Security configuration
- Production readiness
"""

import sys
import asyncio
import time
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from config import ConfigurationManager, Environment
from api_validator import APIKeyValidator


class Phase13Validator:
    """Phase 1.3 specific validation."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
        
    def validate_all(self) -> bool:
        """Run all Phase 1.3 validation checks."""
        print("🔍 Phase 1.3 Validation: Configuration Management System")
        print("=" * 70)
        
        # Core validation checks
        self._validate_configuration_system()
        self._validate_environment_handling()
        self._validate_api_key_management()
        self._validate_security_features()
        
        # API connectivity test
        asyncio.run(self._validate_api_connectivity())
        
        # Summary
        self._print_summary()
        
        return len(self.errors) == 0
    
    def _check(self, description: str, condition: bool, 
               error_msg: str = None, warning_msg: str = None) -> bool:
        """Helper method to perform a validation check."""
        self.total_checks += 1
        
        if condition:
            print(f"✅ {description}")
            self.success_count += 1
            return True
        else:
            print(f"❌ {description}")
            if error_msg:
                self.errors.append(error_msg)
            if warning_msg:
                self.warnings.append(warning_msg)
            return False
    
    def _validate_configuration_system(self):
        """Validate core configuration system functionality."""
        print("\n⚙️  Configuration System Validation")
        print("-" * 40)
        
        try:
            # Test configuration loading
            config_manager = ConfigurationManager()
            config = config_manager.load_configuration()
            
            self._check(
                "Configuration manager instantiation",
                config_manager is not None,
                "Failed to create configuration manager"
            )
            
            self._check(
                "Configuration loading",
                config is not None,
                "Failed to load configuration"
            )
            
            self._check(
                "Configuration validation",
                hasattr(config, 'environment') and hasattr(config, 'database'),
                "Configuration structure invalid"
            )
            
            # Store config for further tests
            self.config = config
            
        except Exception as e:
            self.errors.append(f"Configuration system error: {e}")
            print(f"❌ Configuration system error: {e}")
            self.config = None
    
    def _validate_environment_handling(self):
        """Validate environment-specific configuration handling."""
        print("\n🌍 Environment Handling Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate environment - configuration not loaded")
            return
        
        config = self.config
        
        self._check(
            f"Environment detection: {config.environment.value}",
            config.environment in Environment,
            "Invalid environment configuration"
        )
        
        self._check(
            f"Debug mode: {config.debug}",
            isinstance(config.debug, bool),
            "Debug mode not properly configured"
        )
        
        # Validate environment-specific settings
        if config.environment == Environment.DEVELOPMENT:
            self._check(
                "Development environment properly configured",
                True,  # Development is flexible
                None
            )
        elif config.environment == Environment.PRODUCTION:
            self._check(
                "Production security requirements",
                not config.debug,
                "Production should not have debug enabled"
            )
    
    def _validate_api_key_management(self):
        """Validate API key management functionality."""
        print("\n🔑 API Key Management Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate API keys - configuration not loaded")
            return
        
        api_keys = self.config.api_keys
        
        # Check required API keys
        required_keys = [
            ('etherscan_api_key', 'Etherscan'),
            ('coingecko_api_key', 'CoinGecko'),
            ('dune_api_key', 'Dune Analytics'),
            ('coin_api_key', 'CoinAPI')
        ]
        
        for key_attr, key_name in required_keys:
            key_value = getattr(api_keys, key_attr, '')
            self._check(
                f"{key_name} API key configured",
                len(key_value) > 10 and not key_value.startswith('your_'),
                f"{key_name} API key not properly configured"
            )
        
        # Check optional API keys
        optional_keys = [
            ('binance_api_key', 'Binance'),
            ('alpha_vantage_api_key', 'Alpha Vantage'),
            ('thirdweb_client_id', 'Thirdweb')
        ]
        
        for key_attr, key_name in optional_keys:
            key_value = getattr(api_keys, key_attr, None)
            if key_value:
                self._check(
                    f"{key_name} API key (optional) configured",
                    len(key_value) > 5,
                    None,
                    f"{key_name} API key appears invalid"
                )
    
    def _validate_security_features(self):
        """Validate security configuration features."""
        print("\n🔒 Security Features Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate security - configuration not loaded")
            return
        
        security = self.config.security
        
        self._check(
            "Rate limiting configured",
            security.api_rate_limit_per_minute > 0,
            "Rate limiting not configured"
        )
        
        self._check(
            "Request size limits configured",
            security.max_request_size_mb > 0,
            "Request size limits not configured"
        )
        
        self._check(
            "Session timeout configured",
            security.session_timeout_minutes > 0,
            "Session timeout not configured"
        )
        
        # Check production security requirements
        if self.config.environment == Environment.PRODUCTION:
            self._check(
                "Production JWT secret configured",
                security.jwt_secret_key is not None,
                "Production requires JWT secret"
            )
            
            self._check(
                "Production encryption key configured",
                security.encryption_key is not None,
                "Production requires encryption key"
            )
    
    async def _validate_api_connectivity(self):
        """Validate API connectivity functionality."""
        print("\n🌐 API Connectivity Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate API connectivity - configuration not loaded")
            return
        
        try:
            # Test API validator functionality
            validator = APIKeyValidator(self.config.api_keys, timeout=10)
            result = await validator.validate_all_apis()
            
            self._check(
                "API validator instantiation",
                validator is not None,
                "Failed to create API validator"
            )
            
            self._check(
                f"API validation execution ({result.total_apis} APIs tested)",
                result.total_apis > 0,
                "No APIs were tested"
            )
            
            # Check critical API connectivity
            critical_apis = ['Etherscan', 'CoinGecko', 'CrossRef']
            critical_working = sum(1 for status in result.api_statuses 
                                 if status.name in critical_apis and status.is_valid)
            
            self._check(
                f"Critical API connectivity ({critical_working}/{len(critical_apis)} working)",
                critical_working >= 2,  # At least 2 critical APIs should work
                "Too many critical API failures"
            )
            
            self._check(
                f"Overall API success rate: {(result.valid_apis/result.total_apis)*100:.1f}%",
                result.valid_apis >= result.total_apis * 0.5,  # At least 50% should work
                "API success rate too low"
            )
            
        except Exception as e:
            self.errors.append(f"API connectivity validation failed: {e}")
            print(f"❌ API connectivity validation failed: {e}")
    
    def _print_summary(self):
        """Print validation summary."""
        print("\n" + "=" * 70)
        print("📊 PHASE 1.3 VALIDATION SUMMARY")
        print("=" * 70)
        
        success_rate = (self.success_count / self.total_checks) * 100
        
        print(f"✅ Successful checks: {self.success_count}/{self.total_checks}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        
        if hasattr(self, 'config') and self.config:
            print(f"🌍 Environment: {self.config.environment.value}")
            print(f"🐛 Debug mode: {self.config.debug}")
            print(f"📦 Application: {self.config.app_name} v{self.config.app_version}")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        print("\n" + "=" * 70)
        
        if len(self.errors) == 0:
            print("🎉 PHASE 1.3 VALIDATION SUCCESSFUL!")
            print("✨ Configuration management system fully operational")
            print("🔒 Security configuration validated")
            print("🌐 API connectivity confirmed")
            print("⚙️  Environment properly configured")
            print("🎯 Ready to proceed to Phase 1.4")
        else:
            print("❌ PHASE 1.3 VALIDATION FAILED")
            print("🔧 Please address the errors above before proceeding")
        
        print("=" * 70)


def main():
    """Main validation entry point."""
    validator = Phase13Validator()
    success = validator.validate_all()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
