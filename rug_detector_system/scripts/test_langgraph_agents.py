#!/usr/bin/env python3
"""
Test LangGraph Multi-Agent System

Quick test to validate the LangGraph multi-agent system is working
with the most powerful available models.

Author: MLDevOps Architect
Version: 4.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from agents.simplified_langgraph_integration import analyze_contract_with_simplified_agents
from logging_config import get_logger


async def test_multi_agent_system():
    """Test the multi-agent system with a sample contract."""
    logger = get_logger(__name__)
    
    print("🧪 TESTING LANGGRAPH MULTI-AGENT SYSTEM")
    print("=" * 60)
    print("Using most powerful available models:")
    print("  • DeepSeek R1 (Reasoning)")
    print("  • CodeLlama (Code Analysis)")
    print("  • Llama 3.1 (General Tasks)")
    print("")
    
    # Test contract (known rug-pull example)
    test_contract = "0x1234567890123456789012345678901234567890"
    test_code = '''
    pragma solidity ^0.8.0;
    
    contract TestRugPull {
        address private owner;
        bool public tradingEnabled = false;
        mapping(address => bool) public blacklisted;
        mapping(address => uint256) public balanceOf;
        
        modifier onlyOwner() {
            require(msg.sender == owner, "Not owner");
            _;
        }
        
        constructor() {
            owner = msg.sender;
        }
        
        function transfer(address to, uint256 value) public returns (bool) {
            require(tradingEnabled, "Trading disabled");
            require(!blacklisted[msg.sender], "Address blacklisted");
            if (msg.sender != owner) {
                revert("Transfer not allowed");
            }
            balanceOf[msg.sender] -= value;
            balanceOf[to] += value;
            return true;
        }
        
        function rugPull() public onlyOwner {
            payable(owner).transfer(address(this).balance);
            tradingEnabled = false;
        }
        
        function emergencyWithdraw() public onlyOwner {
            payable(owner).transfer(address(this).balance);
        }
    }
    '''
    
    try:
        print("🔄 Starting multi-agent analysis...")
        start_time = time.time()
        
        # Run multi-agent analysis
        result = await analyze_contract_with_simplified_agents(test_contract, test_code)
        
        analysis_time = time.time() - start_time
        
        print(f"✅ Analysis completed in {analysis_time:.2f} seconds")
        print("")
        
        # Print results
        print("📊 MULTI-AGENT ANALYSIS RESULTS:")
        print("-" * 40)
        
        # Agent-specific results
        agent_analyses = result.get('agent_analyses', {})
        
        pattern_analysis = agent_analyses.get('pattern_analyst', {})
        if pattern_analysis:
            print(f"🔍 Pattern Analysis Agent:")
            print(f"   Risk Level: {pattern_analysis.get('recommended_risk_level', 'unknown')}")
            print(f"   Confidence: {pattern_analysis.get('confidence', 0):.2f}")
            print(f"   Risk Factors: {len(pattern_analysis.get('risk_factors', []))}")
        
        market_analysis = agent_analyses.get('market_data_analyst', {})
        if market_analysis:
            print(f"📈 Market Data Agent:")
            print(f"   Market Risk: {market_analysis.get('market_risk_level', 'unknown')}")
            print(f"   Confidence: {market_analysis.get('confidence', 0):.2f}")
        
        social_analysis = agent_analyses.get('social_sentiment_analyst', {})
        if social_analysis:
            print(f"🌐 Social Sentiment Agent:")
            print(f"   Social Risk: {social_analysis.get('social_risk_level', 'unknown')}")
            print(f"   Confidence: {social_analysis.get('confidence', 0):.2f}")
        
        # Note: Simplified version doesn't include blockchain monitor
        
        # Final coordinated results
        print(f"\n🎯 FINAL COORDINATED ASSESSMENT:")
        print("-" * 40)
        print(f"Final Risk Score: {result.get('final_risk_score', 0):.3f}")
        print(f"Recommendation: {result.get('final_recommendation', 'unknown')}")
        print(f"Overall Confidence: {result.get('confidence_level', 0):.3f}")
        print(f"Analysis Complete: {result.get('analysis_complete', False)}")
        
        # Validate results
        success = (
            result.get('final_risk_score', 0) > 0.5 and  # Should detect high risk
            result.get('confidence_level', 0) > 0.7 and  # Should be confident
            result.get('analysis_complete', False) and    # Should complete
            len(agent_analyses) >= 3                      # Multiple agents should respond
        )
        
        if success:
            print("\n🎉 MULTI-AGENT SYSTEM TEST SUCCESSFUL!")
            print("✅ All agents functioning correctly")
            print("✅ Risk detection working properly")
            print("✅ Coordination and synthesis operational")
            print("✅ Using most powerful available models")
        else:
            print("\n⚠️  MULTI-AGENT SYSTEM TEST ISSUES DETECTED")
            print("❌ Some agents may not be functioning optimally")
            
        return success
        
    except Exception as e:
        logger.error(f"Multi-agent test failed: {e}")
        print(f"\n❌ TEST FAILED: {e}")
        return False


async def main():
    """Main test entry point."""
    print("🚀 LANGGRAPH MULTI-AGENT SYSTEM TEST")
    print("Using DeepSeek R1, CodeLlama, and Llama 3.1 models")
    print("")
    
    success = await test_multi_agent_system()
    
    if success:
        print("\n✅ ALL TESTS PASSED")
        print("🚀 LangGraph multi-agent system is ready for production!")
        return True
    else:
        print("\n❌ TESTS FAILED")
        print("Please check the system configuration and try again.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
