#!/usr/bin/env python3
"""
Comprehensive Validation Execution Script

This script executes the complete real-world validation suite including:
- Live blockchain data testing
- Performance benchmarking
- Accuracy analysis
- Production readiness assessment

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import sys
import time
import json
from pathlib import Path
from datetime import datetime

# Add src and tests to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))
sys.path.insert(0, str(Path(__file__).parent.parent / 'tests'))

from real_world_validation import RealWorldValidator


class ComprehensiveValidationRunner:
    """Comprehensive validation execution and reporting."""
    
    def __init__(self):
        """Initialize validation runner."""
        self.start_time = None
        self.end_time = None
        self.validation_results = {}
        
    async def run_full_validation_suite(self) -> bool:
        """Run the complete validation suite."""
        print("🚀 COMPREHENSIVE RUG DETECTOR VALIDATION SUITE")
        print("=" * 80)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        self.start_time = time.time()
        
        try:
            # Phase 1: System Initialization Check
            print("\n📋 Phase 1: System Initialization Validation")
            print("-" * 50)
            init_success = await self._validate_system_initialization()
            if not init_success:
                print("❌ System initialization failed - aborting validation")
                return False
            print("✅ System initialization successful")
            
            # Phase 2: Real-World Data Testing
            print("\n🌐 Phase 2: Real-World Blockchain Data Testing")
            print("-" * 50)
            validator = RealWorldValidator()
            self.validation_results = await validator.run_comprehensive_validation()
            
            # Phase 3: Results Analysis and Reporting
            print("\n📊 Phase 3: Results Analysis and Reporting")
            print("-" * 50)
            success = self._analyze_and_report_results()
            
            self.end_time = time.time()
            
            # Phase 4: Final Assessment
            print("\n🎯 Phase 4: Final Production Readiness Assessment")
            print("-" * 50)
            production_ready = self._final_production_assessment()
            
            return production_ready
            
        except Exception as e:
            print(f"\n❌ VALIDATION SUITE FAILED: {e}")
            return False
    
    async def _validate_system_initialization(self) -> bool:
        """Validate that all system components can initialize properly."""
        try:
            # Test database connection
            print("  🗄️  Testing database connection...")
            from database import get_database_manager
            db_manager = get_database_manager()
            if not db_manager:
                print("    ❌ Database manager initialization failed")
                return False
            print("    ✅ Database connection successful")
            
            # Test cache connection
            print("  🔄 Testing cache connection...")
            from cache import get_cache_manager
            cache_manager = await get_cache_manager()
            if not cache_manager:
                print("    ❌ Cache manager initialization failed")
                return False
            print("    ✅ Cache connection successful")
            
            # Test Web3 connections
            print("  🌐 Testing Web3 connections...")
            from blockchain import get_web3_manager
            web3_manager = await get_web3_manager()
            if not web3_manager:
                print("    ❌ Web3 manager initialization failed")
                return False
            print("    ✅ Web3 connections successful")
            
            # Test analysis engines
            print("  🔍 Testing analysis engines...")
            from analysis import get_static_analyzer, get_risk_scorer
            static_analyzer = await get_static_analyzer()
            risk_scorer = await get_risk_scorer()
            if not static_analyzer or not risk_scorer:
                print("    ❌ Analysis engines initialization failed")
                return False
            print("    ✅ Analysis engines ready")
            
            return True
            
        except Exception as e:
            print(f"    ❌ System initialization error: {e}")
            return False
    
    def _analyze_and_report_results(self) -> bool:
        """Analyze validation results and generate detailed reports."""
        try:
            # Extract key metrics
            test_report = self.validation_results.get('test_execution_report', {})
            performance_report = self.validation_results.get('performance_benchmark_report', {})
            accuracy_report = self.validation_results.get('accuracy_analysis_report', {})
            issues = self.validation_results.get('issue_registry', [])
            
            # Print Test Execution Summary
            print("  📋 Test Execution Summary:")
            summary = test_report.get('summary', {})
            print(f"    • Total contracts tested: {summary.get('total_contracts_tested', 0)}")
            print(f"    • Successful tests: {summary.get('successful_tests', 0)}")
            print(f"    • Failed tests: {summary.get('failed_tests', 0)}")
            print(f"    • Passed tests: {summary.get('passed_tests', 0)}")
            print(f"    • Average analysis time: {summary.get('average_analysis_time', 0):.2f}s")
            
            # Print Performance Metrics
            print("\n  ⚡ Performance Metrics:")
            response_times = performance_report.get('response_times', {})
            print(f"    • Average response time: {response_times.get('average_seconds', 0):.2f}s")
            print(f"    • 95th percentile: {response_times.get('percentile_95_seconds', 0):.2f}s")
            print(f"    • Meets SLA target: {response_times.get('meets_sla_target', False)}")
            
            throughput = performance_report.get('throughput_metrics', {})
            print(f"    • Throughput: {throughput.get('contracts_per_minute', 0):.1f} contracts/min")
            
            # Print Accuracy Analysis
            print("\n  🎯 Accuracy Analysis:")
            confusion_matrix = accuracy_report.get('confusion_matrix', {})
            metrics = accuracy_report.get('classification_metrics', {})
            
            print(f"    • True Positives: {confusion_matrix.get('true_positives', 0)}")
            print(f"    • False Positives: {confusion_matrix.get('false_positives', 0)}")
            print(f"    • True Negatives: {confusion_matrix.get('true_negatives', 0)}")
            print(f"    • False Negatives: {confusion_matrix.get('false_negatives', 0)}")
            print(f"    • Accuracy: {metrics.get('accuracy', 0):.1%}")
            print(f"    • Precision: {metrics.get('precision', 0):.1%}")
            print(f"    • Recall: {metrics.get('recall', 0):.1%}")
            print(f"    • F1-Score: {metrics.get('f1_score', 0):.3f}")
            print(f"    • False Positive Rate: {metrics.get('false_positive_rate', 0):.1%}")
            
            # Print Issues Found
            print(f"\n  🐛 Issues Identified: {len(issues)}")
            critical_issues = [i for i in issues if i.get('severity') == 'Critical']
            high_issues = [i for i in issues if i.get('severity') == 'High']
            
            print(f"    • Critical: {len(critical_issues)}")
            print(f"    • High: {len(high_issues)}")
            print(f"    • Medium/Low: {len(issues) - len(critical_issues) - len(high_issues)}")
            
            if critical_issues:
                print("\n    Critical Issues:")
                for issue in critical_issues[:3]:  # Show first 3
                    print(f"      - {issue.get('description', 'Unknown issue')}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Results analysis failed: {e}")
            return False
    
    def _final_production_assessment(self) -> bool:
        """Perform final production readiness assessment."""
        try:
            assessment = self.validation_results.get('production_readiness_assessment', {})
            
            print("  🎯 Production Readiness Criteria:")
            criteria = assessment.get('criteria_met', {})
            
            for criterion, met in criteria.items():
                status = "✅" if met else "❌"
                print(f"    {status} {criterion.replace('_', ' ').title()}: {met}")
            
            overall_status = assessment.get('overall_status', 'NOT_READY')
            go_no_go = assessment.get('go_no_go_decision', 'NO_GO')
            
            print(f"\n  📊 Overall Status: {overall_status}")
            print(f"  🚦 Go/No-Go Decision: {go_no_go}")
            
            if go_no_go == 'NO_GO':
                print("\n  📝 Recommendations for Production Readiness:")
                recommendations = assessment.get('recommendations', [])
                for i, rec in enumerate(recommendations, 1):
                    print(f"    {i}. {rec}")
                
                print("\n  ⚠️  Known Limitations:")
                limitations = assessment.get('limitations', [])
                for limitation in limitations:
                    print(f"    • {limitation}")
            
            # Calculate total validation time
            total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
            print(f"\n  ⏱️  Total validation time: {total_time:.1f} seconds")
            
            return go_no_go == 'GO'
            
        except Exception as e:
            print(f"  ❌ Final assessment failed: {e}")
            return False
    
    def print_final_summary(self, production_ready: bool):
        """Print final validation summary."""
        print("\n" + "=" * 80)
        print("🏁 COMPREHENSIVE VALIDATION COMPLETE")
        print("=" * 80)
        
        if production_ready:
            print("🎉 RESULT: SYSTEM IS PRODUCTION READY!")
            print("\n✅ The Rug Detector System has passed comprehensive validation:")
            print("   • Real-world blockchain data testing completed successfully")
            print("   • Performance benchmarks meet production requirements")
            print("   • Accuracy metrics exceed quality thresholds")
            print("   • No critical issues blocking production deployment")
            print("\n🚀 The system is ready for production deployment.")
            
        else:
            print("❌ RESULT: SYSTEM NOT READY FOR PRODUCTION")
            print("\n🔧 The Rug Detector System requires additional work:")
            print("   • Critical issues must be resolved")
            print("   • Performance or accuracy improvements needed")
            print("   • Additional testing and validation required")
            print("\n⚠️  DO NOT deploy to production until issues are resolved.")
        
        print(f"\n📄 Detailed reports saved in validation_report_*.json")
        print(f"📊 Test results saved in test_results_*.csv")
        print("=" * 80)


async def main():
    """Main validation execution."""
    runner = ComprehensiveValidationRunner()
    
    try:
        production_ready = await runner.run_full_validation_suite()
        runner.print_final_summary(production_ready)
        
        # Exit with appropriate code
        sys.exit(0 if production_ready else 1)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Validation interrupted by user")
        sys.exit(2)
    except Exception as e:
        print(f"\n\n❌ Validation failed with error: {e}")
        sys.exit(3)


if __name__ == "__main__":
    asyncio.run(main())
