#!/usr/bin/env python3
"""
Phase 2.1 Mock Validation Script - Database Architecture & Models

This script validates the database architecture and models using SQLite
for testing without requiring PostgreSQL to be running.

Validation Criteria:
- Database model definitions
- SQLAlchemy configuration
- Connection management
- Table creation and schema validation
- Session management
- Basic data operations

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import sys
import asyncio
import tempfile
from pathlib import Path
from typing import List, Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from database.models import (
    Base, SmartContract, Transaction, AnalysisResult, RiskScore, 
    LiquidityPool, Alert, ChainType, RiskLevel, AnalysisStatus, AlertSeverity
)
from database.connection import DatabaseManager


class Phase21MockValidator:
    """Phase 2.1 validation using SQLite for testing."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
        self.temp_db = None
        
    def validate_all(self) -> bool:
        """Run all Phase 2.1 validation checks."""
        print("🔍 Phase 2.1 Mock Validation: Database Architecture & Models")
        print("=" * 70)
        print("📝 Using SQLite for testing (PostgreSQL not required)")
        print()
        
        # Core validation checks
        self._validate_model_definitions()
        self._validate_database_connection()
        self._validate_table_creation()
        asyncio.run(self._validate_health_checking())
        self._validate_session_management()
        self._validate_data_operations()
        
        # Summary
        self._print_summary()
        
        return len(self.errors) == 0
    
    def _check(self, description: str, condition: bool, 
               error_msg: str = None, warning_msg: str = None) -> bool:
        """Helper method to perform a validation check."""
        self.total_checks += 1
        
        if condition:
            print(f"✅ {description}")
            self.success_count += 1
            return True
        else:
            print(f"❌ {description}")
            if error_msg:
                self.errors.append(error_msg)
            if warning_msg:
                self.warnings.append(warning_msg)
            return False
    
    def _validate_model_definitions(self):
        """Validate SQLAlchemy model definitions."""
        print("\n🗄️  Model Definitions Validation")
        print("-" * 40)
        
        # Test model imports
        models = [SmartContract, Transaction, AnalysisResult, RiskScore, LiquidityPool, Alert]
        model_names = ["SmartContract", "Transaction", "AnalysisResult", "RiskScore", "LiquidityPool", "Alert"]
        
        for model, name in zip(models, model_names):
            self._check(
                f"Model {name} imported successfully",
                model is not None,
                f"Failed to import {name} model"
            )
        
        # Test enum imports
        enums = [ChainType, RiskLevel, AnalysisStatus, AlertSeverity]
        enum_names = ["ChainType", "RiskLevel", "AnalysisStatus", "AlertSeverity"]
        
        for enum, name in zip(enums, enum_names):
            self._check(
                f"Enum {name} imported successfully",
                enum is not None,
                f"Failed to import {name} enum"
            )
        
        # Test model attributes
        try:
            # Test SmartContract model
            self._check(
                "SmartContract has required attributes",
                hasattr(SmartContract, 'address') and hasattr(SmartContract, 'chain'),
                "SmartContract missing required attributes"
            )
            
            # Test relationships
            self._check(
                "SmartContract has relationships",
                hasattr(SmartContract, 'transactions') and hasattr(SmartContract, 'analysis_results'),
                "SmartContract missing relationships"
            )
            
            # Test table names
            self._check(
                "Models have correct table names",
                SmartContract.__tablename__ == "smart_contracts" and 
                Transaction.__tablename__ == "transactions",
                "Models have incorrect table names"
            )
            
        except Exception as e:
            self._check("Model attribute validation", False, f"Model validation failed: {e}")
    
    def _validate_database_connection(self):
        """Validate database connection management with SQLite."""
        print("\n🔌 Database Connection Validation (SQLite)")
        print("-" * 40)
        
        try:
            # Create temporary SQLite database
            self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
            sqlite_url = f"sqlite:///{self.temp_db.name}"
            
            # Test database manager creation
            manager = DatabaseManager(sqlite_url)
            self._check(
                "DatabaseManager instantiation",
                manager is not None,
                "Failed to create DatabaseManager"
            )
            
            # Test initialization
            init_success = manager.initialize()
            self._check(
                "Database connection initialization",
                init_success,
                "Failed to initialize database connection"
            )
            
            # Test engine creation
            self._check(
                "SQLAlchemy engine created",
                manager.engine is not None,
                "SQLAlchemy engine not created"
            )
            
            # Test session factory
            self._check(
                "Session factory created",
                manager.session_factory is not None,
                "Session factory not created"
            )
            
            # Store manager for later tests
            self.manager = manager
            
        except Exception as e:
            self._check("Database connection", False, f"Connection validation failed: {e}")
    
    def _validate_table_creation(self):
        """Validate table creation and schema."""
        print("\n🏗️  Table Creation Validation")
        print("-" * 40)
        
        try:
            if not hasattr(self, 'manager'):
                self._check("Table creation", False, "No database manager available")
                return
            
            # Test table creation
            creation_success = self.manager.create_tables()
            self._check(
                "Database tables creation",
                creation_success,
                "Failed to create database tables"
            )
            
            # Test table existence by checking metadata
            if self.manager.engine:
                # Get table names from metadata
                table_names = list(Base.metadata.tables.keys())
                expected_tables = [
                    "smart_contracts", "transactions", "analysis_results",
                    "risk_scores", "liquidity_pools", "alerts"
                ]
                
                for table_name in expected_tables:
                    self._check(
                        f"Table '{table_name}' defined in metadata",
                        table_name in table_names,
                        f"Table {table_name} not found in metadata"
                    )
                
                # Test table structure
                smart_contracts_table = Base.metadata.tables.get("smart_contracts")
                if smart_contracts_table is not None:
                    column_names = [col.name for col in smart_contracts_table.columns]
                    required_columns = ["id", "address", "chain", "name"]
                    
                    for col_name in required_columns:
                        self._check(
                            f"SmartContract table has '{col_name}' column",
                            col_name in column_names,
                            f"SmartContract table missing {col_name} column"
                        )
            
        except Exception as e:
            self._check("Table creation", False, f"Table creation validation failed: {e}")
    
    async def _validate_health_checking(self):
        """Validate database health checking."""
        print("\n🏥 Database Health Checking Validation")
        print("-" * 40)
        
        try:
            if not hasattr(self, 'manager'):
                self._check("Health checking", False, "No database manager available")
                return
            
            # Test manager health check
            manager_health = await self.manager.check_health()
            
            self._check(
                "Manager health check functional",
                manager_health is not None and "status" in manager_health,
                "Manager health check failed"
            )
            
            self._check(
                "Health check returns status",
                "status" in manager_health,
                "Health check missing status field"
            )
            
            self._check(
                "Health check returns response time",
                "response_time_ms" in manager_health,
                "Health check missing response time"
            )
            
            # Check pool status if available
            if "pool_status" in manager_health:
                pool_status = manager_health["pool_status"]
                self._check(
                    "Pool status information available",
                    isinstance(pool_status, dict) and "size" in pool_status,
                    "Pool status information incomplete"
                )
            
        except Exception as e:
            self._check("Health checking", False, f"Health check validation failed: {e}")
    
    def _validate_session_management(self):
        """Validate database session management."""
        print("\n🔄 Session Management Validation")
        print("-" * 40)
        
        try:
            if not hasattr(self, 'manager'):
                self._check("Session management", False, "No database manager available")
                return
            
            # Test session context manager
            with self.manager.get_session() as session:
                self._check(
                    "Session context manager functional",
                    session is not None,
                    "Failed to create session"
                )
                
                # Test basic query
                from sqlalchemy import text
                result = session.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                
                self._check(
                    "Session query execution",
                    row is not None and row[0] == 1,
                    "Session query failed"
                )
            
            # Test multiple sessions
            session_count = 0
            for i in range(3):
                with self.manager.get_session() as session:
                    session_count += 1
            
            self._check(
                f"Multiple sessions created ({session_count})",
                session_count == 3,
                "Failed to create multiple sessions"
            )
            
        except Exception as e:
            self._check("Session management", False, f"Session management validation failed: {e}")
    
    def _validate_data_operations(self):
        """Validate basic data operations."""
        print("\n💾 Data Operations Validation")
        print("-" * 40)
        
        try:
            if not hasattr(self, 'manager'):
                self._check("Data operations", False, "No database manager available")
                return
            
            # Test model instantiation
            contract = SmartContract(
                address="******************************************",
                chain=ChainType.ETHEREUM.value,
                name="Test Contract",
                symbol="TEST"
            )
            
            self._check(
                "Model instantiation",
                contract.address == "******************************************",
                "Failed to instantiate model"
            )
            
            # Test enum usage
            self._check(
                "Enum values accessible",
                ChainType.ETHEREUM.value == "ethereum" and RiskLevel.HIGH.value == "high",
                "Enum values not accessible"
            )
            
            # Test data insertion and retrieval
            with self.manager.get_session() as session:
                # Insert test data
                session.add(contract)
                session.flush()  # Get ID without committing
                
                contract_id = contract.id
                
                self._check(
                    "Data insertion",
                    contract_id is not None,
                    "Failed to insert test data"
                )
                
                # Query test data
                retrieved = session.query(SmartContract).filter_by(
                    address="******************************************"
                ).first()
                
                self._check(
                    "Data retrieval",
                    retrieved is not None and retrieved.name == "Test Contract",
                    "Failed to retrieve test data"
                )
                
                # Test relationship
                analysis = AnalysisResult(
                    analysis_id="test-analysis-123",
                    analysis_type="static",
                    contract_id=contract_id,
                    risk_level=RiskLevel.LOW.value,
                    confidence_score=0.85
                )
                
                session.add(analysis)
                session.flush()
                
                self._check(
                    "Relationship data insertion",
                    analysis.id is not None,
                    "Failed to insert relationship data"
                )
                
                # Test relationship query
                contract_with_analysis = session.query(SmartContract).filter_by(
                    id=contract_id
                ).first()
                
                analysis_count = len(contract_with_analysis.analysis_results)
                
                self._check(
                    "Relationship query",
                    analysis_count > 0,
                    "Failed to query relationship data"
                )
                
                # Rollback to avoid affecting database
                session.rollback()
            
        except Exception as e:
            self._check("Data operations", False, f"Data operations validation failed: {e}")
    
    def _print_summary(self):
        """Print validation summary."""
        print("\n" + "=" * 70)
        print("📊 PHASE 2.1 MOCK VALIDATION SUMMARY")
        print("=" * 70)
        
        success_rate = (self.success_count / self.total_checks) * 100
        
        print(f"✅ Successful checks: {self.success_count}/{self.total_checks}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        print(f"🗄️  Database: SQLite (for testing)")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        print("\n" + "=" * 70)
        
        if len(self.errors) == 0:
            print("🎉 PHASE 2.1 MOCK VALIDATION SUCCESSFUL!")
            print("✨ Database architecture and models fully operational")
            print("🗄️  All models defined and validated")
            print("🔌 Database connection management working")
            print("🏗️  Table creation and schema validated")
            print("💾 Data operations functional")
            print("🎯 Ready to proceed to Phase 2.2")
            print("\n📝 Note: This validation used SQLite for testing.")
            print("   For production, ensure PostgreSQL is configured and running.")
        else:
            print("❌ PHASE 2.1 MOCK VALIDATION FAILED")
            print("🔧 Please address the errors above before proceeding")
        
        print("=" * 70)
        
        # Cleanup
        if hasattr(self, 'manager'):
            self.manager.close()
        if self.temp_db:
            try:
                import os
                os.unlink(self.temp_db.name)
            except:
                pass


def main():
    """Main validation entry point."""
    validator = Phase21MockValidator()
    success = validator.validate_all()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
