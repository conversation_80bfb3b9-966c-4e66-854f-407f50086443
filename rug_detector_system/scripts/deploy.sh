#!/bin/bash

# Production Deployment Script for Rug Detector System
# Optimized for M1 Max local development environment
# Author: MLDevOps Architect
# Version: 2.0.0

set -euo pipefail

# Configuration
PROJECT_NAME="rug_detector_system"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.production"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed or not in PATH"
    fi
    
    # Check environment file
    if [[ ! -f "$ENV_FILE" ]]; then
        error "Environment file $ENV_FILE not found"
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
    fi
    
    success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p logs data backups monitoring/grafana/{dashboards,datasources} nginx/ssl
    
    success "Directories created"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
        
        mkdir -p "$BACKUP_PATH"
        
        # Backup database
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump -U rug_detector_user rug_detector > "$BACKUP_PATH/database.sql" 2>/dev/null; then
            log "Database backup created: $BACKUP_PATH/database.sql"
        else
            warning "Database backup failed or no existing database"
        fi
        
        # Backup Redis data
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis redis-cli BGSAVE > /dev/null 2>&1; then
            log "Redis backup initiated"
        else
            warning "Redis backup failed or no existing Redis instance"
        fi
        
        success "Backup completed: $BACKUP_PATH"
    else
        log "No running containers found, skipping backup"
    fi
}

# Build and deploy
deploy() {
    log "Starting deployment..."
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    # Build application image
    log "Building application image..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache rug_detector
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans
    
    # Start services
    log "Starting services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    success "Deployment completed"
}

# Health check
health_check() {
    log "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check PostgreSQL
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_isready -U rug_detector_user -d rug_detector > /dev/null 2>&1; then
        success "PostgreSQL is healthy"
    else
        error "PostgreSQL health check failed"
    fi
    
    # Check Redis
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis redis-cli ping | grep -q "PONG"; then
        success "Redis is healthy"
    else
        error "Redis health check failed"
    fi
    
    # Check application
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            success "Application is healthy"
            break
        else
            log "Attempt $attempt/$max_attempts: Application not ready yet..."
            sleep 10
            ((attempt++))
        fi
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        error "Application health check failed after $max_attempts attempts"
    fi
    
    success "All health checks passed"
}

# Show status
show_status() {
    log "Deployment status:"
    echo ""
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
    echo ""
    
    log "Service URLs:"
    echo "  Application: http://localhost:8000"
    echo "  Grafana: http://localhost:3000"
    echo "  Prometheus: http://localhost:9090"
    echo ""
    
    log "Logs location: ./logs/"
    log "Data location: ./data/"
    log "Backups location: ./backups/"
}

# Cleanup function
cleanup() {
    log "Cleaning up old Docker images..."
    docker image prune -f
    docker volume prune -f
    success "Cleanup completed"
}

# Main deployment function
main() {
    log "Starting Rug Detector System deployment..."
    log "Environment: Production"
    log "Platform: M1 Max optimized"
    
    check_prerequisites
    create_directories
    backup_data
    deploy
    health_check
    show_status
    cleanup
    
    success "🎉 Deployment completed successfully!"
    log "System is ready for production use"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        backup_data
        ;;
    "health")
        health_check
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    "stop")
        log "Stopping all services..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        success "All services stopped"
        ;;
    "logs")
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f "${2:-}"
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|status|cleanup|stop|logs [service]}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment (default)"
        echo "  backup  - Backup existing data"
        echo "  health  - Run health checks"
        echo "  status  - Show service status"
        echo "  cleanup - Clean up old Docker resources"
        echo "  stop    - Stop all services"
        echo "  logs    - Show logs (optionally for specific service)"
        exit 1
        ;;
esac
