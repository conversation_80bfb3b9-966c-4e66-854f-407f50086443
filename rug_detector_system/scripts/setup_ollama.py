#!/usr/bin/env python3
"""
Ollama Setup Script for LangGraph Multi-Agent System

This script sets up Ollama with the required models for the rug detection system.

Author: MLDevOps Architect
Version: 4.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import subprocess
import sys
import time
import requests
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from logging_config import get_logger


class OllamaSetup:
    """Ollama setup and model management."""
    
    def __init__(self):
        """Initialize Ollama setup."""
        self.logger = get_logger(__name__)
        self.ollama_base_url = "http://localhost:11434"
        
        # Recommended models for rug detection
        self.recommended_models = [
            "llama3.1:8b",      # Primary model - good balance of speed/accuracy
            "mistral:7b",       # Alternative model - fast inference
            "codellama:7b",     # For code analysis tasks
            "deepseek-r1:7b"    # Latest reasoning model
        ]
        
        # Model configurations
        self.model_configs = {
            "llama3.1:8b": {
                "description": "Meta's Llama 3.1 8B - Excellent for general reasoning",
                "size": "4.7GB",
                "recommended_for": "Primary multi-agent analysis"
            },
            "mistral:7b": {
                "description": "Mistral 7B - Fast and efficient",
                "size": "4.1GB", 
                "recommended_for": "Quick pattern analysis"
            },
            "codellama:7b": {
                "description": "Code Llama 7B - Specialized for code analysis",
                "size": "3.8GB",
                "recommended_for": "Smart contract pattern detection"
            },
            "deepseek-r1:7b": {
                "description": "DeepSeek R1 7B - Advanced reasoning capabilities",
                "size": "4.2GB",
                "recommended_for": "Complex risk assessment"
            }
        }
    
    async def setup_ollama(self) -> bool:
        """Complete Ollama setup process."""
        try:
            print("🚀 OLLAMA SETUP FOR LANGGRAPH MULTI-AGENT SYSTEM")
            print("=" * 60)
            
            # Check if Ollama is installed
            if not await self._check_ollama_installation():
                print("❌ Ollama not found. Please install Ollama first:")
                print("   macOS: brew install ollama")
                print("   Linux: curl -fsSL https://ollama.ai/install.sh | sh")
                print("   Windows: Download from https://ollama.ai/download")
                return False
            
            # Check if Ollama service is running
            if not await self._check_ollama_service():
                print("🔄 Starting Ollama service...")
                await self._start_ollama_service()
                
                # Wait for service to start
                await asyncio.sleep(5)
                
                if not await self._check_ollama_service():
                    print("❌ Failed to start Ollama service")
                    return False
            
            print("✅ Ollama service is running")
            
            # List available models
            await self._list_available_models()
            
            # Setup recommended models
            await self._setup_models()
            
            # Test model functionality
            await self._test_models()
            
            print("\n🎉 OLLAMA SETUP COMPLETE!")
            print("✅ Ready for LangGraph multi-agent rug detection")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Ollama setup failed: {e}")
            print(f"❌ Setup failed: {e}")
            return False
    
    async def _check_ollama_installation(self) -> bool:
        """Check if Ollama is installed."""
        try:
            result = subprocess.run(
                ["ollama", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ Ollama found: {version}")
                return True
            else:
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    async def _check_ollama_service(self) -> bool:
        """Check if Ollama service is running."""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except requests.RequestException:
            return False
    
    async def _start_ollama_service(self) -> bool:
        """Start Ollama service."""
        try:
            # Start Ollama service in background
            subprocess.Popen(
                ["ollama", "serve"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to start Ollama service: {e}")
            return False
    
    async def _list_available_models(self):
        """List currently available models."""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                
                print(f"\n📋 Currently Available Models ({len(models)}):")
                print("-" * 40)
                
                if models:
                    for model in models:
                        name = model.get('name', 'Unknown')
                        size = model.get('size', 0)
                        size_gb = size / (1024**3) if size > 0 else 0
                        print(f"  • {name} ({size_gb:.1f}GB)")
                else:
                    print("  No models currently installed")
                    
        except Exception as e:
            self.logger.error(f"Failed to list models: {e}")
    
    async def _setup_models(self):
        """Setup recommended models."""
        print(f"\n🔧 Setting Up Recommended Models")
        print("-" * 40)
        
        for model in self.recommended_models:
            config = self.model_configs.get(model, {})
            description = config.get('description', 'No description')
            size = config.get('size', 'Unknown size')
            
            print(f"\n📦 {model}")
            print(f"   {description}")
            print(f"   Size: {size}")
            
            if await self._is_model_available(model):
                print(f"   ✅ Already installed")
                continue
            
            print(f"   🔄 Downloading and installing...")
            
            if await self._pull_model(model):
                print(f"   ✅ Successfully installed")
            else:
                print(f"   ❌ Failed to install")
    
    async def _is_model_available(self, model_name: str) -> bool:
        """Check if a model is available locally."""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                return any(model.get('name', '').startswith(model_name) for model in models)
            return False
        except Exception:
            return False
    
    async def _pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry."""
        try:
            # Use subprocess for model pulling with real-time output
            process = subprocess.Popen(
                ["ollama", "pull", model_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Monitor progress
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # Show progress for large downloads
                    if "pulling" in output.lower() or "%" in output:
                        print(f"     {output.strip()}")
            
            return_code = process.poll()
            return return_code == 0
            
        except Exception as e:
            self.logger.error(f"Failed to pull model {model_name}: {e}")
            return False
    
    async def _test_models(self):
        """Test installed models."""
        print(f"\n🧪 Testing Model Functionality")
        print("-" * 40)
        
        test_prompt = "What is a smart contract rug pull? Answer in one sentence."
        
        for model in self.recommended_models:
            if await self._is_model_available(model):
                print(f"\n🔍 Testing {model}...")
                
                if await self._test_model_inference(model, test_prompt):
                    print(f"   ✅ Model working correctly")
                else:
                    print(f"   ❌ Model test failed")
            else:
                print(f"\n⏭️  Skipping {model} (not installed)")
    
    async def _test_model_inference(self, model_name: str, prompt: str) -> bool:
        """Test model inference."""
        try:
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '').strip()
                
                if response_text and len(response_text) > 10:
                    print(f"     Response: {response_text[:100]}...")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Model test failed for {model_name}: {e}")
            return False
    
    def print_usage_instructions(self):
        """Print usage instructions."""
        print("\n📖 USAGE INSTRUCTIONS")
        print("=" * 60)
        print("1. Start the rug detector system:")
        print("   docker-compose up -d")
        print("")
        print("2. Run LangGraph validation:")
        print("   python scripts/langgraph_validation.py")
        print("")
        print("3. Available models for configuration:")
        for model, config in self.model_configs.items():
            print(f"   • {model}: {config['recommended_for']}")
        print("")
        print("4. To change the default model, update OLLAMA_MODEL in .env:")
        print("   OLLAMA_MODEL=mistral:7b")
        print("")
        print("5. Monitor Ollama service:")
        print("   curl http://localhost:11434/api/tags")


async def main():
    """Main setup entry point."""
    setup = OllamaSetup()
    
    success = await setup.setup_ollama()
    
    if success:
        setup.print_usage_instructions()
        return True
    else:
        print("\n❌ Setup failed. Please check the logs and try again.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
