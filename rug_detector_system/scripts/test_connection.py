#!/usr/bin/env python3
"""
Connection Test Script - API and Service Connectivity Validation

This script provides a quick connection test for all external services
and APIs used by the Rug Detector System. It's designed to be run
before starting the main application to prevent runtime errors.

Features:
- Quick API connectivity testing
- Database connection validation
- Redis connectivity testing
- Blockchain RPC endpoint testing
- Comprehensive error reporting
- Performance metrics

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import sys
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Tuple

import structlog

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from config import ConfigurationManager, ConfigurationError
from api_validator import APIKeyValidator


class ConnectionTester:
    """Quick connection testing for all external services."""
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        self.start_time = time.time()
        
    async def run_connection_tests(self) -> bool:
        """Run all connection tests and return overall success."""
        print("🔗 Rug Detector System - Connection Test")
        print("=" * 50)
        print("Testing connectivity to all external services...")
        print()
        
        try:
            # Load configuration
            print("📋 Loading configuration...")
            config_manager = ConfigurationManager()
            config = config_manager.load_configuration()
            print(f"✅ Configuration loaded (Environment: {config.environment.value})")
            print()
            
            # Test API connectivity
            print("🌐 Testing API connectivity...")
            api_validator = APIKeyValidator(config.api_keys, timeout=10)
            api_result = await api_validator.validate_all_apis()
            
            # Print API results
            for status in api_result.api_statuses:
                if status.is_valid:
                    response_info = f" ({status.response_time_ms:.0f}ms)" if status.response_time_ms else ""
                    print(f"  ✅ {status.name}{response_info}")
                else:
                    print(f"  ❌ {status.name}: {status.error_message}")
            
            print()
            
            # Test blockchain connectivity
            print("⛓️  Testing blockchain connectivity...")
            blockchain_success = await self._test_blockchain_connectivity(config)
            
            print()
            
            # Calculate overall results
            total_time = time.time() - self.start_time
            api_success_rate = (api_result.valid_apis / api_result.total_apis) * 100
            
            print("=" * 50)
            print("📊 CONNECTION TEST SUMMARY")
            print("=" * 50)
            print(f"🌐 API Tests: {api_result.valid_apis}/{api_result.total_apis} passed ({api_success_rate:.1f}%)")
            print(f"⛓️  Blockchain: {'✅ Connected' if blockchain_success else '❌ Failed'}")
            print(f"⏱️  Total time: {total_time:.2f} seconds")
            print()
            
            # Determine overall success
            critical_apis_working = api_result.valid_apis >= 3  # At least 3 APIs should work
            overall_success = critical_apis_working and blockchain_success
            
            if overall_success:
                print("🎉 CONNECTION TEST PASSED!")
                print("✅ All critical services are accessible")
                print("🚀 System ready to start")
            else:
                print("❌ CONNECTION TEST FAILED!")
                print("🔧 Please check your configuration and network connectivity")
                
                if not critical_apis_working:
                    print("   • Too many API failures - check your API keys")
                if not blockchain_success:
                    print("   • Blockchain connectivity failed - check RPC URLs")
            
            print("=" * 50)
            
            return overall_success
            
        except ConfigurationError as e:
            print(f"❌ Configuration Error: {e}")
            print("🔧 Please check your .env file and configuration")
            return False
        except Exception as e:
            print(f"❌ Unexpected Error: {e}")
            print("🔧 Please check your setup and try again")
            return False
    
    async def _test_blockchain_connectivity(self, config) -> bool:
        """Test blockchain RPC connectivity."""
        try:
            import httpx
            
            # Test primary Web3 provider
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Test with a simple eth_blockNumber call
                payload = {
                    "jsonrpc": "2.0",
                    "method": "eth_blockNumber",
                    "params": [],
                    "id": 1
                }
                
                response = await client.post(
                    config.blockchain.web3_provider_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'result' in data:
                        block_number = int(data['result'], 16)
                        print(f"  ✅ Web3 Provider (Latest block: {block_number:,})")
                        return True
                    else:
                        print(f"  ❌ Web3 Provider: Invalid response - {data}")
                        return False
                else:
                    print(f"  ❌ Web3 Provider: HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"  ❌ Web3 Provider: {str(e)}")
            return False
    
    def print_quick_help(self):
        """Print quick help for common issues."""
        print("\n🆘 TROUBLESHOOTING GUIDE")
        print("-" * 30)
        print("If connection tests fail, check:")
        print("1. Internet connectivity")
        print("2. API keys in .env file are correct")
        print("3. No typos in configuration")
        print("4. API services are not down")
        print("5. Rate limits not exceeded")
        print("\nFor detailed validation, run:")
        print("  python scripts/validate_configuration.py")


async def main():
    """Main connection test entry point."""
    tester = ConnectionTester()
    
    try:
        success = await tester.run_connection_tests()
        
        if not success:
            tester.print_quick_help()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️  Connection test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Fatal error during connection test: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
