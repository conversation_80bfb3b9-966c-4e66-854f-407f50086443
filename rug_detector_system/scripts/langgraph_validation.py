#!/usr/bin/env python3
"""
LangGraph Multi-Agent Validation Script

This script validates the enhanced rug detection system using LangGraph
multi-agent architecture with real-time data integration.

Author: MLDevOps Architect
Version: 4.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import sys
import time
import json
import csv
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional

# Add src and tests to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))
sys.path.insert(0, str(Path(__file__).parent.parent / 'tests'))

from agents.langgraph_integration import analyze_contract_with_agents
from agents.real_time_data_integrator import create_real_time_integrator
from enhanced_test_dataset import get_enhanced_test_dataset, ContractCategory


@dataclass
class LangGraphTestResult:
    """Test result for LangGraph multi-agent analysis."""
    contract_address: str
    contract_name: str
    category: str
    expected_risk_level: str
    
    # Multi-agent results
    pattern_agent_risk: str
    pattern_agent_confidence: float
    market_agent_risk: str
    market_agent_confidence: float
    social_agent_risk: str
    social_agent_confidence: float
    
    # Final coordinated results
    final_risk_score: float
    final_recommendation: str
    overall_confidence: float
    
    # Performance metrics
    analysis_time: float
    agents_successful: int
    total_agents: int
    
    # Validation
    test_passed: bool
    accuracy_improvement: float
    error_message: Optional[str] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


class LangGraphValidator:
    """LangGraph multi-agent validation framework."""
    
    def __init__(self):
        """Initialize LangGraph validator."""
        self.test_results: List[LangGraphTestResult] = []
        self.dataset = get_enhanced_test_dataset()
        self.data_integrator = None
        
        # Enhanced accuracy targets for multi-agent system
        self.accuracy_targets = {
            'overall_accuracy': 0.95,  # Higher target with multi-agent
            'rug_pull_detection_rate': 0.98,
            'false_positive_rate': 0.03,  # Lower target with better analysis
            'confidence_threshold': 0.80,
            'agent_success_rate': 0.90
        }
        
        # Performance metrics
        self.performance_metrics = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'true_positives': 0,
            'false_positives': 0,
            'true_negatives': 0,
            'false_negatives': 0,
            'avg_analysis_time': 0.0,
            'avg_confidence': 0.0,
            'agent_success_rates': {}
        }
    
    async def run_langgraph_validation(self) -> Dict[str, Any]:
        """Run comprehensive LangGraph multi-agent validation."""
        print("🚀 LANGGRAPH MULTI-AGENT RUG DETECTOR VALIDATION")
        print("=" * 80)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Print dataset summary
        summary = self.dataset.get_test_summary()
        print(f"\n📊 Enhanced Test Dataset Summary:")
        print(f"  Total contracts: {summary['total_contracts']}")
        print(f"  Documented rug-pulls: {summary['by_category']['rug_pull']}")
        print(f"  Legitimate contracts: {summary['by_category']['legitimate']}")
        print(f"  Total documented losses: ${summary['total_documented_losses_usd']:,.0f}")
        print("=" * 80)
        
        validation_report = {
            'dataset_summary': summary,
            'test_results': [],
            'performance_metrics': {},
            'accuracy_analysis': {},
            'agent_performance': {},
            'production_readiness': {}
        }
        
        try:
            # Initialize real-time data integrator
            print("\n🔧 Initializing Multi-Agent System")
            print("-" * 50)
            
            self.data_integrator = await create_real_time_integrator()
            print("✅ Real-time data integrator initialized")
            print("✅ LangGraph multi-agent system ready")
            
            # Run comprehensive tests
            print("\n🧪 Running Multi-Agent Test Suite")
            print("-" * 50)
            
            all_contracts = self.dataset.get_all_contracts()
            
            for i, contract in enumerate(all_contracts, 1):
                print(f"\n[{i}/{len(all_contracts)}] Multi-Agent Analysis: {contract.name}")
                
                try:
                    result = await self._test_contract_with_agents(contract)
                    self.test_results.append(result)
                    validation_report['test_results'].append(asdict(result))
                    
                    # Update performance metrics
                    self._update_performance_metrics(contract, result)
                    
                    # Print result
                    status = "✅" if result.test_passed else "❌"
                    print(f"  {status} Expected: {result.expected_risk_level} | "
                          f"Final: {result.final_recommendation} | "
                          f"Score: {result.final_risk_score:.3f} | "
                          f"Confidence: {result.overall_confidence:.3f} | "
                          f"Time: {result.analysis_time:.2f}s")
                    
                    # Print agent breakdown
                    print(f"    Agents: Pattern({result.pattern_agent_confidence:.2f}) | "
                          f"Market({result.market_agent_confidence:.2f}) | "
                          f"Social({result.social_agent_confidence:.2f})")
                    
                except Exception as e:
                    print(f"  ❌ Multi-agent analysis failed: {e}")
                    error_result = LangGraphTestResult(
                        contract_address=contract.address,
                        contract_name=contract.name,
                        category=contract.category.value,
                        expected_risk_level=contract.expected_risk_level,
                        pattern_agent_risk="error",
                        pattern_agent_confidence=0.0,
                        market_agent_risk="error",
                        market_agent_confidence=0.0,
                        social_agent_risk="error",
                        social_agent_confidence=0.0,
                        final_risk_score=0.0,
                        final_recommendation="error",
                        overall_confidence=0.0,
                        analysis_time=0.0,
                        agents_successful=0,
                        total_agents=3,
                        test_passed=False,
                        accuracy_improvement=0.0,
                        error_message=str(e)
                    )
                    self.test_results.append(error_result)
                    validation_report['test_results'].append(asdict(error_result))
            
            # Analyze results
            print("\n📊 Analyzing Multi-Agent Results")
            print("-" * 50)
            
            validation_report['performance_metrics'] = self._calculate_performance_metrics()
            validation_report['accuracy_analysis'] = self._analyze_accuracy()
            validation_report['agent_performance'] = self._analyze_agent_performance()
            validation_report['production_readiness'] = self._assess_production_readiness()
            
            # Save comprehensive report
            await self._save_langgraph_report(validation_report)
            
            return validation_report
            
        except Exception as e:
            print(f"\n❌ LangGraph validation failed: {e}")
            raise
        
        finally:
            if self.data_integrator:
                await self.data_integrator.close()
    
    async def _test_contract_with_agents(self, contract) -> LangGraphTestResult:
        """Test individual contract with multi-agent system."""
        start_time = time.time()
        
        try:
            # Generate realistic contract code
            contract_code = self._generate_test_contract_code(contract)
            
            # Run multi-agent analysis
            agent_result = await analyze_contract_with_agents(
                contract.address, contract_code
            )
            
            analysis_time = time.time() - start_time
            
            # Extract agent-specific results
            pattern_analysis = agent_result.get('agent_analyses', {}).get('pattern_analyst', {})
            market_analysis = agent_result.get('agent_analyses', {}).get('market_data_analyst', {})
            social_analysis = agent_result.get('agent_analyses', {}).get('social_sentiment_analyst', {})
            
            # Count successful agents
            agents_successful = sum(1 for analysis in [pattern_analysis, market_analysis, social_analysis]
                                  if analysis and 'error' not in analysis)
            
            # Evaluate test result
            test_passed, accuracy_improvement = self._evaluate_agent_result(
                contract, agent_result
            )
            
            return LangGraphTestResult(
                contract_address=contract.address,
                contract_name=contract.name,
                category=contract.category.value,
                expected_risk_level=contract.expected_risk_level,
                pattern_agent_risk=pattern_analysis.get('recommended_risk_level', 'unknown'),
                pattern_agent_confidence=pattern_analysis.get('confidence', 0.0),
                market_agent_risk=market_analysis.get('market_risk_level', 'unknown'),
                market_agent_confidence=market_analysis.get('confidence', 0.0),
                social_agent_risk=social_analysis.get('social_risk_level', 'unknown'),
                social_agent_confidence=social_analysis.get('confidence', 0.0),
                final_risk_score=agent_result.get('final_risk_score', 0.0),
                final_recommendation=agent_result.get('final_recommendation', 'unknown'),
                overall_confidence=agent_result.get('confidence_level', 0.0),
                analysis_time=analysis_time,
                agents_successful=agents_successful,
                total_agents=3,
                test_passed=test_passed,
                accuracy_improvement=accuracy_improvement
            )
            
        except Exception as e:
            analysis_time = time.time() - start_time
            return LangGraphTestResult(
                contract_address=contract.address,
                contract_name=contract.name,
                category=contract.category.value,
                expected_risk_level=contract.expected_risk_level,
                pattern_agent_risk="error",
                pattern_agent_confidence=0.0,
                market_agent_risk="error",
                market_agent_confidence=0.0,
                social_agent_risk="error",
                social_agent_confidence=0.0,
                final_risk_score=0.0,
                final_recommendation="error",
                overall_confidence=0.0,
                analysis_time=analysis_time,
                agents_successful=0,
                total_agents=3,
                test_passed=False,
                accuracy_improvement=0.0,
                error_message=str(e)
            )
    
    def _generate_test_contract_code(self, contract) -> str:
        """Generate realistic contract code based on category."""
        if contract.category == ContractCategory.RUG_PULL:
            return self._generate_rug_pull_code(contract)
        elif contract.category == ContractCategory.HONEYPOT:
            return self._generate_honeypot_code(contract)
        elif contract.category == ContractCategory.LEGITIMATE:
            return self._generate_legitimate_code(contract)
        else:
            return self._generate_suspicious_code(contract)
    
    def _generate_rug_pull_code(self, contract) -> str:
        """Generate rug-pull contract code with specific attack vectors."""
        return f'''
        pragma solidity ^0.8.0;
        
        contract {contract.name.replace(" ", "")} {{
            address private owner;
            bool public tradingEnabled = false;
            mapping(address => bool) public blacklisted;
            mapping(address => uint256) public balanceOf;
            
            modifier onlyOwner() {{
                require(msg.sender == owner, "Not owner");
                _;
            }}
            
            constructor() {{
                owner = msg.sender;
            }}
            
            function transfer(address to, uint256 value) public returns (bool) {{
                require(tradingEnabled, "Trading disabled");
                require(!blacklisted[msg.sender], "Address blacklisted");
                if (msg.sender != owner) {{
                    revert("Transfer not allowed");
                }}
                balanceOf[msg.sender] -= value;
                balanceOf[to] += value;
                return true;
            }}
            
            function rugPull() public onlyOwner {{
                payable(owner).transfer(address(this).balance);
                tradingEnabled = false;
            }}
            
            function emergencyWithdraw() public onlyOwner {{
                payable(owner).transfer(address(this).balance);
            }}
        }}
        '''
    
    def _generate_honeypot_code(self, contract) -> str:
        """Generate honeypot contract code."""
        return f'''
        pragma solidity ^0.8.0;
        
        contract {contract.name.replace(" ", "")} {{
            address private owner;
            mapping(address => uint256) public balanceOf;
            
            constructor() {{
                owner = msg.sender;
            }}
            
            function transfer(address to, uint256 value) public returns (bool) {{
                require(balanceOf[msg.sender] >= value, "Insufficient balance");
                if (msg.sender != owner) {{
                    revert("Transfer not allowed");
                }}
                balanceOf[msg.sender] -= value;
                balanceOf[to] += value;
                return true;
            }}
        }}
        '''
    
    def _generate_legitimate_code(self, contract) -> str:
        """Generate legitimate contract code."""
        return f'''
        pragma solidity ^0.8.0;
        
        contract {contract.name.replace(" ", "")} {{
            string public name = "{contract.name}";
            string public symbol = "{contract.symbol}";
            uint8 public decimals = 18;
            uint256 public totalSupply = 1000000 * 10**18;
            
            mapping(address => uint256) public balanceOf;
            mapping(address => mapping(address => uint256)) public allowance;
            
            event Transfer(address indexed from, address indexed to, uint256 value);
            
            constructor() {{
                balanceOf[msg.sender] = totalSupply;
            }}
            
            function transfer(address to, uint256 value) public returns (bool) {{
                require(balanceOf[msg.sender] >= value, "Insufficient balance");
                balanceOf[msg.sender] -= value;
                balanceOf[to] += value;
                emit Transfer(msg.sender, to, value);
                return true;
            }}
        }}
        '''
    
    def _generate_suspicious_code(self, contract) -> str:
        """Generate suspicious contract code."""
        return f'''
        pragma solidity ^0.8.0;
        
        contract {contract.name.replace(" ", "")} {{
            address public owner;
            bool public paused = false;
            mapping(address => uint256) public balanceOf;
            
            modifier onlyOwner() {{
                require(msg.sender == owner, "Not owner");
                _;
            }}
            
            constructor() {{
                owner = msg.sender;
            }}
            
            function transfer(address to, uint256 value) public returns (bool) {{
                require(!paused, "Contract is paused");
                require(balanceOf[msg.sender] >= value, "Insufficient balance");
                balanceOf[msg.sender] -= value;
                balanceOf[to] += value;
                return true;
            }}
            
            function pause() public onlyOwner {{
                paused = true;
            }}
        }}
        '''
    
    def _evaluate_agent_result(self, contract, agent_result) -> tuple[bool, float]:
        """Evaluate multi-agent result with enhanced accuracy scoring."""
        expected = contract.expected_risk_level
        final_recommendation = agent_result.get('final_recommendation', 'unknown')
        
        # Map recommendations to risk levels
        recommendation_to_risk = {
            'safe': 'minimal',
            'low_risk': 'low',
            'caution': 'medium',
            'high_caution': 'high',
            'avoid': 'critical',
            'unknown': 'medium'
        }
        
        predicted = recommendation_to_risk.get(final_recommendation, 'medium')
        
        # Enhanced evaluation for multi-agent system
        risk_levels = ['minimal', 'low', 'medium', 'high', 'critical']
        expected_idx = risk_levels.index(expected) if expected in risk_levels else 2
        predicted_idx = risk_levels.index(predicted) if predicted in risk_levels else 2
        
        distance = abs(expected_idx - predicted_idx)
        accuracy_score = max(0.0, 1.0 - (distance * 0.2))  # More lenient for multi-agent
        
        # Test passes if within 1 level or exact match
        test_passed = distance <= 1
        
        # Special cases for critical categories
        if contract.category == ContractCategory.RUG_PULL:
            test_passed = final_recommendation in ['high_caution', 'avoid']
        elif contract.category == ContractCategory.LEGITIMATE:
            test_passed = final_recommendation in ['safe', 'low_risk', 'caution']
        elif contract.category == ContractCategory.HONEYPOT:
            test_passed = final_recommendation in ['high_caution', 'avoid']
        
        return test_passed, accuracy_score
    
    def _update_performance_metrics(self, contract, result):
        """Update performance metrics based on test result."""
        self.performance_metrics['total_tests'] += 1
        
        if result.test_passed:
            self.performance_metrics['passed_tests'] += 1
        else:
            self.performance_metrics['failed_tests'] += 1
        
        # Update confusion matrix
        is_malicious = contract.category in [ContractCategory.RUG_PULL, ContractCategory.HONEYPOT]
        predicted_malicious = result.final_recommendation in ['high_caution', 'avoid']
        
        if is_malicious and predicted_malicious:
            self.performance_metrics['true_positives'] += 1
        elif is_malicious and not predicted_malicious:
            self.performance_metrics['false_negatives'] += 1
        elif not is_malicious and predicted_malicious:
            self.performance_metrics['false_positives'] += 1
        else:
            self.performance_metrics['true_negatives'] += 1
    
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        tp = self.performance_metrics['true_positives']
        fp = self.performance_metrics['false_positives']
        tn = self.performance_metrics['true_negatives']
        fn = self.performance_metrics['false_negatives']
        
        total = tp + fp + tn + fn
        
        if total == 0:
            return {}
        
        accuracy = (tp + tn) / total
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        false_positive_rate = fp / (fp + tn) if (fp + tn) > 0 else 0
        
        # Calculate average metrics
        analysis_times = [r.analysis_time for r in self.test_results if r.error_message is None]
        confidences = [r.overall_confidence for r in self.test_results if r.error_message is None]
        agent_success_rates = [r.agents_successful / r.total_agents for r in self.test_results if r.error_message is None]
        
        return {
            'confusion_matrix': {'tp': tp, 'fp': fp, 'tn': tn, 'fn': fn},
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'false_positive_rate': false_positive_rate,
            'avg_analysis_time': sum(analysis_times) / len(analysis_times) if analysis_times else 0,
            'avg_confidence': sum(confidences) / len(confidences) if confidences else 0,
            'avg_agent_success_rate': sum(agent_success_rates) / len(agent_success_rates) if agent_success_rates else 0,
            'meets_accuracy_target': accuracy >= self.accuracy_targets['overall_accuracy'],
            'meets_fpr_target': false_positive_rate <= self.accuracy_targets['false_positive_rate']
        }
    
    def _analyze_accuracy(self) -> Dict[str, Any]:
        """Analyze accuracy by category and agent performance."""
        accuracy_by_category = {}
        
        # Group results by category
        for category in ContractCategory:
            category_results = [r for r in self.test_results if r.category == category.value]
            if category_results:
                passed = sum(1 for r in category_results if r.test_passed)
                accuracy_by_category[category.value] = passed / len(category_results)
        
        return {
            'accuracy_by_category': accuracy_by_category,
            'overall_accuracy': sum(1 for r in self.test_results if r.test_passed) / len(self.test_results) if self.test_results else 0
        }
    
    def _analyze_agent_performance(self) -> Dict[str, Any]:
        """Analyze individual agent performance."""
        pattern_confidences = [r.pattern_agent_confidence for r in self.test_results if r.pattern_agent_confidence > 0]
        market_confidences = [r.market_agent_confidence for r in self.test_results if r.market_agent_confidence > 0]
        social_confidences = [r.social_agent_confidence for r in self.test_results if r.social_agent_confidence > 0]
        
        return {
            'pattern_agent': {
                'avg_confidence': sum(pattern_confidences) / len(pattern_confidences) if pattern_confidences else 0,
                'success_rate': len(pattern_confidences) / len(self.test_results) if self.test_results else 0
            },
            'market_agent': {
                'avg_confidence': sum(market_confidences) / len(market_confidences) if market_confidences else 0,
                'success_rate': len(market_confidences) / len(self.test_results) if self.test_results else 0
            },
            'social_agent': {
                'avg_confidence': sum(social_confidences) / len(social_confidences) if social_confidences else 0,
                'success_rate': len(social_confidences) / len(self.test_results) if self.test_results else 0
            }
        }
    
    def _assess_production_readiness(self) -> Dict[str, Any]:
        """Assess production readiness with multi-agent criteria."""
        performance = self._calculate_performance_metrics()
        accuracy = self._analyze_accuracy()
        agent_performance = self._analyze_agent_performance()
        
        criteria_met = {
            'overall_accuracy': accuracy['overall_accuracy'] >= self.accuracy_targets['overall_accuracy'],
            'rug_pull_detection': accuracy['accuracy_by_category'].get('rug_pull', 0) >= self.accuracy_targets['rug_pull_detection_rate'],
            'false_positive_rate': performance.get('false_positive_rate', 1.0) <= self.accuracy_targets['false_positive_rate'],
            'confidence_threshold': performance.get('avg_confidence', 0) >= self.accuracy_targets['confidence_threshold'],
            'agent_success_rate': performance.get('avg_agent_success_rate', 0) >= self.accuracy_targets['agent_success_rate'],
            'no_critical_errors': len([r for r in self.test_results if r.error_message]) == 0
        }
        
        all_criteria_met = all(criteria_met.values())
        
        return {
            'criteria_met': criteria_met,
            'overall_ready': all_criteria_met,
            'go_no_go_decision': 'GO' if all_criteria_met else 'NO_GO',
            'accuracy_targets': self.accuracy_targets,
            'actual_performance': {
                'overall_accuracy': accuracy['overall_accuracy'],
                'rug_pull_detection_rate': accuracy['accuracy_by_category'].get('rug_pull', 0),
                'false_positive_rate': performance.get('false_positive_rate', 1.0),
                'avg_confidence': performance.get('avg_confidence', 0),
                'agent_success_rate': performance.get('avg_agent_success_rate', 0)
            },
            'agent_performance': agent_performance
        }
    
    async def _save_langgraph_report(self, report: Dict[str, Any]):
        """Save LangGraph validation report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save JSON report
        report_path = Path(f"langgraph_validation_report_{timestamp}.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Save CSV results
        csv_path = Path(f"langgraph_test_results_{timestamp}.csv")
        with open(csv_path, 'w', newline='') as f:
            if self.test_results:
                writer = csv.DictWriter(f, fieldnames=asdict(self.test_results[0]).keys())
                writer.writeheader()
                for result in self.test_results:
                    writer.writerow(asdict(result))
        
        print(f"\n📄 LangGraph validation report saved: {report_path}")
        print(f"📊 LangGraph test results saved: {csv_path}")


async def main():
    """Main LangGraph validation entry point."""
    validator = LangGraphValidator()
    
    try:
        report = await validator.run_langgraph_validation()
        
        print("\n" + "=" * 80)
        print("🏁 LANGGRAPH MULTI-AGENT VALIDATION COMPLETE")
        print("=" * 80)
        
        # Print key metrics
        performance = report.get('performance_metrics', {})
        accuracy = report.get('accuracy_analysis', {})
        agent_perf = report.get('agent_performance', {})
        readiness = report.get('production_readiness', {})
        
        print(f"\n📊 MULTI-AGENT RESULTS:")
        print(f"  Overall Accuracy: {accuracy.get('overall_accuracy', 0):.1%}")
        print(f"  Rug-Pull Detection Rate: {accuracy.get('accuracy_by_category', {}).get('rug_pull', 0):.1%}")
        print(f"  False Positive Rate: {performance.get('false_positive_rate', 0):.1%}")
        print(f"  Average Confidence: {performance.get('avg_confidence', 0):.1%}")
        print(f"  Agent Success Rate: {performance.get('avg_agent_success_rate', 0):.1%}")
        print(f"  Average Analysis Time: {performance.get('avg_analysis_time', 0):.2f}s")
        
        print(f"\n🤖 AGENT PERFORMANCE:")
        for agent, metrics in agent_perf.items():
            print(f"  {agent.replace('_', ' ').title()}: "
                  f"Confidence {metrics.get('avg_confidence', 0):.1%} | "
                  f"Success {metrics.get('success_rate', 0):.1%}")
        
        print(f"\n🎯 PRODUCTION READINESS: {readiness.get('go_no_go_decision', 'NO_GO')}")
        
        if readiness.get('go_no_go_decision') == 'GO':
            print("\n🎉 MULTI-AGENT SYSTEM IS PRODUCTION READY!")
            print("✅ All accuracy and performance targets exceeded")
            print("✅ LangGraph multi-agent architecture validated")
            print("✅ Real-time data integration confirmed")
            print("✅ Enhanced rug-pull detection with AI agents")
        else:
            print("\n❌ MULTI-AGENT SYSTEM NEEDS REFINEMENT")
            print("\nCriteria Status:")
            for criterion, met in readiness.get('criteria_met', {}).items():
                status = "✅" if met else "❌"
                print(f"  {status} {criterion.replace('_', ' ').title()}")
        
        return readiness.get('go_no_go_decision') == 'GO'
        
    except Exception as e:
        print(f"\n❌ LANGGRAPH VALIDATION FAILED: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
