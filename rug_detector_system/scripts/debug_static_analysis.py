#!/usr/bin/env python3
"""
Debug Static Analysis - Investigate Risk Calculation Issues

This script debugs the static analysis to understand why all contracts
are receiving the same risk score.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from analysis.static_analyzer import StaticAnalyzer


async def debug_static_analysis():
    """Debug static analysis with detailed output."""
    
    # Test contract with obvious rug-pull patterns
    rug_pull_contract = '''
    pragma solidity ^0.8.0;
    
    contract RugPullToken {
        address private owner;
        bool public tradingEnabled = false;
        mapping(address => bool) public blacklisted;
        
        modifier onlyOwner() {
            require(msg.sender == owner, "Not owner");
            _;
        }
        
        function transfer(address to, uint256 value) public returns (bool) {
            require(tradingEnabled, "Trading disabled");
            require(!blacklisted[msg.sender], "Address blacklisted");
            if (msg.sender != owner) {
                revert("Transfer not allowed");
            }
            return true;
        }
        
        function rugPull() public onlyOwner {
            payable(owner).transfer(address(this).balance);
        }
        
        function emergencyWithdraw() public onlyOwner {
            payable(owner).transfer(address(this).balance);
        }
    }
    '''
    
    # Initialize analyzer
    analyzer = StaticAnalyzer()
    await analyzer.initialize()
    
    print("🔍 Debugging Static Analysis")
    print("=" * 50)
    
    # Analyze contract
    result = await analyzer.analyze_contract("0xDEBUG", rug_pull_contract)
    
    print(f"Contract: {result.contract_name}")
    print(f"Overall Risk Score: {result.overall_risk_score}")
    print(f"Vulnerabilities Found: {len(result.vulnerabilities)}")
    print()
    
    print("Detected Vulnerabilities:")
    print("-" * 30)
    for i, vuln in enumerate(result.vulnerabilities, 1):
        print(f"{i}. Type: {vuln.type.value}")
        print(f"   Severity: {vuln.severity.value}")
        print(f"   Confidence: {vuln.confidence}")
        print(f"   Title: {vuln.title}")
        print(f"   Location: {vuln.location}")
        print()
    
    # Debug pattern analysis directly
    print("Pattern Analysis Debug:")
    print("-" * 30)
    pattern_vulns = analyzer._analyze_patterns(rug_pull_contract)
    
    for i, vuln in enumerate(pattern_vulns, 1):
        print(f"{i}. Pattern Type: {vuln.type.value}")
        print(f"   Severity: {vuln.severity.value}")
        print(f"   Confidence: {vuln.confidence}")
        print(f"   Description: {vuln.description}")
        print()
    
    # Debug risk calculation
    print("Risk Calculation Debug:")
    print("-" * 30)
    risk_score = analyzer._calculate_risk_score(result.vulnerabilities)
    print(f"Calculated Risk Score: {risk_score}")
    
    # Manual risk calculation
    print("\nManual Risk Calculation:")
    severity_weights = {
        'critical': 1.0,
        'high': 0.8,
        'medium': 0.5,
        'low': 0.2,
        'info': 0.1
    }
    
    total_score = 0.0
    for vuln in result.vulnerabilities:
        weight = severity_weights.get(vuln.severity.value, 0.1)
        score_contribution = weight * vuln.confidence
        total_score += score_contribution
        print(f"  {vuln.type.value}: {vuln.severity.value} * {vuln.confidence} = {score_contribution}")
    
    base_score = total_score / max(len(result.vulnerabilities), 1)
    print(f"Base Score: {total_score} / {len(result.vulnerabilities)} = {base_score}")
    
    # Check multipliers
    high_risk_vulns = [v for v in result.vulnerabilities if v.severity.value in ['high', 'critical']]
    print(f"High Risk Vulnerabilities: {len(high_risk_vulns)}")
    
    vulnerability_count_multiplier = min(1.0 + (len(result.vulnerabilities) - 1) * 0.15, 3.0)
    severity_multiplier = 1.0 + len(high_risk_vulns) * 0.3
    
    print(f"Vulnerability Count Multiplier: {vulnerability_count_multiplier}")
    print(f"Severity Multiplier: {severity_multiplier}")
    
    final_score = min(base_score * vulnerability_count_multiplier * severity_multiplier, 1.0)
    print(f"Final Score: {base_score} * {vulnerability_count_multiplier} * {severity_multiplier} = {final_score}")


if __name__ == "__main__":
    asyncio.run(debug_static_analysis())
