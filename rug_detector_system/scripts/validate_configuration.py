#!/usr/bin/env python3
"""
Configuration Validation Script - Phase 1.3 Completion

This script validates the complete configuration management system including:
- Environment file validation
- Configuration schema validation
- API key connectivity testing
- Database and Redis connectivity
- Security configuration validation
- Production readiness assessment

Validation Criteria:
- All required configuration present
- API keys valid and functional
- Database connectivity working
- Security settings properly configured
- Environment-specific validation
- Production readiness checks
"""

import sys
import asyncio
import time
from pathlib import Path
from typing import List, Dict, Any

import structlog

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from config import (
    ConfigurationManager, 
    RugDetectorConfig, 
    ConfigurationError,
    Environment
)
from api_validator import APIKeyValidator, ValidationResult


class ConfigurationValidator:
    """Comprehensive configuration validation for Phase 1.3 completion."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.success_count = 0
        self.total_checks = 0
        self.logger = structlog.get_logger(__name__)
        
    def validate_all(self) -> bool:
        """Run all configuration validation checks."""
        print("🔍 Phase 1.3 Validation: Configuration Management System")
        print("=" * 70)
        
        # Core validation checks
        self._validate_environment_file()
        self._validate_configuration_loading()
        self._validate_configuration_schema()
        self._validate_security_configuration()
        self._validate_database_configuration()
        self._validate_redis_configuration()
        self._validate_blockchain_configuration()
        
        # API validation (async)
        asyncio.run(self._validate_api_connectivity())
        
        # Environment-specific validation
        self._validate_environment_specific_settings()
        
        # Summary
        self._print_summary()
        
        return len(self.errors) == 0
    
    def _check(self, description: str, condition: bool, 
               error_msg: str = None, warning_msg: str = None) -> bool:
        """Helper method to perform a validation check."""
        self.total_checks += 1
        
        if condition:
            print(f"✅ {description}")
            self.success_count += 1
            return True
        else:
            print(f"❌ {description}")
            if error_msg:
                self.errors.append(error_msg)
            if warning_msg:
                self.warnings.append(warning_msg)
            return False
    
    def _validate_environment_file(self):
        """Validate .env file exists and is properly formatted."""
        print("\n📋 Environment File Validation")
        print("-" * 40)
        
        env_file = self.project_root / '.env'
        self._check(
            ".env file exists",
            env_file.exists() and env_file.is_file(),
            ".env file not found"
        )
        
        if env_file.exists():
            try:
                content = env_file.read_text()
                lines = [line.strip() for line in content.split('\n') 
                        if line.strip() and not line.startswith('#')]
                
                self._check(
                    f"Environment file has {len(lines)} variables",
                    len(lines) > 20,  # Should have substantial configuration
                    "Environment file appears incomplete"
                )
                
                # Check for required variables
                required_vars = [
                    'DATABASE_URL', 'REDIS_URL', 'ETHERSCAN_API_KEY',
                    'COINGECKO_API_KEY', 'DUNE_API_KEY', 'WEB3_PROVIDER_URL'
                ]
                
                for var in required_vars:
                    var_present = any(line.startswith(f'{var}=') for line in lines)
                    self._check(
                        f"Required variable {var} present",
                        var_present,
                        f"Missing required environment variable: {var}"
                    )
                
            except Exception as e:
                self.errors.append(f"Error reading .env file: {e}")
                print(f"❌ Error reading .env file: {e}")
    
    def _validate_configuration_loading(self):
        """Validate configuration can be loaded successfully."""
        print("\n⚙️  Configuration Loading Validation")
        print("-" * 40)
        
        try:
            config_manager = ConfigurationManager()
            config = config_manager.load_configuration()
            
            self._check(
                "Configuration loaded successfully",
                config is not None,
                "Failed to load configuration"
            )
            
            self._check(
                f"Environment: {config.environment.value}",
                config.environment in Environment,
                "Invalid environment configuration"
            )
            
            # Store config for further validation
            self.config = config
            
        except ConfigurationError as e:
            self.errors.append(f"Configuration loading failed: {e}")
            print(f"❌ Configuration loading failed: {e}")
            self.config = None
        except Exception as e:
            self.errors.append(f"Unexpected configuration error: {e}")
            print(f"❌ Unexpected configuration error: {e}")
            self.config = None
    
    def _validate_configuration_schema(self):
        """Validate configuration schema and data types."""
        print("\n📊 Configuration Schema Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate schema - configuration not loaded")
            print("❌ Cannot validate schema - configuration not loaded")
            return
        
        config = self.config
        
        # Validate database configuration
        self._check(
            "Database configuration valid",
            config.database.url.startswith('postgresql://'),
            "Invalid database configuration"
        )
        
        # Validate Redis configuration
        self._check(
            "Redis configuration valid",
            config.redis.url.startswith('redis://'),
            "Invalid Redis configuration"
        )
        
        # Validate blockchain configuration
        blockchain_valid = (
            config.blockchain.ethereum_rpc_url.startswith('https://') and
            config.blockchain.web3_provider_url.startswith('https://')
        )
        self._check(
            "Blockchain configuration valid",
            blockchain_valid,
            "Invalid blockchain configuration"
        )
        
        # Validate API keys configuration
        api_keys_valid = (
            len(config.api_keys.etherscan_api_key) > 10 and
            len(config.api_keys.coingecko_api_key) > 10 and
            len(config.api_keys.dune_api_key) > 10
        )
        self._check(
            "API keys configuration valid",
            api_keys_valid,
            "Invalid API keys configuration"
        )
    
    def _validate_security_configuration(self):
        """Validate security configuration."""
        print("\n🔒 Security Configuration Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate security - configuration not loaded")
            print("❌ Cannot validate security - configuration not loaded")
            return
        
        config = self.config
        
        # Check if production environment has proper security
        if config.environment == Environment.PRODUCTION:
            self._check(
                "Production JWT secret configured",
                config.security.jwt_secret_key is not None,
                "Production environment requires JWT secret"
            )
            
            self._check(
                "Production encryption key configured",
                config.security.encryption_key is not None,
                "Production environment requires encryption key"
            )
            
            self._check(
                "Debug disabled in production",
                not config.debug,
                "Debug mode should be disabled in production"
            )
        
        # Validate rate limiting
        self._check(
            "Rate limiting configured",
            config.security.api_rate_limit_per_minute > 0,
            "Rate limiting not properly configured"
        )
    
    def _validate_database_configuration(self):
        """Validate database configuration."""
        print("\n🗄️  Database Configuration Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate database - configuration not loaded")
            print("❌ Cannot validate database - configuration not loaded")
            return
        
        config = self.config
        
        # Validate connection parameters
        self._check(
            "Database pool size reasonable",
            1 <= config.database.pool_size <= 100,
            "Database pool size out of reasonable range"
        )
        
        self._check(
            "Database timeout reasonable",
            1 <= config.database.pool_timeout <= 300,
            "Database timeout out of reasonable range"
        )
        
        # Check URL format
        db_url = config.database.url
        url_parts = ['postgresql://', '@', '/', ':']
        url_valid = all(part in db_url for part in url_parts)
        
        self._check(
            "Database URL format valid",
            url_valid,
            "Database URL format appears invalid"
        )
    
    def _validate_redis_configuration(self):
        """Validate Redis configuration."""
        print("\n🔴 Redis Configuration Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate Redis - configuration not loaded")
            print("❌ Cannot validate Redis - configuration not loaded")
            return
        
        config = self.config
        
        # Validate connection parameters
        self._check(
            "Redis max connections reasonable",
            1 <= config.redis.max_connections <= 1000,
            "Redis max connections out of reasonable range"
        )
        
        self._check(
            "Redis timeout reasonable",
            1 <= config.redis.socket_timeout <= 300,
            "Redis timeout out of reasonable range"
        )
        
        # Check URL format
        redis_url = config.redis.url
        self._check(
            "Redis URL format valid",
            redis_url.startswith('redis://') and '@' in redis_url,
            "Redis URL format appears invalid"
        )
    
    def _validate_blockchain_configuration(self):
        """Validate blockchain configuration."""
        print("\n⛓️  Blockchain Configuration Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate blockchain - configuration not loaded")
            print("❌ Cannot validate blockchain - configuration not loaded")
            return
        
        config = self.config
        
        # Validate timeout settings
        self._check(
            "Connection timeout reasonable",
            1 <= config.blockchain.connection_timeout <= 300,
            "Blockchain connection timeout out of reasonable range"
        )
        
        self._check(
            "Request timeout reasonable",
            1 <= config.blockchain.request_timeout <= 120,
            "Blockchain request timeout out of reasonable range"
        )
        
        # Validate retry settings
        self._check(
            "Max retries reasonable",
            0 <= config.blockchain.max_retries <= 10,
            "Blockchain max retries out of reasonable range"
        )
        
        # Check RPC URLs
        rpc_urls = [
            config.blockchain.ethereum_rpc_url,
            config.blockchain.web3_provider_url
        ]
        
        for i, url in enumerate(rpc_urls):
            url_name = ["Ethereum RPC", "Web3 Provider"][i]
            self._check(
                f"{url_name} URL valid",
                url.startswith('https://') and len(url) > 20,
                f"{url_name} URL appears invalid"
            )
    
    async def _validate_api_connectivity(self):
        """Validate API connectivity and keys."""
        print("\n🌐 API Connectivity Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate APIs - configuration not loaded")
            print("❌ Cannot validate APIs - configuration not loaded")
            return
        
        try:
            validator = APIKeyValidator(self.config.api_keys, timeout=15)
            result = await validator.validate_all_apis()
            
            self._check(
                f"API validation completed ({result.total_apis} APIs tested)",
                True,  # Always true if we get here
                None
            )
            
            self._check(
                f"API connectivity success rate: {(result.valid_apis/result.total_apis)*100:.1f}%",
                result.valid_apis >= result.total_apis * 0.8,  # At least 80% should work
                f"Too many API failures: {result.invalid_apis}/{result.total_apis}"
            )
            
            # Report individual API statuses
            for status in result.api_statuses:
                if status.is_valid:
                    response_info = f" ({status.response_time_ms:.0f}ms)" if status.response_time_ms else ""
                    print(f"  ✅ {status.name}{response_info}")
                else:
                    print(f"  ❌ {status.name}: {status.error_message}")
                    if status.name in ["Etherscan", "CoinGecko", "Dune Analytics"]:
                        # These are critical APIs
                        self.errors.append(f"Critical API {status.name} failed: {status.error_message}")
            
        except Exception as e:
            self.errors.append(f"API validation failed: {e}")
            print(f"❌ API validation failed: {e}")
    
    def _validate_environment_specific_settings(self):
        """Validate environment-specific settings."""
        print("\n🌍 Environment-Specific Validation")
        print("-" * 40)
        
        if not hasattr(self, 'config') or self.config is None:
            self.errors.append("Cannot validate environment - configuration not loaded")
            print("❌ Cannot validate environment - configuration not loaded")
            return
        
        config = self.config
        
        if config.environment == Environment.DEVELOPMENT:
            self._check(
                "Development environment properly configured",
                config.debug is True or config.debug is False,  # Debug can be either
                None
            )
            
        elif config.environment == Environment.PRODUCTION:
            self._check(
                "Production debug disabled",
                not config.debug,
                "Debug should be disabled in production"
            )
            
            self._check(
                "Production security configured",
                config.security.jwt_secret_key is not None,
                "Production requires security configuration"
            )
        
        # Validate monitoring configuration
        self._check(
            "Monitoring ports configured",
            1024 <= config.monitoring.prometheus_port <= 65535,
            "Invalid monitoring port configuration"
        )
    
    def _print_summary(self):
        """Print validation summary."""
        print("\n" + "=" * 70)
        print("📊 CONFIGURATION VALIDATION SUMMARY")
        print("=" * 70)
        
        success_rate = (self.success_count / self.total_checks) * 100
        
        print(f"✅ Successful checks: {self.success_count}/{self.total_checks}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        
        if hasattr(self, 'config') and self.config:
            print(f"🌍 Environment: {self.config.environment.value}")
            print(f"🐛 Debug mode: {self.config.debug}")
            print(f"📦 Application: {self.config.app_name} v{self.config.app_version}")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        print("\n" + "=" * 70)
        
        if len(self.errors) == 0:
            print("🎉 PHASE 1.3 VALIDATION SUCCESSFUL!")
            print("✨ Configuration management system fully operational")
            print("🔒 Security configuration validated")
            print("🌐 API connectivity confirmed")
            print("⚙️  Environment properly configured")
            print("🎯 Ready to proceed to Phase 1.4")
        else:
            print("❌ PHASE 1.3 VALIDATION FAILED")
            print("🔧 Please address the errors above before proceeding")
        
        print("=" * 70)


def main():
    """Main validation entry point."""
    validator = ConfigurationValidator()
    success = validator.validate_all()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
