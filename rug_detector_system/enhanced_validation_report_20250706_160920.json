{"dataset_summary": {"total_contracts": 24, "by_category": {"legitimate": 10, "rug_pull": 10, "honeypot": 2, "suspicious": 2}, "by_risk_level": {"minimal": 3, "low": 7, "medium": 2, "high": 5, "critical": 7}, "by_chain": {"ethereum": 16, "bsc": 6, "polygon": 2}, "documented_incidents": 10, "total_documented_losses_usd": 3933800000}, "test_results": [{"contract_address": "******************************************", "contract_name": "AnubisDAO", "category": "rug_pull", "expected_risk_level": "critical", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.017454862594604492, "vulnerabilities_found": 13, "alerts_generated": 2, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.015713"}, {"contract_address": "******************************************", "contract_name": "Squid Game Token", "category": "rug_pull", "expected_risk_level": "critical", "predicted_risk_level": "critical", "risk_score": 0.9717500000000001, "confidence": 0.85968, "analysis_time": 0.005000114440917969, "vulnerabilities_found": 12, "alerts_generated": 1, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.020840"}, {"contract_address": "0xc2d00de94795e60fb76bc37d899170996cbda436", "contract_name": "Uranium Finance", "category": "rug_pull", "expected_risk_level": "high", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.004776954650878906, "vulnerabilities_found": 13, "alerts_generated": 2, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.025704"}, {"contract_address": "0x5d0158a5c3ddf47d4ea4517d8db0d76aa2e87563", "contract_name": "Meerkat Finance", "category": "rug_pull", "expected_risk_level": "critical", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.004230022430419922, "vulnerabilities_found": 13, "alerts_generated": 2, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.030051"}, {"contract_address": "0x3c037C4c2296f280bB318D725D0b454B76c199b9", "contract_name": "Thodex Token", "category": "rug_pull", "expected_risk_level": "high", "predicted_risk_level": "high", "risk_score": 0.7113564662488919, "confidence": 0.8612510864256977, "analysis_time": 0.0043010711669921875, "vulnerabilities_found": 8, "alerts_generated": 1, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.034620"}, {"contract_address": "0x940230b6b7ef1979a28f32196a8e3439c645ba49", "contract_name": "Fintoch", "category": "rug_pull", "expected_risk_level": "critical", "predicted_risk_level": "critical", "risk_score": 0.9717500000000001, "confidence": 0.85968, "analysis_time": 0.0042150020599365234, "vulnerabilities_found": 12, "alerts_generated": 1, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.038932"}, {"contract_address": "******************************************", "contract_name": "SaveTheKids", "category": "rug_pull", "expected_risk_level": "high", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.004052877426147461, "vulnerabilities_found": 13, "alerts_generated": 2, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.043080"}, {"contract_address": "******************************************", "contract_name": "Evolved Apes", "category": "rug_pull", "expected_risk_level": "high", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.004251956939697266, "vulnerabilities_found": 13, "alerts_generated": 2, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.047418"}, {"contract_address": "******************************************", "contract_name": "Polywhale", "category": "rug_pull", "expected_risk_level": "critical", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.0042438507080078125, "vulnerabilities_found": 13, "alerts_generated": 2, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.051823"}, {"contract_address": "0x7b65B489fE53fCE1F6548Db886C08aD73111DDd8", "contract_name": "Iron Finance", "category": "rug_pull", "expected_risk_level": "high", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.004014253616333008, "vulnerabilities_found": 13, "alerts_generated": 2, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.055942"}, {"contract_address": "0xA0b86a33E6441E6C8D3C8C8C8C8C8C8C8C8C8C8C", "contract_name": "USD Coin", "category": "legitimate", "expected_risk_level": "minimal", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.004240274429321289, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.060284"}, {"contract_address": "******************************************", "contract_name": "Wrapped Bitcoin", "category": "legitimate", "expected_risk_level": "minimal", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.0035400390625, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.063952"}, {"contract_address": "******************************************", "contract_name": "Uniswap", "category": "legitimate", "expected_risk_level": "low", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.004005908966064453, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.068056"}, {"contract_address": "******************************************", "contract_name": "Chainlink", "category": "legitimate", "expected_risk_level": "low", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.003963947296142578, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.072120"}, {"contract_address": "0x7Fc66500c84A76Ad7e9c93437bFc5Ac33E2DDaE9", "contract_name": "Aave", "category": "legitimate", "expected_risk_level": "low", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.0039250850677490234, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.076174"}, {"contract_address": "0xc00e94Cb662C3520282E6f5717214004A7f26888", "contract_name": "Compound", "category": "legitimate", "expected_risk_level": "low", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.004106044769287109, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.080390"}, {"contract_address": "0x9f8F72aA9304c8B593d555F12eF6589cC3A579A2", "contract_name": "Maker", "category": "legitimate", "expected_risk_level": "low", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.00449681282043457, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.085033"}, {"contract_address": "0x6B175474E89094C44Da98b954EedeAC495271d0F", "contract_name": "Dai Stablecoin", "category": "legitimate", "expected_risk_level": "minimal", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.004075050354003906, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.089212"}, {"contract_address": "0x7D1AfA7B718fb893dB30A3aBc0Cfc608AaCfeBB0", "contract_name": "Polygon", "category": "legitimate", "expected_risk_level": "low", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.00429987907409668, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.093630"}, {"contract_address": "0xC011a73ee8576Fb46F5E1c5751cA3B9Fe0af2a6F", "contract_name": "Synthetix Network Token", "category": "legitimate", "expected_risk_level": "low", "predicted_risk_level": "minimal", "risk_score": 0.07730000000000001, "confidence": 0.7670208, "analysis_time": 0.0043790340423583984, "vulnerabilities_found": 0, "alerts_generated": 0, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.098141"}, {"contract_address": "0x1234567890123456789012345678901234567890", "contract_name": "HoneyPot Example 1", "category": "honeypot", "expected_risk_level": "critical", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.004647016525268555, "vulnerabilities_found": 8, "alerts_generated": 2, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.102915"}, {"contract_address": "0x2345678901234567890123456789012345678901", "contract_name": "HoneyPot Example 2", "category": "honeypot", "expected_risk_level": "critical", "predicted_risk_level": "critical", "risk_score": 1.0, "confidence": 0.85968, "analysis_time": 0.0048449039459228516, "vulnerabilities_found": 8, "alerts_generated": 2, "test_passed": true, "accuracy_score": 1.0, "error_message": null, "timestamp": "2025-07-06T16:09:20.107850"}, {"contract_address": "0x3456789012345678901234567890123456789012", "contract_name": "Suspicious Token 1", "category": "suspicious", "expected_risk_level": "medium", "predicted_risk_level": "high", "risk_score": 0.6692285079368205, "confidence": 0.8634731572306269, "analysis_time": 0.004674196243286133, "vulnerabilities_found": 5, "alerts_generated": 1, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.112649"}, {"contract_address": "0x4567890123456789012345678901234567890123", "contract_name": "Suspicious Token 2", "category": "suspicious", "expected_risk_level": "medium", "predicted_risk_level": "high", "risk_score": 0.6692285079368205, "confidence": 0.8634731572306269, "analysis_time": 0.005535125732421875, "vulnerabilities_found": 5, "alerts_generated": 1, "test_passed": true, "accuracy_score": 0.75, "error_message": null, "timestamp": "2025-07-06T16:09:20.118307"}], "performance_metrics": {"confusion_matrix": {"tp": 12, "fp": 2, "tn": 10, "fn": 0}, "accuracy": 0.9166666666666666, "precision": 0.8571428571428571, "recall": 1.0, "specificity": 0.8333333333333334, "f1_score": 0.923076923076923, "false_positive_rate": 0.16666666666666666, "avg_analysis_time": 0.004886428515116374, "avg_confidence": 0.8214535583702895, "meets_accuracy_target": true, "meets_fpr_target": false}, "accuracy_analysis": {"accuracy_by_category": {"legitimate": 1.0, "rug_pull": 1.0, "honeypot": 1.0, "suspicious": 1.0}, "accuracy_by_risk_level": {"minimal": 1.0, "low": 1.0, "medium": 1.0, "high": 1.0, "critical": 1.0}, "overall_accuracy": 1.0}, "production_readiness": {"criteria_met": {"overall_accuracy": true, "rug_pull_detection": true, "false_positive_rate": false, "confidence_threshold": true, "no_critical_errors": true}, "overall_ready": false, "go_no_go_decision": "NO_GO", "accuracy_targets": {"overall_accuracy": 0.9, "rug_pull_detection_rate": 0.95, "false_positive_rate": 0.05, "confidence_threshold": 0.7}, "actual_performance": {"overall_accuracy": 1.0, "rug_pull_detection_rate": 1.0, "false_positive_rate": 0.16666666666666666, "avg_confidence": 0.8214535583702895}}}