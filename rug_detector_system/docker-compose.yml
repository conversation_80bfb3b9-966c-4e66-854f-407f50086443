version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: rug_detector_postgres_dev
    environment:
      POSTGRES_DB: rug_detector_dev
      POSTGRES_USER: rug_detector_user
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - rug_detector_dev_network

  # Redis Cache & Message Queue
  redis:
    image: redis:7-alpine
    container_name: rug_detector_redis_dev
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - rug_detector_dev_network

  # Rug Detector Development Application
  rug_detector:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rug_detector_app_dev
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=*********************************************************/rug_detector_dev
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app/src
    volumes:
      - ./src:/app/src
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - rug_detector_dev_network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  rug_detector_dev_network:
    driver: bridge
