# Rug Detector System - Development Makefile
# Production-grade development workflow automation

.PHONY: help install install-dev clean lint format type-check test test-cov security audit run docker-build docker-run docs

# Default target
help: ## Show this help message
	@echo "Rug Detector System - Development Commands"
	@echo "=========================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Environment setup
install: ## Install production dependencies
	pip install -e .

install-dev: ## Install development dependencies
	pip install -e ".[dev,test,docs]"
	pre-commit install

# Cleaning
clean: ## Clean build artifacts and cache
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete

# Code quality
lint: ## Run linting (flake8)
	flake8 src/ tests/

format: ## Format code (black + isort)
	black src/ tests/
	isort src/ tests/

format-check: ## Check code formatting
	black --check src/ tests/
	isort --check-only src/ tests/

type-check: ## Run type checking (mypy)
	MYPYPATH=src mypy --explicit-package-bases src/

# Security
security: ## Run security checks (bandit + safety)
	bandit -r src/ -f json -o bandit-report.json
	safety check --output json > safety-report.json

audit: ## Full security audit
	safety check
	bandit -r src/
	pip-audit

# Testing
test: ## Run tests
	pytest tests/ -v

test-cov: ## Run tests with coverage
	pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing

test-integration: ## Run integration tests
	pytest tests/ -v -m integration

test-unit: ## Run unit tests only
	pytest tests/ -v -m "not integration"

test-slow: ## Run all tests including slow ones
	pytest tests/ -v --runslow

# Pre-commit
pre-commit: ## Run pre-commit hooks on all files
	pre-commit run --all-files

pre-commit-update: ## Update pre-commit hooks
	pre-commit autoupdate

# Application
run: ## Run the application
	python -m src.main

run-dev: ## Run in development mode
	ENVIRONMENT=development python -m src.main

run-test-connection: ## Run connection test
	python tests/test_connection.py

# Database
db-init: ## Initialize database
	python -m src.database init

db-migrate: ## Run database migrations
	python -m src.database migrate

db-reset: ## Reset database (WARNING: destructive)
	python -m src.database reset

# Docker (if needed later)
docker-build: ## Build Docker image
	docker build -t rug-detector-system .

docker-run: ## Run Docker container
	docker run -p 8000:8000 rug-detector-system

# Documentation
docs: ## Build documentation
	cd docs && make html

docs-serve: ## Serve documentation locally
	cd docs/_build/html && python -m http.server 8080

# Development workflow
dev-setup: clean install-dev ## Complete development setup
	@echo "Development environment setup complete!"
	@echo "Run 'make run-test-connection' to verify setup"

dev-check: format-check lint type-check security test ## Run all checks
	@echo "All development checks passed!"

# CI/CD simulation
ci: clean install-dev format-check lint type-check security test-cov ## Simulate CI pipeline
	@echo "CI pipeline simulation complete!"

# Quality gates
quality-gate: ## Run quality gate checks (95% coverage required)
	pytest tests/ --cov=src --cov-fail-under=95 --cov-report=term-missing

# Performance
benchmark: ## Run performance benchmarks
	python -m pytest tests/benchmarks/ -v --benchmark-only

profile: ## Profile application performance
	python -m cProfile -o profile.stats -m src.main
	python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(20)"

# Monitoring
health-check: ## Check application health
	curl -f http://localhost:8000/health || echo "Application not running"

metrics: ## Show application metrics
	curl -s http://localhost:9090/metrics | grep rug_detector

# Release
version: ## Show current version
	python -c "from src import __version__; print(__version__)"

release-check: ci quality-gate ## Pre-release validation
	@echo "Release validation complete!"

# M1 Mac specific optimizations
m1-optimize: ## Optimize for M1 Mac performance
	pip install --upgrade --force-reinstall --no-cache-dir numpy scipy pandas
	@echo "M1 Mac optimizations applied!"

# Environment validation
validate-env: ## Validate environment setup
	@echo "Validating environment..."
	@python --version
	@pip --version
	@which python
	@echo "Python path: $$(which python)"
	@echo "Virtual environment: $$VIRTUAL_ENV"
	@echo "Architecture: $$(uname -m)"
	@echo "Environment validation complete!"
