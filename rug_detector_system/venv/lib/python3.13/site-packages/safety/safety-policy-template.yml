# Safety Security and License Configuration file
# We recommend checking this file into your source control in the root of your Python project
# If this file is named .safety-policy.yml and is in the same directory where you run `safety check` it will be used by default.
# Otherwise, you can use the flag `safety check --policy-file <path-to-this-file>` to specify a custom location and name for the file.
# To validate and review your policy file, run the validate command: `safety validate policy_file --path <path-to-this-file>`
project: # Project to associate the scans with on pyup.io.
    id: ''
organization:
    id: ''
    name: ''
security: # configuration for the `safety check` command
    ignore-unpinned-requirements: True # This will ignore dependencies found in requirement files without a pinned specification. Like requests or requests>=0 or django>=2.2
    ignore-cvss-severity-below: 0 # A severity number between 0 and 10. Some helpful reference points: 9=ignore all vulnerabilities except CRITICAL severity. 7=ignore all vulnerabilities except CRITICAL & HIGH severity. 4=ignore all vulnerabilities except CRITICAL, HIGH & MEDIUM severity.
    ignore-cvss-unknown-severity: False # True or False. We recommend you set this to False.
    ignore-vulnerabilities: # Here you can list multiple specific vulnerabilities you want to ignore (optionally for a time period)
        # We recommend making use of the optional `reason` and `expires` keys for each vulnerability that you ignore.
        25853: # Example vulnerability ID
            reason: we don't use the vulnerable function # optional, for internal note purposes to communicate with your team. This reason will be reported in the Safety reports
            expires: '2022-10-21' # datetime string - date this ignore will expire, best practice to use this variable
    continue-on-vulnerability-error: False # Suppress non-zero exit codes when vulnerabilities are found. Enable this in pipelines and CI/CD processes if you want to pass builds that have vulnerabilities. We recommend you set this to False.
alert: # configuration for the `safety alert` command
    security:
        # Configuration specific to Safety's GitHub Issue alerting
        github-issue:
            # Same as for security - these allow controlling if this alert will fire based
            # on severity information.
            # default: not set
            # ignore-cvss-severity-below: 6
            # ignore-cvss-unknown-severity: False

            # Add a label to pull requests with the cvss severity, if available
            # default: true
            # label-severity: True

            # Add a label to pull requests, default is 'security'
            # requires private repo permissions, even on public repos
            # default: security
            # labels:
            #  - security

            # Assign users to pull requests, default is not set
            # requires private repo permissions, even on public repos
            # default: empty
            # assignees:
            #  - example-user

            # Prefix to give issues when creating them. Note that changing
            # this might cause duplicate issues to be created.
            # default: "[PyUp] "
            # issue-prefix: "[PyUp] "

        # Configuration specific to Safety's GitHub PR alerting
        github-pr:
            # Same as for security - these allow controlling if this alert will fire based
            # on severity information.
            # default: not set
            # ignore-cvss-severity-below: 6
            # ignore-cvss-unknown-severity: False

            # Set the default branch (ie, main, master)
            # default: empty, the default branch on GitHub
            branch: ''

            # Add a label to pull requests with the cvss severity, if available
            # default: true
            # label-severity: True

            # Add a label to pull requests, default is 'security'
            # requires private repo permissions, even on public repos
            # default: security
            # labels:
            #  - security

            # Assign users to pull requests, default is not set
            # requires private repo permissions, even on public repos
            # default: empty
            # assignees:
            #  - example-user

            # Configure the branch prefix for PRs created by this alert.
            # NB: Changing this will likely cause duplicate PRs.
            # default: pyup/
            branch-prefix: pyup/

            # Set a global prefix for PRs
            # default: "[PyUp] "
            pr-prefix: "[PyUp] "
security-updates: # configuration for the `safety check --apply-security-updates` command
    auto-security-updates-limit:
        # A list of allowed values: major, minor, patch
        - patch
