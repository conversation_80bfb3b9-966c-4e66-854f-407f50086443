PUBLIC_REPOSITORY_URL = "https://pkgs.safetycli.com/repository/public/pypi/simple/"
ORGANIZATION_REPOSITORY_URL = "https://pkgs.safetycli.com/repository/{}/pypi/simple/"
PROJECT_REPOSITORY_URL = (
    "https://pkgs.safetycli.com/repository/{}/project/{}/pypi/simple/"
)
PROJECT_CONFIG = ".safety-project.ini"

MOST_FREQUENTLY_DOWNLOADED_PYPI_PACKAGES = [
    "boto3",
    "urllib3",
    "botocore",
    "requests",
    "setuptools",
    "certifi",
    "idna",
    "charset-normalizer",
    "aiobotocore",
    "typing-extensions",
    "python-dateutil",
    "s3transfer",
    "packaging",
    "grpcio-status",
    "s3fs",
    "six",
    "fsspec",
    "pyyaml",
    "numpy",
    "importlib-metadata",
    "cryptography",
    "zipp",
    "cffi",
    "pip",
    "pandas",
    "google-api-core",
    "pycparser",
    "pydantic",
    "protobuf",
    "wheel",
    "jmespath",
    "attrs",
    "rsa",
    "pyasn1",
    "click",
    "platformdirs",
    "pytz",
    "colorama",
    "jinja2",
    "awscli",
    "markupsafe",
    "tomli",
    "pyjwt",
    "googleapis-common-protos",
    "filelock",
    "virtualenv",
    "cachetools",
    "wrapt",
    "google-auth",
    "pluggy",
    "pytest",
    "pydantic-core",
    "pyparsing",
    "docutils",
    "pyarrow",
    "pyasn1-modules",
    "requests-oauthlib",
    "aiohttp",
    "scipy",
    "jsonschema",
    "oauthlib",
    "sqlalchemy",
    "iniconfig",
    "exceptiongroup",
    "yarl",
    "decorator",
    "multidict",
    "psutil",
    "soupsieve",
    "greenlet",
    "tzdata",
    "pillow",
    "isodate",
    "pygments",
    "beautifulsoup4",
    "annotated-types",
    "requests-toolbelt",
    "frozenlist",
    "tomlkit",
    "pyopenssl",
    "aiosignal",
    "distlib",
    "async-timeout",
    "more-itertools",
    "openpyxl",
    "tqdm",
    "et-xmlfile",
    "grpcio",
    "deprecated",
    "cloudpickle",
    "lxml",
    "pynacl",
    "werkzeug",
    "proto-plus",
    "azure-core",
    "google-cloud-storage",
    "asn1crypto",
    "coverage",
    "websocket-client",
    "msgpack",
    "h11",
    "rich",
    "dill",
    "pexpect",
    "sniffio",
    "anyio",
    "mypy-extensions",
    "ptyprocess",
    "importlib-resources",
    "sortedcontainers",
    "matplotlib",
    "chardet",
    "rpds-py",
    "grpcio-tools",
    "aiohappyeyeballs",
    "flask",
    "httpx",
    "referencing",
    "scikit-learn",
    "jsonschema-specifications",
    "httpcore",
    "pyzmq",
    "poetry-core",
    "keyring",
    "google-cloud-core",
    "python-dotenv",
    "pathspec",
    "markdown-it-py",
    "pkginfo",
    "msal",
    "networkx",
    "bcrypt",
    "mdurl",
    "gitpython",
    "psycopg2-binary",
    "poetry-plugin-export",
    "google-resumable-media",
    "paramiko",
    "kiwisolver",
    "smmap",
    "gitdb",
    "xmltodict",
    "snowflake-connector-python",
    "tabulate",
    "cycler",
    "typedload",
    "jaraco-classes",
    "jeepney",
    "secretstorage",
    "ruamel-yaml",
    "tenacity",
    "wcwidth",
    "build",
    "backoff",
    "shellingham",
    "threadpoolctl",
    "regex",
    "itsdangerous",
    "portalocker",
    "py",
    "google-crc32c",
    "rapidfuzz",
    "pyproject-hooks",
    "py4j",
    "google-cloud-bigquery",
    "fastjsonschema",
    "sqlparse",
    "mccabe",
    "pytest-cov",
    "awswrangler",
    "trove-classifiers",
    "msal-extensions",
    "azure-storage-blob",
    "google-api-python-client",
    "pycodestyle",
    "joblib",
    "google-auth-oauthlib",
    "ruamel-yaml-clib",
    "tzlocal",
    "docker",
    "alembic",
    "fonttools",
    "prompt-toolkit",
    "cachecontrol",
    "azure-identity",
    "distro",
    "marshmallow",
    "uritemplate",
    "isort",
    "cython",
    "ply",
    "httplib2",
    "redis",
    "pymysql",
    "pyrsistent",
    "gym-notices",
    "google-auth-httplib2",
    "poetry",
    "blinker",
    "defusedxml",
    "dnspython",
    "dulwich",
    "toml",
    "gunicorn",
    "crashtest",
    "markdown",
    "nest-asyncio",
    "babel",
    "cleo",
    "sentry-sdk",
    "opentelemetry-api",
    "scramp",
    "multiprocess",
    "installer",
    "termcolor",
    "black",
    "huggingface-hub",
    "mock",
    "msrest",
    "pendulum",
    "requests-aws4auth",
    "ipython",
    "pyflakes",
    "pycryptodomex",
    "grpc-google-iam-v1",
    "types-requests",
    "azure-common",
    "traitlets",
    "fastapi",
    "setuptools-scm",
    "tornado",
    "flake8",
    "contourpy",
    "prometheus-client",
    "future",
    "openai",
    "mako",
    "pycryptodome",
    "imageio",
    "jedi",
    "webencodings",
    "pygithub",
    "parso",
    "transformers",
    "typing-inspect",
    "kubernetes",
    "jsonpointer",
    "matplotlib-inline",
    "starlette",
    "loguru",
    "opentelemetry-sdk",
    "retry",
    "argcomplete",
    "pkgutil-resolve-name",
    "redshift-connector",
    "elasticsearch",
    "pymongo",
    "opentelemetry-semantic-conventions",
    "pytzdata",
    "pytest-runner",
    "asgiref",
    "pg8000",
    "bs4",
    "datadog",
    "debugpy",
    "python-json-logger",
    "jsonpath-ng",
    "uvicorn",
    "executing",
    "smart-open",
    "zope-interface",
    "asttokens",
    "typer",
    "aioitertools",
    "apache-airflow",
    "sagemaker",
    "arrow",
    "google-pasta",
    "pyspark",
    "humanfriendly",
    "websockets",
    "stack-data",
    "shapely",
    "pure-eval",
    "torch",
    "oscrypto",
    "tokenizers",
    "pysocks",
    "sphinx",
    "typeguard",
    "tox",
    "scikit-image",
    "requests-file",
    "google-cloud-pubsub",
    "pytest-mock",
    "google-cloud-secret-manager",
    "snowflake-sqlalchemy",
    "mysql-connector-python",
    "pylint",
    "jupyter-core",
    "jupyter-client",
    "astroid",
    "jsonpatch",
    "setproctitle",
    "adal",
    "types-python-dateutil",
    "ipykernel",
    "xgboost",
    "orjson",
    "schema",
    "tb-nightly",
    "nbconvert",
    "xlrd",
    "toolz",
    "appdirs",
    "aiofiles",
    "sympy",
    "opensearch-py",
    "nodeenv",
    "pywavelets",
    "jaraco-functools",
    "jupyter-server",
    "nbformat",
    "jupyterlab",
    "progressbar2",
    "comm",
    "identify",
    "bleach",
    "mypy",
    "pathos",
    "pyodbc",
    "pre-commit",
    "xlsxwriter",
    "rfc3339-validator",
    "aws-requests-auth",
    "gym",
    "pox",
    "ppft",
    "mistune",
    "aenum",
    "jaraco-context",
    "tinycss2",
    "pbr",
    "google-cloud-appengine-logging",
    "notebook",
    "db-dtypes",
    "mpmath",
    "sentencepiece",
    "responses",
    "cfgv",
    "cattrs",
    "python-utils",
    "slack-sdk",
    "jupyterlab-server",
    "nbclient",
    "lz4",
    "ipywidgets",
    "sshtunnel",
    "absl-py",
    "widgetsnbextension",
    "watchdog",
    "asynctest",
    "semver",
    "rfc3986",
    "google-cloud-aiplatform",
    "jupyterlab-widgets",
    "altair",
    "pandas-gbq",
    "click-man",
    "tensorboard",
    "smdebug-rulesconfig",
    "simplejson",
    "text-unidecode",
    "argon2-cffi",
    "apache-airflow-providers-common-sql",
    "snowballstemmer",
    "azure-mgmt-core",
    "docker-pycreds",
    "nltk",
    "python-slugify",
    "croniter",
    "structlog",
    "selenium",
    "antlr4-python3-runtime",
    "google-cloud-logging",
    "argon2-cffi-bindings",
    "azure-storage-file-datalake",
    "django",
    "pydeequ",
    "pytest-xdist",
    "h5py",
    "google-cloud-resource-manager",
    "dataclasses",
    "execnet",
    "send2trash",
    "opentelemetry-proto",
    "google-cloud-bigquery-storage",
    "oauth2client",
    "dataclasses-json",
    "json5",
    "tiktoken",
    "wandb",
    "databricks-sql-connector",
    "langchain-core",
    "overrides",
    "prettytable",
    "pandocfilters",
    "semantic-version",
    "jupyterlab-pygments",
    "msrestazure",
    "safetensors",
    "hvac",
    "colorlog",
    "imbalanced-learn",
    "monotonic",
    "seaborn",
    "alabaster",
    "terminado",
    "webcolors",
    "ordered-set",
    "graphql-core",
    "notebook-shim",
    "lazy-object-proxy",
    "funcsigs",
    "numba",
    "llvmlite",
    "gremlinpython",
    "xxhash",
    "great-expectations",
    "flatbuffers",
    "pydata-google-auth",
    "fqdn",
    "uri-template",
    "imagesize",
    "opentelemetry-exporter-otlp-proto-common",
    "isoduration",
    "backports-tarfile",
    "wsproto",
    "tensorflow",
    "thrift",
    "hypothesis",
    "rfc3986-validator",
    "trio",
    "inflection",
    "html5lib",
    "plotly",
    "entrypoints",
    "sphinxcontrib-serializinghtml",
    "jupyter-events",
    "lockfile",
    "coloredlogs",
    "sphinxcontrib-htmlhelp",
    "cached-property",
    "sphinxcontrib-qthelp",
    "sphinxcontrib-devhelp",
    "sphinxcontrib-applehelp",
    "gast",
    "azure-cli",
    "azure-datalake-store",
    "opentelemetry-exporter-otlp-proto-http",
    "pyproject-api",
    "azure-mgmt-resource",
    "async-lru",
    "faker",
    "sphinxcontrib-jsmath",
    "nose",
    "opencv-python",
    "outcome",
    "statsmodels",
    "readme-renderer",
    "jupyter-server-terminals",
    "libcst",
    "retrying",
    "datasets",
    "aniso8601",
    "pybind11",
    "databricks-sdk",
    "pyroaring",
    "azure-keyvault-secrets",
    "email-validator",
    "argparse",
    "parameterized",
    "docopt",
    "google-cloud-audit-log",
    "confluent-kafka",
    "kafka-python",
    "pymssql",
    "zeep",
    "gcsfs",
    "click-plugins",
    "jupyter-lsp",
    "ruff",
    "deepdiff",
    "docstring-parser",
    "tblib",
    "time-machine",
    "jiter",
    "patsy",
    "azure-storage-common",
    "deprecation",
    "azure-nspkg",
    "databricks-cli",
    "nh3",
    "twine",
    "invoke",
    "delta-spark",
    "watchtower",
    "mlflow",
    "pydantic-settings",
    "azure-mgmt-storage",
    "opentelemetry-exporter-otlp-proto-grpc",
    "applicationinsights",
    "dbt-core",
    "freezegun",
    "pickleshare",
    "apache-airflow-providers-ssh",
    "python-multipart",
    "langchain",
    "uv",
    "unidecode",
    "azure-keyvault-keys",
    "azure-cosmos",
    "pytest-metadata",
    "pipenv",
    "tensorboard-data-server",
    "azure-graphrbac",
    "google-cloud-kms",
    "backcall",
    "trio-websocket",
    "azure-keyvault",
    "pytest-asyncio",
    "psycopg2",
    "google-cloud-dataproc",
    "keras",
    "datetime",
    "zope-event",
    "apache-airflow-providers-google",
    "backports-zoneinfo",
    "google-cloud-monitoring",
    "looker-sdk",
    "azure-mgmt-containerregistry",
    "makefun",
    "google-cloud-vision",
    "mlflow-skinny",
    "hatchling",
    "spacy",
    "torchvision",
    "apache-airflow-providers-snowflake",
    "google-cloud-spanner",
    "google-cloud-container",
    "nvidia-nccl-cu12",
    "triton",
    "gevent",
    "google-cloud-dlp",
    "uvloop",
    "simple-salesforce",
    "tldextract",
    "analytics-python",
    "apache-airflow-providers-databricks",
    "tensorflow-estimator",
    "google-cloud-bigquery-datatransfer",
    "azure-mgmt-keyvault",
    "azure-mgmt-cosmosdb",
    "azure-mgmt-compute",
    "graphviz",
    "google-cloud-tasks",
    "ujson",
    "opentelemetry-instrumentation",
    "azure-mgmt-authorization",
    "fastavro",
    "httptools",
    "pathlib2",
    "azure-mgmt-network",
    "google-cloud-datacatalog",
    "pkce",
    "google-ads",
    "opt-einsum",
    "sh",
    "jsondiff",
    "azure-mgmt-msi",
    "google-cloud-firestore",
    "evergreen-py",
    "google-cloud-bigtable",
    "astunparse",
    "watchfiles",
    "configparser",
    "flask-appbuilder",
    "fabric",
    "azure-mgmt-recoveryservices",
    "apache-airflow-providers-mysql",
    "scp",
    "db-contrib-tool",
    "google-cloud-build",
    "omegaconf",
    "azure-mgmt-monitor",
    "ecdsa",
    "gspread",
    "azure-mgmt-signalr",
    "azure-mgmt-containerinstance",
    "blis",
    "thinc",
    "bitarray",
    "murmurhash",
    "pycrypto",
    "dask",
    "requests-mock",
    "catalogue",
    "cymem",
    "azure-mgmt-sql",
    "preshed",
    "google-cloud-workflows",
    "opentelemetry-exporter-otlp",
    "azure-mgmt-web",
    "google-cloud-redis",
    "azure-batch",
    "kombu",
    "pywin32",
    "azure-data-tables",
    "wasabi",
    "azure-mgmt-containerservice",
    "azure-mgmt-servicebus",
    "azure-mgmt-redis",
    "google-cloud-dataplex",
    "srsly",
    "pytimeparse",
    "google-cloud-language",
    "authlib",
    "google-cloud-automl",
    "google-cloud-videointelligence",
    "google-cloud-os-login",
    "azure-mgmt-rdbms",
    "brotli",
    "pyserial",
    "azure-mgmt-dns",
    "langchain-community",
    "nvidia-cudnn-cu12",
    "texttable",
    "azure-mgmt-advisor",
    "google-cloud-memcache",
    "azure-mgmt-eventhub",
    "tensorflow-serving-api",
    "gsutil",
    "lark",
    "azure-cli-core",
    "flask-cors",
    "pysftp",
    "celery",
    "langcodes",
    "azure-mgmt-batch",
    "azure-mgmt-loganalytics",
    "azure-mgmt-cdn",
    "ninja",
    "azure-mgmt-recoveryservicesbackup",
    "azure-mgmt-iothub",
    "azure-mgmt-search",
    "azure-mgmt-marketplaceordering",
    "azure-mgmt-trafficmanager",
    "azure-mgmt-managementgroups",
    "pip-tools",
    "azure-mgmt-cognitiveservices",
    "azure-mgmt-devtestlabs",
    "azure-mgmt-eventgrid",
    "python-gnupg",
    "jira",
    "pypdf2",
    "azure-mgmt-applicationinsights",
    "azure-mgmt-servicefabric",
    "billiard",
    "azure-mgmt-media",
    "azure-mgmt-billing",
    "ratelimit",
    "azure-mgmt-iothubprovisioningservices",
    "azure-mgmt-policyinsights",
    "azure-mgmt-nspkg",
    "google-cloud-orchestration-airflow",
    "apache-airflow-providers-cncf-kubernetes",
    "azure-mgmt-batchai",
    "azure-mgmt-iotcentral",
    "azure-mgmt-datamigration",
    "graphql-relay",
    "azure-mgmt-maps",
    "graphene",
    "azure-appconfiguration",
    "amqp",
    "google-cloud-dataproc-metastore",
    "mdit-py-plugins",
    "google-cloud-translate",
    "ijson",
    "sqlalchemy-bigquery",
    "vine",
    "nvidia-cublas-cu12",
    "nvidia-nvjitlink-cu12",
    "spacy-loggers",
    "spacy-legacy",
    "levenshtein",
    "agate",
    "azure-mgmt-datalake-nspkg",
    "knack",
    "yapf",
    "awscrt",
    "azure-mgmt-datalake-store",
    "google-cloud-dataform",
    "types-pyyaml",
    "confection",
    "propcache",
    "google-cloud-speech",
    "nvidia-cuda-runtime-cu12",
    "opencensus",
    "opencensus-context",
    "nvidia-cuda-cupti-cu12",
    "nvidia-cuda-nvrtc-cu12",
    "parsedatetime",
    "nvidia-cusparse-cu12",
    "nvidia-cufft-cu12",
    "nvidia-cusolver-cu12",
    "grpcio-gcp",
    "nvidia-curand-cu12",
    "google-cloud-texttospeech",
    "typing",
    "humanize",
    "pytest-html",
    "langsmith",
    "nvidia-nvtx-cu12",
    "flask-sqlalchemy",
    "opentelemetry-util-http",
    "narwhals",
    "azure-multiapi-storage",
    "gcloud-aio-storage",
    "pycountry",
    "jsonpickle",
    "zstandard",
    "avro-python3",
    "libclang",
    "apispec",
    "gcloud-aio-auth",
    "azure-storage-queue",
    "contextlib2",
    "azure-mgmt-datalake-analytics",
    "gcloud-aio-bigquery",
    "azure-mgmt-reservations",
    "javaproperties",
    "tensorflow-io-gcs-filesystem",
    "azure-loganalytics",
    "djangorestframework",
    "azure-mgmt-consumption",
    "hpack",
    "google-cloud-compute",
    "click-didyoumean",
    "azure-mgmt-relay",
    "parsimonious",
    "azure-synapse-artifacts",
    "python-magic",
    "azure-cli-telemetry",
    "click-repl",
    "moto",
    "pyathena",
    "pyproj",
    "protobuf3-to-dict",
    "durationpy",
    "stevedore",
    "python-daemon",
    "azure-synapse-spark",
    "apache-airflow-providers-http",
    "mypy-boto3-s3",
    "pyspnego",
    "cfn-lint",
    "astor",
    "azure-mgmt-apimanagement",
    "h2",
    "hyperframe",
    "azure-mgmt-hdinsight",
    "azure-mgmt-privatedns",
    "boto3-stubs",
    "mashumaro",
    "dateparser",
    "ml-dtypes",
    "mysqlclient",
    "azure-mgmt-security",
    "opencensus-ext-azure",
    "azure-mgmt-synapse",
    "azure-mgmt-kusto",
    "azure-mgmt-netapp",
    "grpcio-health-checking",
    "azure-mgmt-redhatopenshift",
    "iso8601",
    "lightgbm",
    "azure-mgmt-appconfiguration",
    "azure-keyvault-administration",
    "boto",
    "azure-mgmt-sqlvirtualmachine",
    "azure-mgmt-imagebuilder",
    "azure-synapse-accesscontrol",
    "enum34",
    "azure-mgmt-servicelinker",
    "azure-mgmt-botservice",
    "azure-mgmt-servicefabricmanagedclusters",
    "jpype1",
    "python-jose",
    "azure-mgmt-databoxedge",
    "azure-synapse-managedprivateendpoints",
    "azure-mgmt-extendedlocation",
    "office365-rest-python-client",
    "onnxruntime",
    "azure-mgmt-managedservices",
    "cramjam",
    "urllib3-secure-extra",
    "avro",
    "holidays",
    "psycopg",
    "botocore-stubs",
    "fasteners",
    "resolvelib",
    "partd",
    "hyperlink",
    "leather",
    "apscheduler",
    "flask-wtf",
    "jupyter",
    "marisa-trie",
    "locket",
    "jupyter-console",
    "python-http-client",
    "elastic-transport",
    "dbt-extractor",
    "tensorflow-text",
    "language-data",
    "inflect",
    "fuzzywuzzy",
    "cytoolz",
    "cmake",
    "parse",
    "python-gitlab",
    "mypy-boto3-rds",
    "tifffile",
    "eth-utils",
    "eth-hash",
    "netaddr",
    "incremental",
    "setuptools-rust",
    "python-levenshtein",
    "geopandas",
    "twisted",
    "langchain-text-splitters",
    "types-awscrt",
    "apache-airflow-providers-fab",
    "yamllint",
    "cligj",
    "sphinx-rtd-theme",
    "azure-mgmt-deploymentmanager",
    "pytest-timeout",
    "lazy-loader",
    "wtforms",
    "bytecode",
    "accelerate",
    "polars",
    "sendgrid",
    "frozendict",
    "flask-login",
    "opentelemetry-instrumentation-requests",
    "jaydebeapi",
    "eth-typing",
    "dacite",
    "types-pytz",
    "py-cpuinfo",
    "querystring-parser",
    "universal-pathlib",
    "dbt-semantic-interfaces",
    "magicattr",
    "cssselect",
    "fastparquet",
    "opencv-python-headless",
    "automat",
    "unicodecsv",
    "constantly",
    "kfp",
    "ddtrace",
    "logbook",
    "envier",
    "cloudpathlib",
    "types-s3transfer",
    "google-cloud-dataflow-client",
    "sqlalchemy-utils",
    "apache-beam",
    "validators",
    "bracex",
    "apache-airflow-providers-ftp",
    "phonenumbers",
    "diskcache",
    "mergedeep",
    "slicer",
    "shap",
    "python-docx",
    "types-urllib3",
    "pytest-rerunfailures",
    "types-setuptools",
    "pathy",
    "pytz-deprecation-shim",
    "yappi",
    "pydot",
    "types-protobuf",
    "ipython-genutils",
    "pytorch-lightning",
    "fire",
    "apache-airflow-providers-sqlite",
    "nvidia-cublas-cu11",
    "azure-storage-file-share",
    "mmh3",
    "azure-mgmt-datafactory",
    "azure-servicebus",
    "nvidia-cudnn-cu11",
    "inject",
    "typed-ast",
    "connexion",
    "configargparse",
    "linkify-it-py",
    "aws-sam-translator",
    "slackclient",
    "eth-abi",
    "pydash",
    "timm",
    "datadog-api-client",
    "nvidia-cuda-runtime-cu11",
    "nvidia-cuda-nvrtc-cu11",
    "geographiclib",
    "gradio",
    "cron-descriptor",
    "ansible",
    "azure-kusto-data",
    "django-cors-headers",
    "junit-xml",
    "geopy",
    "uc-micro-py",
    "pyee",
    "xarray",
    "ansible-core",
    "pypdf",
    "pyotp",
    "starkbank-ecdsa",
    "geoip2",
    "multimethod",
    "eth-account",
    "meson",
    "jellyfish",
    "futures",
    "cachelib",
    "flask-caching",
    "natsort",
    "autopep8",
    "torchaudio",
    "torchmetrics",
    "pydub",
    "pandera",
    "pyhcl",
    "apache-airflow-providers-slack",
    "oracledb",
    "google-cloud-run",
    "h3",
    "apache-airflow-providers-amazon",
    "sqlalchemy-spanner",
    "events",
    "google-cloud-batch",
    "requests-ntlm",
    "bottle",
    "google-cloud-storage-transfer",
    "junitparser",
    "apache-airflow-providers-smtp",
    "apache-airflow-providers-imap",
    "emoji",
    "crcmod",
    "statsd",
    "limits",
    "apache-airflow-providers-common-io",
    "methodtools",
    "asyncpg",
    "strictyaml",
    "wcmatch",
    "marshmallow-sqlalchemy",
    "faiss-cpu",
    "sentence-transformers",
    "psycopg-binary",
    "azure-keyvault-certificates",
    "django-filter",
    "maxminddb",
    "weasel",
    "gql",
    "onnx",
    "fiona",
    "boltons",
    "dbt-common",
    "bidict",
    "keras-applications",
    "json-merge-patch",
    "elasticsearch-dsl",
    "ftfy",
    "swagger-ui-bundle",
    "tableauserverclient",
    "flask-jwt-extended",
    "lightning-utilities",
    "meson-python",
    "google-cloud",
]

MSG_NOT_AUTHENTICATED_TOOL = "{tool_name} is aliased to Safety to ensure your package installations are audited and secured."
MSG_NOT_AUTHENTICATED_TOOL_NO_TTY = "Safety is not authenticated - this usage of {tool_name} will not be audited by Safety. Please run safety auth login or set an environment variable for SAFETY_API_KEY if running in a script."
