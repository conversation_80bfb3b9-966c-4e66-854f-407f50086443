# Package Licenses

| Name | Version | License |
|------|---------|----------|
| annotated-types | 0.7.0 | MIT License |
| anyio | 4.5.2 | MIT |
| authlib | 1.3.2 | BSD-3-Clause |
| certifi | 2025.1.31 | MPL-2.0 |
| cffi | 1.17.1 | MIT |
| charset-normalizer | 3.4.1 | MIT |
| click | 8.1.8 | BSD License |
| cryptography | 44.0.2 | Apache-2.0 OR BSD-3-Clause |
| dparse | 0.6.4 | MIT license |
| exceptiongroup | 1.2.2 | MIT License |
| filelock | 3.16.1 | Unlicense |
| h11 | 0.14.0 | MIT |
| httpcore | 1.0.8 | BSD-3-Clause |
| httpx | 0.28.1 | BSD-3-Clause |
| idna | 3.10 | BSD License |
| jinja2 | 3.1.6 | BSD License |
| joblib | 1.4.2 | BSD 3-Clause |
| markdown-it-py | 3.0.0 | MIT License |
| markupsafe | 2.1.5 | BSD-3-Clause |
| marshmallow | 3.22.0 | MIT License |
| mdurl | 0.1.2 | MIT License |
| nltk | 3.9.1 | Apache License, Version 2.0 |
| packaging | 25.0 | Apache Software License |
| pip | 23.0.1 | MIT |
| psutil | 6.1.1 | BSD-3-Clause |
| pycparser | 2.22 | BSD-3-Clause |
| pydantic | 2.9.2 | MIT |
| pydantic-core | 2.23.4 | MIT |
| pygments | 2.19.1 | BSD-2-Clause |
| regex | 2024.11.6 | Apache Software License |
| requests | 2.32.3 | Apache-2.0 |
| rich | 14.0.0 | MIT |
| ruamel-yaml | 0.18.10 | MIT license |
| ruamel-yaml-clib | 0.2.8 | MIT |
| safety-schemas | 0.0.14 | MIT |
| setuptools | 75.3.2 | MIT License |
| shellingham | 1.5.4 | ISC License |
| sniffio | 1.3.1 | MIT OR Apache-2.0 |
| tenacity | 9.0.0 | Apache 2.0 |
| tomli | 2.2.1 | MIT License |
| tomlkit | 0.13.2 | MIT |
| tqdm | 4.67.1 | MPL-2.0 AND MIT |
| typer | 0.15.2 | MIT License |
| typing-extensions | 4.13.2 | PSF-2.0 |
| urllib3 | 2.2.3 | MIT License |
