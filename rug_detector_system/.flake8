[flake8]
# Configuration for flake8 linting
# Compatible with Black formatter (88 character line length)

# Maximum line length to match Black
max-line-length = 88

# Ignore specific error codes that conflict with Black
ignore = E203,W503,E501

# File patterns to exclude
exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    .mypy_cache,
    .pytest_cache,
    migrations

# Enable specific error codes
select =
    # Pyflakes errors
    F,
    # Pycodestyle errors
    E,
    # Pycodestyle warnings
    W,
    # McCabe complexity
    C90

# Maximum cyclomatic complexity
max-complexity = 10

# Show source code for each error
show-source = True

# Show pep8 violation statistics
statistics = True

# Count errors and warnings
count = True

# Format for error messages
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s

# Per-file ignores (if needed)
per-file-ignores =
    # Tests can have longer lines and unused imports
    tests/*:E501,F401,F811
    # __init__.py files can have unused imports
    __init__.py:F401
    # Configuration files
    */settings.py:E501
    */config.py:E501
