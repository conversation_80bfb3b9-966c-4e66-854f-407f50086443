{"test_results": [{"test_type": "static_analysis", "contract_name": "Standard ERC20 Token", "contract_id": "legitimate_erc20", "expected_risk": "low", "actual_risk": "low", "risk_score": 0.3499999999999999, "vulnerabilities_found": 3, "analysis_time": 0.005365133285522461, "test_passed": true}, {"test_type": "static_analysis", "contract_name": "Honeypot Contract", "contract_id": "honeypot_contract", "expected_risk": "high", "actual_risk": "low", "risk_score": 0.35, "vulnerabilities_found": 4, "analysis_time": 0.0041010379791259766, "test_passed": false}, {"test_type": "static_analysis", "contract_name": "<PERSON><PERSON>tract", "contract_id": "rug_pull_contract", "expected_risk": "critical", "actual_risk": "low", "risk_score": 0.35000000000000003, "vulnerabilities_found": 10, "analysis_time": 0.0049779415130615234, "test_passed": false}, {"test_type": "risk_scoring", "contract_name": "Standard ERC20 Token", "contract_id": "legitimate_erc20", "expected_risk": "low", "actual_risk": "minimal", "risk_score": 0.125, "confidence": 0.6, "alerts_generated": 0, "scoring_time": 0.0005459785461425781, "test_passed": true}, {"test_type": "risk_scoring", "contract_name": "Honeypot Contract", "contract_id": "honeypot_contract", "expected_risk": "high", "actual_risk": "medium", "risk_score": 0.6479166666666668, "confidence": 0.6, "alerts_generated": 1, "scoring_time": 0.0006258487701416016, "test_passed": true}, {"test_type": "risk_scoring", "contract_name": "<PERSON><PERSON>tract", "contract_id": "rug_pull_contract", "expected_risk": "critical", "actual_risk": "high", "risk_score": 0.7555555555555556, "confidence": 0.6, "alerts_generated": 2, "scoring_time": 0.0006620883941650391, "test_passed": true}], "performance_metrics": {"total_validation_time": 0, "average_analysis_time": 0, "average_scoring_time": 0, "max_analysis_time": 0, "meets_performance_target": true}, "accuracy_assessment": {"total_tests": 0, "passed_tests": 0, "accuracy": 0, "meets_accuracy_target": false}, "production_readiness": {"criteria_met": {"accuracy_threshold": false, "performance_threshold": true, "no_critical_errors": true}, "overall_ready": false, "go_no_go_decision": "NO_GO"}}