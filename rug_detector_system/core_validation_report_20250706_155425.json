{"test_results": [{"test_type": "static_analysis", "contract_name": "Standard ERC20 Token", "contract_id": "legitimate_erc20", "expected_risk": "low", "actual_risk": "low", "risk_score": 0.3499999999999999, "vulnerabilities_found": 3, "analysis_time": 0.0010519027709960938, "test_passed": true}, {"test_type": "static_analysis", "contract_name": "Honeypot Contract", "contract_id": "honeypot_contract", "expected_risk": "high", "actual_risk": "low", "risk_score": 0.35, "vulnerabilities_found": 4, "analysis_time": 0.0002796649932861328, "test_passed": false}, {"test_type": "static_analysis", "contract_name": "<PERSON><PERSON>tract", "contract_id": "rug_pull_contract", "expected_risk": "critical", "actual_risk": "low", "risk_score": 0.35000000000000003, "vulnerabilities_found": 10, "analysis_time": 0.00022292137145996094, "test_passed": false}, {"test_type": "risk_scoring", "contract_name": "Standard ERC20 Token", "contract_id": "legitimate_erc20", "expected_risk": "low", "actual_risk": "minimal", "risk_score": 0.125, "confidence": 0.6, "alerts_generated": 0, "scoring_time": 0.00019788742065429688, "test_passed": true}, {"test_type": "risk_scoring", "contract_name": "Honeypot Contract", "contract_id": "honeypot_contract", "expected_risk": "high", "actual_risk": "medium", "risk_score": 0.6479166666666668, "confidence": 0.6, "alerts_generated": 0, "scoring_time": 0.0002503395080566406, "test_passed": true}, {"test_type": "risk_scoring", "contract_name": "<PERSON><PERSON>tract", "contract_id": "rug_pull_contract", "expected_risk": "critical", "actual_risk": "high", "risk_score": 0.7555555555555556, "confidence": 0.6, "alerts_generated": 0, "scoring_time": 0.00040721893310546875, "test_passed": true}], "performance_metrics": {"total_validation_time": 0, "average_analysis_time": 0.0005181630452473959, "average_scoring_time": 0.00028514862060546875, "max_analysis_time": 0.0010519027709960938, "meets_performance_target": true}, "accuracy_assessment": {"total_tests": 6, "passed_tests": 4, "accuracy": 0.6666666666666666, "meets_accuracy_target": false}, "production_readiness": {"criteria_met": {"accuracy_threshold": false, "performance_threshold": true, "no_critical_errors": true}, "overall_ready": false, "go_no_go_decision": "NO_GO"}}