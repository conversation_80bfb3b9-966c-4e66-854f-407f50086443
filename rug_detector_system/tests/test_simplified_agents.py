#!/usr/bin/env python3
"""
Unit Tests for Simplified LangGraph Agents

Comprehensive unit tests for the simplified multi-agent system components.

Author: MLDevOps Architect
Version: 4.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import pytest
import time
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from agents.simplified_langgraph_integration import (
    SimplifiedPatternAgent,
    SimplifiedMarketAgent,
    SimplifiedSocialAgent,
    SimplifiedCoordinatorAgent,
    SimpleAgentState,
    analyze_contract_with_simplified_agents
)
from langchain_ollama import ChatOllama


class TestSimpleAgentState:
    """Test SimpleAgentState dataclass."""
    
    def test_agent_state_initialization(self):
        """Test agent state initialization."""
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code="pragma solidity ^0.8.0;",
            agent_analyses={},
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=False,
            timestamp=time.time()
        )
        
        assert state.contract_address == "0x1234567890123456789012345678901234567890"
        assert state.contract_code == "pragma solidity ^0.8.0;"
        assert state.agent_analyses == {}
        assert not state.analysis_complete
    
    def test_agent_state_post_init(self):
        """Test agent state post-initialization."""
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code=None,
            agent_analyses=None,
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=None,
            timestamp=time.time()
        )
        
        # Post-init should set defaults
        assert isinstance(state.agent_analyses, dict)
        assert state.analysis_complete is False


class TestSimplifiedPatternAgent:
    """Test SimplifiedPatternAgent."""
    
    @pytest.fixture
    async def pattern_agent(self):
        """Create pattern agent for testing."""
        mock_llm = Mock(spec=ChatOllama)
        agent = SimplifiedPatternAgent(mock_llm)
        
        # Mock static analyzer
        agent.static_analyzer = AsyncMock()
        return agent
    
    @pytest.mark.asyncio
    async def test_pattern_agent_initialization(self, pattern_agent):
        """Test pattern agent initialization."""
        await pattern_agent.initialize()
        assert pattern_agent.static_analyzer is not None
    
    @pytest.mark.asyncio
    async def test_pattern_analysis_success(self, pattern_agent):
        """Test successful pattern analysis."""
        # Mock LLM response
        mock_response = Mock()
        mock_response.content = "This contract shows HIGH risk patterns including centralized ownership."
        pattern_agent.llm.ainvoke = AsyncMock(return_value=mock_response)
        
        # Create test state
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code="pragma solidity ^0.8.0;",
            agent_analyses={},
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=False,
            timestamp=time.time()
        )
        
        # Run analysis
        result_state = await pattern_agent.analyze(state)
        
        # Verify results
        assert 'pattern_analyst' in result_state.agent_analyses
        analysis = result_state.agent_analyses['pattern_analyst']
        assert analysis['recommended_risk_level'] == 'high'
        assert analysis['confidence'] == 0.8
        assert 'HIGH risk patterns' in analysis['llm_analysis']
    
    @pytest.mark.asyncio
    async def test_pattern_analysis_timeout(self, pattern_agent):
        """Test pattern analysis with LLM timeout."""
        # Mock LLM timeout
        pattern_agent.llm.ainvoke = AsyncMock(side_effect=asyncio.TimeoutError())
        
        # Create test state
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code=None,
            agent_analyses={},
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=False,
            timestamp=time.time()
        )
        
        # Run analysis
        result_state = await pattern_agent.analyze(state)
        
        # Verify fallback behavior
        assert 'pattern_analyst' in result_state.agent_analyses
        analysis = result_state.agent_analyses['pattern_analyst']
        assert 'LLM analysis timed out' in analysis['llm_analysis']
        assert analysis['confidence'] < 0.6  # Lower confidence due to timeout
    
    @pytest.mark.asyncio
    async def test_pattern_analysis_error(self, pattern_agent):
        """Test pattern analysis with LLM error."""
        # Mock LLM error
        pattern_agent.llm.ainvoke = AsyncMock(side_effect=Exception("LLM connection failed"))
        
        # Create test state
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code=None,
            agent_analyses={},
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=False,
            timestamp=time.time()
        )
        
        # Run analysis
        result_state = await pattern_agent.analyze(state)
        
        # Verify error handling
        assert 'pattern_analyst' in result_state.agent_analyses
        analysis = result_state.agent_analyses['pattern_analyst']
        assert 'LLM analysis failed' in analysis['llm_analysis']
        assert analysis['recommended_risk_level'] == 'medium'  # Conservative fallback


class TestSimplifiedMarketAgent:
    """Test SimplifiedMarketAgent."""
    
    @pytest.fixture
    def market_agent(self):
        """Create market agent for testing."""
        mock_llm = Mock(spec=ChatOllama)
        return SimplifiedMarketAgent(mock_llm)
    
    @pytest.mark.asyncio
    async def test_market_analysis_success(self, market_agent):
        """Test successful market analysis."""
        # Mock LLM response
        mock_response = Mock()
        mock_response.content = "Current market conditions show MEDIUM risk with neutral sentiment."
        market_agent.llm.ainvoke = AsyncMock(return_value=mock_response)
        
        # Create test state
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code=None,
            agent_analyses={},
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=False,
            timestamp=time.time()
        )
        
        # Run analysis
        result_state = await market_agent.analyze(state)
        
        # Verify results
        assert 'market_data_analyst' in result_state.agent_analyses
        analysis = result_state.agent_analyses['market_data_analyst']
        assert analysis['market_risk_level'] == 'medium'
        assert analysis['confidence'] == 0.7


class TestSimplifiedSocialAgent:
    """Test SimplifiedSocialAgent."""
    
    @pytest.fixture
    def social_agent(self):
        """Create social agent for testing."""
        mock_llm = Mock(spec=ChatOllama)
        return SimplifiedSocialAgent(mock_llm)
    
    @pytest.mark.asyncio
    async def test_social_analysis_success(self, social_agent):
        """Test successful social analysis."""
        # Mock LLM response
        mock_response = Mock()
        mock_response.content = "Social sentiment analysis shows LOW risk with positive community engagement."
        social_agent.llm.ainvoke = AsyncMock(return_value=mock_response)
        
        # Create test state
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code=None,
            agent_analyses={},
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=False,
            timestamp=time.time()
        )
        
        # Run analysis
        result_state = await social_agent.analyze(state)
        
        # Verify results
        assert 'social_sentiment_analyst' in result_state.agent_analyses
        analysis = result_state.agent_analyses['social_sentiment_analyst']
        assert analysis['social_risk_level'] == 'low'
        assert analysis['confidence'] > 0


class TestSimplifiedCoordinatorAgent:
    """Test SimplifiedCoordinatorAgent."""
    
    @pytest.fixture
    def coordinator_agent(self):
        """Create coordinator agent for testing."""
        mock_llm = Mock(spec=ChatOllama)
        return SimplifiedCoordinatorAgent(mock_llm)
    
    @pytest.mark.asyncio
    async def test_risk_coordination_success(self, coordinator_agent):
        """Test successful risk coordination."""
        # Create test state with agent analyses
        state = SimpleAgentState(
            contract_address="0x1234567890123456789012345678901234567890",
            contract_code=None,
            agent_analyses={
                'pattern_analyst': {
                    'recommended_risk_level': 'high',
                    'confidence': 0.8
                },
                'market_data_analyst': {
                    'market_risk_level': 'medium',
                    'confidence': 0.7
                },
                'social_sentiment_analyst': {
                    'social_risk_level': 'low',
                    'confidence': 0.6
                }
            },
            final_risk_score=None,
            final_recommendation=None,
            confidence_level=None,
            analysis_complete=False,
            timestamp=time.time()
        )
        
        # Run coordination
        result_state = await coordinator_agent.coordinate(state)
        
        # Verify results
        assert result_state.final_risk_score is not None
        assert result_state.final_recommendation is not None
        assert result_state.confidence_level is not None
        assert result_state.analysis_complete is True
        
        # Risk score should be weighted average (pattern analysis has highest weight)
        assert result_state.final_risk_score > 0.5  # Should be medium-high due to high pattern risk
    
    def test_calculate_final_risk_score(self, coordinator_agent):
        """Test final risk score calculation."""
        pattern_analysis = {'recommended_risk_level': 'high', 'confidence': 0.8}
        market_analysis = {'market_risk_level': 'low', 'confidence': 0.7}
        social_analysis = {'social_risk_level': 'medium', 'confidence': 0.6}
        
        risk_score = coordinator_agent._calculate_final_risk_score(
            pattern_analysis, market_analysis, social_analysis
        )
        
        # Should be weighted toward pattern analysis (60% weight)
        # high (0.7) * 0.6 + low (0.3) * 0.25 + medium (0.5) * 0.15 = 0.57
        assert 0.5 < risk_score < 0.7
    
    def test_determine_final_recommendation(self, coordinator_agent):
        """Test final recommendation determination."""
        # Test different risk score ranges
        assert coordinator_agent._determine_final_recommendation(0.9) == 'avoid'
        assert coordinator_agent._determine_final_recommendation(0.7) == 'high_caution'
        assert coordinator_agent._determine_final_recommendation(0.5) == 'caution'
        assert coordinator_agent._determine_final_recommendation(0.3) == 'caution'
        assert coordinator_agent._determine_final_recommendation(0.1) == 'safe'


class TestIntegrationAnalysis:
    """Test end-to-end analysis integration."""
    
    @pytest.mark.asyncio
    async def test_analyze_contract_fast_mode(self):
        """Test contract analysis in fast mode."""
        with patch('agents.simplified_langgraph_integration.create_simplified_multi_agent_system') as mock_create:
            # Mock the agent system
            mock_system = AsyncMock()
            mock_system.ainvoke = AsyncMock(return_value=SimpleAgentState(
                contract_address="0x1234567890123456789012345678901234567890",
                contract_code=None,
                agent_analyses={
                    'pattern_analyst': {'recommended_risk_level': 'medium', 'confidence': 0.7},
                    'market_data_analyst': {'market_risk_level': 'low', 'confidence': 0.6},
                    'social_sentiment_analyst': {'social_risk_level': 'low', 'confidence': 0.5}
                },
                final_risk_score=0.4,
                final_recommendation='caution',
                confidence_level=0.6,
                analysis_complete=True,
                timestamp=time.time()
            ))
            mock_create.return_value = mock_system
            
            # Run analysis
            result = await analyze_contract_with_simplified_agents(
                "0x1234567890123456789012345678901234567890",
                fast_mode=True
            )
            
            # Verify results
            assert result['contract_address'] == "0x1234567890123456789012345678901234567890"
            assert result['final_risk_score'] == 0.4
            assert result['final_recommendation'] == 'caution'
            assert result['confidence_level'] == 0.6
            assert result['analysis_complete'] is True
            
            # Verify fast mode was used
            mock_create.assert_called_once_with(fast_mode=True)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
