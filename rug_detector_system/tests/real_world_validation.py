#!/usr/bin/env python3
"""
Real-World Validation Suite - Comprehensive Blockchain Testing

This module provides comprehensive real-world testing using actual blockchain data,
documented rug-pull incidents, and legitimate contracts to validate system effectiveness.

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import asyncio
import json
import time
import csv
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from analysis import analyze_contract_static, calculate_risk_score, generate_alerts
from blockchain import get_web3_manager, ChainType
from config import get_config
from logging_config import get_logger


@dataclass
class TestContract:
    """Test contract data structure."""
    address: str
    name: str
    chain: ChainType
    category: str  # 'rug_pull', 'legitimate', 'honeypot', 'suspicious'
    known_outcome: str  # 'confirmed_rug', 'safe', 'honeypot', 'unknown'
    deployment_date: Optional[str] = None
    source_url: Optional[str] = None
    notes: Optional[str] = None


@dataclass
class TestResult:
    """Test result data structure."""
    contract_address: str
    contract_name: str
    category: str
    known_outcome: str
    predicted_risk_level: str
    risk_score: float
    confidence: float
    analysis_time: float
    alerts_generated: int
    vulnerabilities_found: int
    test_passed: bool
    error_message: Optional[str] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()


class RealWorldValidator:
    """Comprehensive real-world validation framework."""
    
    def __init__(self):
        """Initialize real-world validator."""
        self.logger = get_logger(__name__)
        self.config = get_config()
        
        # Test datasets
        self.known_rug_pulls = self._load_known_rug_pulls()
        self.legitimate_contracts = self._load_legitimate_contracts()
        self.honeypot_contracts = self._load_honeypot_contracts()
        self.recent_contracts = []  # Will be populated dynamically
        
        # Test results
        self.test_results: List[TestResult] = []
        self.performance_metrics = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'avg_analysis_time': 0.0,
            'true_positives': 0,
            'false_positives': 0,
            'true_negatives': 0,
            'false_negatives': 0
        }
        
        # Quality thresholds
        self.quality_thresholds = {
            'min_accuracy': 0.90,
            'max_false_positive_rate': 0.05,
            'max_analysis_time': 30.0,
            'min_confidence': 0.70
        }
    
    def _load_known_rug_pulls(self) -> List[TestContract]:
        """Load documented rug-pull contracts for testing."""
        return [
            # Documented rug-pull incidents with known outcomes
            TestContract(
                address="******************************************",
                name="AnubisDAO",
                chain=ChainType.ETHEREUM,
                category="rug_pull",
                known_outcome="confirmed_rug",
                deployment_date="2021-10-28",
                source_url="https://etherscan.io/address/******************************************",
                notes="$60M rug pull, liquidity drained within hours"
            ),
            TestContract(
                address="******************************************",
                name="Wrapped Bitcoin (WBTC)",
                chain=ChainType.ETHEREUM,
                category="legitimate",
                known_outcome="safe",
                deployment_date="2019-01-24",
                source_url="https://etherscan.io/address/******************************************",
                notes="Established wrapped Bitcoin token"
            ),
            TestContract(
                address="******************************************",
                name="SQUID Token",
                chain=ChainType.BSC,
                category="rug_pull",
                known_outcome="confirmed_rug",
                deployment_date="2021-10-20",
                notes="Squid Game token rug pull, $3.3M stolen"
            ),
            TestContract(
                address="******************************************",
                name="Uniswap Token (UNI)",
                chain=ChainType.ETHEREUM,
                category="legitimate",
                known_outcome="safe",
                deployment_date="2020-09-16",
                source_url="https://etherscan.io/address/******************************************",
                notes="Uniswap governance token"
            ),
            TestContract(
                address="******************************************",
                name="Dai Stablecoin (DAI)",
                chain=ChainType.ETHEREUM,
                category="legitimate",
                known_outcome="safe",
                deployment_date="2019-11-18",
                source_url="https://etherscan.io/address/******************************************",
                notes="MakerDAO stablecoin"
            ),
            # Add more documented cases...
        ]
    
    def _load_legitimate_contracts(self) -> List[TestContract]:
        """Load established legitimate contracts for false-positive testing."""
        return [
            TestContract(
                address="******************************************",
                name="Cronos Token (CRO)",
                chain=ChainType.ETHEREUM,
                category="legitimate",
                known_outcome="safe",
                notes="Crypto.com exchange token"
            ),
            TestContract(
                address="******************************************",
                name="Polygon Token (MATIC)",
                chain=ChainType.ETHEREUM,
                category="legitimate",
                known_outcome="safe",
                notes="Polygon network token"
            ),
            TestContract(
                address="******************************************",
                name="Chainlink Token (LINK)",
                chain=ChainType.ETHEREUM,
                category="legitimate",
                known_outcome="safe",
                notes="Chainlink oracle token"
            ),
            # Add more established tokens...
        ]
    
    def _load_honeypot_contracts(self) -> List[TestContract]:
        """Load known honeypot contracts for testing."""
        return [
            TestContract(
                address="******************************************",
                name="HoneyPot Example",
                chain=ChainType.ETHEREUM,
                category="honeypot",
                known_outcome="honeypot",
                notes="Known honeypot contract - cannot sell tokens"
            ),
            # Add more honeypot examples...
        ]
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run comprehensive real-world validation suite."""
        self.logger.info("Starting comprehensive real-world validation")
        
        validation_report = {
            'test_execution_report': {},
            'performance_benchmark_report': {},
            'accuracy_analysis_report': {},
            'issue_registry': [],
            'production_readiness_assessment': {}
        }
        
        try:
            # 1. Test Execution Report
            self.logger.info("Phase 1: Executing real-world test cases")
            validation_report['test_execution_report'] = await self._execute_test_cases()
            
            # 2. Performance Benchmark Report
            self.logger.info("Phase 2: Performance benchmarking")
            validation_report['performance_benchmark_report'] = await self._benchmark_performance()
            
            # 3. Accuracy Analysis Report
            self.logger.info("Phase 3: Accuracy analysis")
            validation_report['accuracy_analysis_report'] = self._analyze_accuracy()
            
            # 4. Issue Registry
            self.logger.info("Phase 4: Issue identification")
            validation_report['issue_registry'] = self._compile_issues()
            
            # 5. Production Readiness Assessment
            self.logger.info("Phase 5: Production readiness assessment")
            validation_report['production_readiness_assessment'] = self._assess_production_readiness()
            
            # Save comprehensive report
            await self._save_validation_report(validation_report)
            
            return validation_report
            
        except Exception as e:
            self.logger.error(f"Validation failed: {e}")
            raise
    
    async def _execute_test_cases(self) -> Dict[str, Any]:
        """Execute all test cases with real blockchain data."""
        test_execution_report = {
            'summary': {},
            'detailed_results': [],
            'evidence': {}
        }
        
        # Combine all test contracts
        all_test_contracts = (
            self.known_rug_pulls + 
            self.legitimate_contracts + 
            self.honeypot_contracts
        )
        
        self.logger.info(f"Testing {len(all_test_contracts)} contracts")
        
        for contract in all_test_contracts:
            try:
                self.logger.info(f"Testing contract: {contract.address} ({contract.name})")
                
                start_time = time.time()
                
                # Perform static analysis
                static_result = await analyze_contract_static(contract.address)
                
                # Calculate risk score
                analysis_data = {
                    'static_analysis': static_result.to_dict(),
                    'liquidity_analysis': {'liquidity_locked': False},  # Placeholder
                    'ownership_analysis': {'ownership_renounced': False}  # Placeholder
                }
                
                risk_score = await calculate_risk_score(contract.address, analysis_data)
                
                # Generate alerts
                alerts = await generate_alerts(risk_score)
                
                analysis_time = time.time() - start_time
                
                # Evaluate test result
                test_passed = self._evaluate_test_result(contract, risk_score, alerts)
                
                # Create test result
                result = TestResult(
                    contract_address=contract.address,
                    contract_name=contract.name,
                    category=contract.category,
                    known_outcome=contract.known_outcome,
                    predicted_risk_level=risk_score.risk_level.value,
                    risk_score=risk_score.overall_score,
                    confidence=risk_score.confidence,
                    analysis_time=analysis_time,
                    alerts_generated=len(alerts),
                    vulnerabilities_found=len(static_result.vulnerabilities),
                    test_passed=test_passed
                )
                
                self.test_results.append(result)
                test_execution_report['detailed_results'].append(asdict(result))
                
                # Update performance metrics
                self._update_performance_metrics(contract, result)
                
                self.logger.info(
                    f"Test completed: {contract.address} - "
                    f"Risk: {risk_score.overall_score:.2f} - "
                    f"Passed: {test_passed}"
                )
                
            except Exception as e:
                self.logger.error(f"Test failed for {contract.address}: {e}")
                
                error_result = TestResult(
                    contract_address=contract.address,
                    contract_name=contract.name,
                    category=contract.category,
                    known_outcome=contract.known_outcome,
                    predicted_risk_level="error",
                    risk_score=0.0,
                    confidence=0.0,
                    analysis_time=0.0,
                    alerts_generated=0,
                    vulnerabilities_found=0,
                    test_passed=False,
                    error_message=str(e)
                )
                
                self.test_results.append(error_result)
                test_execution_report['detailed_results'].append(asdict(error_result))
        
        # Calculate summary statistics
        test_execution_report['summary'] = {
            'total_contracts_tested': len(all_test_contracts),
            'successful_tests': len([r for r in self.test_results if r.error_message is None]),
            'failed_tests': len([r for r in self.test_results if r.error_message is not None]),
            'passed_tests': len([r for r in self.test_results if r.test_passed]),
            'average_analysis_time': sum(r.analysis_time for r in self.test_results) / len(self.test_results) if self.test_results else 0,
            'total_alerts_generated': sum(r.alerts_generated for r in self.test_results),
            'total_vulnerabilities_found': sum(r.vulnerabilities_found for r in self.test_results)
        }
        
        return test_execution_report
    
    def _evaluate_test_result(self, contract: TestContract, risk_score, alerts: List) -> bool:
        """Evaluate if test result matches expected outcome."""
        if contract.known_outcome == "confirmed_rug":
            # For known rug pulls, expect high risk score
            return risk_score.overall_score >= 0.7 and risk_score.risk_level.value in ['high', 'critical']
        
        elif contract.known_outcome == "safe":
            # For legitimate contracts, expect low risk score
            return risk_score.overall_score <= 0.3 and risk_score.risk_level.value in ['low', 'minimal']
        
        elif contract.known_outcome == "honeypot":
            # For honeypots, expect specific vulnerability detection
            honeypot_alerts = [a for a in alerts if 'honeypot' in a.alert_type.value.lower()]
            return len(honeypot_alerts) > 0
        
        else:
            # For unknown outcomes, consider any result as valid for now
            return True
    
    def _update_performance_metrics(self, contract: TestContract, result: TestResult):
        """Update performance metrics based on test result."""
        self.performance_metrics['total_tests'] += 1
        
        if result.test_passed:
            self.performance_metrics['passed_tests'] += 1
        else:
            self.performance_metrics['failed_tests'] += 1
        
        # Update confusion matrix
        if contract.known_outcome == "confirmed_rug":
            if result.predicted_risk_level in ['high', 'critical']:
                self.performance_metrics['true_positives'] += 1
            else:
                self.performance_metrics['false_negatives'] += 1
        
        elif contract.known_outcome == "safe":
            if result.predicted_risk_level in ['low', 'minimal']:
                self.performance_metrics['true_negatives'] += 1
            else:
                self.performance_metrics['false_positives'] += 1
    
    async def _benchmark_performance(self) -> Dict[str, Any]:
        """Benchmark system performance under realistic conditions."""
        performance_report = {
            'response_times': {},
            'throughput_metrics': {},
            'resource_utilization': {},
            'scalability_analysis': {}
        }
        
        # Response time analysis
        analysis_times = [r.analysis_time for r in self.test_results if r.error_message is None]
        
        if analysis_times:
            performance_report['response_times'] = {
                'average_seconds': sum(analysis_times) / len(analysis_times),
                'median_seconds': sorted(analysis_times)[len(analysis_times) // 2],
                'min_seconds': min(analysis_times),
                'max_seconds': max(analysis_times),
                'percentile_95_seconds': sorted(analysis_times)[int(len(analysis_times) * 0.95)],
                'meets_sla_target': all(t <= self.quality_thresholds['max_analysis_time'] for t in analysis_times)
            }
        
        # Throughput metrics
        total_time = sum(analysis_times) if analysis_times else 0
        performance_report['throughput_metrics'] = {
            'contracts_per_minute': (len(analysis_times) / (total_time / 60)) if total_time > 0 else 0,
            'total_contracts_processed': len(analysis_times),
            'total_processing_time_minutes': total_time / 60
        }
        
        return performance_report
    
    def _analyze_accuracy(self) -> Dict[str, Any]:
        """Analyze detection accuracy with statistical metrics."""
        accuracy_report = {
            'confusion_matrix': {},
            'classification_metrics': {},
            'roc_analysis': {},
            'statistical_significance': {}
        }
        
        # Confusion matrix
        tp = self.performance_metrics['true_positives']
        fp = self.performance_metrics['false_positives']
        tn = self.performance_metrics['true_negatives']
        fn = self.performance_metrics['false_negatives']
        
        accuracy_report['confusion_matrix'] = {
            'true_positives': tp,
            'false_positives': fp,
            'true_negatives': tn,
            'false_negatives': fn
        }
        
        # Classification metrics
        total = tp + fp + tn + fn
        if total > 0:
            accuracy = (tp + tn) / total
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            false_positive_rate = fp / (fp + tn) if (fp + tn) > 0 else 0
            
            accuracy_report['classification_metrics'] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'specificity': specificity,
                'f1_score': f1_score,
                'false_positive_rate': false_positive_rate,
                'meets_accuracy_threshold': accuracy >= self.quality_thresholds['min_accuracy'],
                'meets_fpr_threshold': false_positive_rate <= self.quality_thresholds['max_false_positive_rate']
            }
        
        return accuracy_report
    
    def _compile_issues(self) -> List[Dict[str, Any]]:
        """Compile list of issues found during testing."""
        issues = []
        
        # Check for failed tests
        failed_tests = [r for r in self.test_results if not r.test_passed]
        for result in failed_tests:
            issues.append({
                'severity': 'High',
                'category': 'Detection Accuracy',
                'description': f"Incorrect prediction for {result.contract_address}",
                'expected': result.known_outcome,
                'actual': result.predicted_risk_level,
                'reproduction_steps': f"Analyze contract {result.contract_address}",
                'status': 'Open'
            })
        
        # Check for performance issues
        slow_tests = [r for r in self.test_results if r.analysis_time > self.quality_thresholds['max_analysis_time']]
        if slow_tests:
            issues.append({
                'severity': 'Medium',
                'category': 'Performance',
                'description': f"{len(slow_tests)} contracts exceeded analysis time threshold",
                'details': f"Max time: {max(r.analysis_time for r in slow_tests):.2f}s",
                'reproduction_steps': "Run performance benchmark",
                'status': 'Open'
            })
        
        # Check for errors
        error_tests = [r for r in self.test_results if r.error_message is not None]
        for result in error_tests:
            issues.append({
                'severity': 'Critical',
                'category': 'System Error',
                'description': f"Analysis failed for {result.contract_address}",
                'error': result.error_message,
                'reproduction_steps': f"Analyze contract {result.contract_address}",
                'status': 'Open'
            })
        
        return issues
    
    def _assess_production_readiness(self) -> Dict[str, Any]:
        """Assess overall production readiness."""
        assessment = {
            'overall_status': 'NOT_READY',
            'criteria_met': {},
            'recommendations': [],
            'limitations': [],
            'go_no_go_decision': 'NO_GO'
        }
        
        # Check accuracy criteria
        accuracy_metrics = self._analyze_accuracy().get('classification_metrics', {})
        accuracy_met = accuracy_metrics.get('meets_accuracy_threshold', False)
        fpr_met = accuracy_metrics.get('meets_fpr_threshold', False)
        
        # Check performance criteria
        performance_metrics = self._benchmark_performance().get('response_times', {})
        performance_met = performance_metrics.get('meets_sla_target', False)
        
        # Check error rate
        error_rate = len([r for r in self.test_results if r.error_message is not None]) / len(self.test_results) if self.test_results else 1
        error_rate_met = error_rate < 0.05  # Less than 5% error rate
        
        assessment['criteria_met'] = {
            'accuracy_threshold': accuracy_met,
            'false_positive_rate': fpr_met,
            'performance_sla': performance_met,
            'error_rate': error_rate_met
        }
        
        # Determine overall readiness
        all_criteria_met = all(assessment['criteria_met'].values())
        
        if all_criteria_met:
            assessment['overall_status'] = 'READY'
            assessment['go_no_go_decision'] = 'GO'
        else:
            assessment['recommendations'] = [
                "Improve detection accuracy for rug-pull identification",
                "Optimize analysis performance to meet SLA targets",
                "Fix critical system errors causing analysis failures",
                "Expand test dataset with more diverse contract examples"
            ]
            
            assessment['limitations'] = [
                "Limited test dataset size",
                "Dependency on external blockchain APIs",
                "Static analysis may miss runtime behaviors",
                "Risk scoring requires manual tuning"
            ]
        
        return assessment
    
    async def _save_validation_report(self, report: Dict[str, Any]):
        """Save comprehensive validation report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save JSON report
        report_path = Path(f"validation_report_{timestamp}.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Save CSV results
        csv_path = Path(f"test_results_{timestamp}.csv")
        with open(csv_path, 'w', newline='') as f:
            if self.test_results:
                writer = csv.DictWriter(f, fieldnames=asdict(self.test_results[0]).keys())
                writer.writeheader()
                for result in self.test_results:
                    writer.writerow(asdict(result))
        
        self.logger.info(f"Validation report saved: {report_path}")
        self.logger.info(f"Test results saved: {csv_path}")


async def main():
    """Main validation entry point."""
    validator = RealWorldValidator()
    
    try:
        print("🔍 Starting Comprehensive Real-World Validation")
        print("=" * 70)
        
        report = await validator.run_comprehensive_validation()
        
        print("\n📊 VALIDATION SUMMARY")
        print("=" * 70)
        
        # Print key metrics
        accuracy_metrics = report.get('accuracy_analysis_report', {}).get('classification_metrics', {})
        performance_metrics = report.get('performance_benchmark_report', {}).get('response_times', {})
        readiness = report.get('production_readiness_assessment', {})
        
        print(f"Accuracy: {accuracy_metrics.get('accuracy', 0):.1%}")
        print(f"False Positive Rate: {accuracy_metrics.get('false_positive_rate', 0):.1%}")
        print(f"Average Analysis Time: {performance_metrics.get('average_seconds', 0):.2f}s")
        print(f"Production Ready: {readiness.get('go_no_go_decision', 'NO_GO')}")
        
        if readiness.get('go_no_go_decision') == 'GO':
            print("\n🎉 SYSTEM IS PRODUCTION READY!")
        else:
            print("\n❌ SYSTEM NOT READY FOR PRODUCTION")
            print("\nRecommendations:")
            for rec in readiness.get('recommendations', []):
                print(f"  • {rec}")
        
    except Exception as e:
        print(f"\n❌ VALIDATION FAILED: {e}")
        return False
    
    return True


if __name__ == "__main__":
    asyncio.run(main())
