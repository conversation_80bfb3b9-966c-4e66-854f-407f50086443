#!/usr/bin/env python3
"""
Enhanced Test Dataset - Real-World Rug-Pull and Legitimate Contracts

This module provides a comprehensive test dataset based on documented
rug-pull incidents and established legitimate contracts for validation.

Author: MLDevOps Architect
Version: 3.0.0
Quality Standard: 99.9th percentile
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from enum import Enum


class ContractCategory(Enum):
    """Contract categories for testing."""
    LEGITIMATE = "legitimate"
    RUG_PULL = "rug_pull"
    HONEYPOT = "honeypot"
    SUSPICIOUS = "suspicious"


class ChainType(Enum):
    """Blockchain types."""
    ETHEREUM = "ethereum"
    BSC = "bsc"
    POLYGON = "polygon"


@dataclass
class TestContract:
    """Enhanced test contract with comprehensive metadata."""
    address: str
    name: str
    symbol: str
    chain: ChainType
    category: ContractCategory
    expected_risk_level: str
    deployment_date: Optional[str] = None
    source_verified: bool = False
    incident_date: Optional[str] = None
    loss_amount_usd: Optional[float] = None
    description: Optional[str] = None
    source_url: Optional[str] = None
    attack_vectors: List[str] = None
    
    def __post_init__(self):
        if self.attack_vectors is None:
            self.attack_vectors = []


class EnhancedTestDataset:
    """Enhanced test dataset with documented contracts."""
    
    def __init__(self):
        """Initialize enhanced test dataset."""
        self.documented_rug_pulls = self._load_documented_rug_pulls()
        self.legitimate_contracts = self._load_legitimate_contracts()
        self.honeypot_contracts = self._load_honeypot_contracts()
        self.suspicious_contracts = self._load_suspicious_contracts()
    
    def _load_documented_rug_pulls(self) -> List[TestContract]:
        """Load documented rug-pull contracts from research and incident reports."""
        return [
            # AnubisDAO - $60M rug pull
            TestContract(
                address="******************************************",
                name="AnubisDAO",
                symbol="ANKH",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="critical",
                deployment_date="2021-10-28",
                incident_date="2021-10-29",
                loss_amount_usd=60000000,
                source_verified=True,
                description="Developer drained $60M from liquidity pool within hours of launch",
                source_url="https://etherscan.io/address/******************************************",
                attack_vectors=["liquidity_drain", "ownership_centralization"]
            ),
            
            # SQUID Token - Squid Game themed rug pull
            TestContract(
                address="******************************************",
                name="Squid Game Token",
                symbol="SQUID",
                chain=ChainType.BSC,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="critical",
                deployment_date="2021-10-20",
                incident_date="2021-11-01",
                loss_amount_usd=3300000,
                source_verified=True,
                description="Anti-sell mechanism prevented users from selling, developers dumped tokens",
                source_url="https://bscscan.com/address/******************************************",
                attack_vectors=["honeypot", "trading_restrictions", "ownership_centralization"]
            ),
            
            # Uranium Finance - Flash loan attack
            TestContract(
                address="0xc2d00de94795e60fb76bc37d899170996cbda436",
                name="Uranium Finance",
                symbol="U92",
                chain=ChainType.BSC,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="high",
                deployment_date="2021-04-28",
                incident_date="2021-04-28",
                loss_amount_usd=50000000,
                source_verified=True,
                description="Migrator function allowed draining of liquidity pools",
                attack_vectors=["liquidity_drain", "access_control"]
            ),
            
            # Meerkat Finance - $31M rug pull
            TestContract(
                address="0x5d0158a5c3ddf47d4ea4517d8db0d76aa2e87563",
                name="Meerkat Finance",
                symbol="MKAT",
                chain=ChainType.BSC,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="critical",
                deployment_date="2021-03-04",
                incident_date="2021-03-04",
                loss_amount_usd=31000000,
                source_verified=False,
                description="Vault contracts drained shortly after launch",
                attack_vectors=["liquidity_drain", "ownership_centralization"]
            ),
            
            # Thodex Token - Exchange rug pull
            TestContract(
                address="******************************************",
                name="Thodex Token",
                symbol="TDX",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="high",
                deployment_date="2021-04-15",
                incident_date="2021-04-21",
                loss_amount_usd=2000000000,
                source_verified=True,
                description="Exchange token with hidden backdoors for fund extraction",
                attack_vectors=["ownership_centralization", "access_control"]
            ),
            
            # Fintoch - Ponzi scheme token
            TestContract(
                address="******************************************",
                name="Fintoch",
                symbol="FTC",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="critical",
                deployment_date="2023-05-15",
                incident_date="2023-07-20",
                loss_amount_usd=31600000,
                source_verified=True,
                description="Ponzi scheme with locked withdrawal functions",
                attack_vectors=["honeypot", "trading_restrictions"]
            ),
            
            # SaveTheKids - Celebrity endorsed rug pull
            TestContract(
                address="******************************************",
                name="SaveTheKids",
                symbol="KIDS",
                chain=ChainType.BSC,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="high",
                deployment_date="2021-06-05",
                incident_date="2021-06-06",
                loss_amount_usd=4000000,
                source_verified=True,
                description="Developers sold tokens immediately after celebrity promotion",
                attack_vectors=["liquidity_drain", "ownership_centralization"]
            ),
            
            # Evolved Apes - NFT + Token rug pull
            TestContract(
                address="******************************************",
                name="Evolved Apes",
                symbol="EVO",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="high",
                deployment_date="2021-09-25",
                incident_date="2021-10-01",
                loss_amount_usd=2700000,
                source_verified=True,
                description="Developer disappeared with funds and website",
                attack_vectors=["ownership_centralization", "liquidity_drain"]
            ),
            
            # Polywhale - Polygon rug pull
            TestContract(
                address="******************************************",
                name="Polywhale",
                symbol="KRILL",
                chain=ChainType.POLYGON,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="critical",
                deployment_date="2021-06-15",
                incident_date="2021-06-17",
                loss_amount_usd=1200000,
                source_verified=True,
                description="Yield farming protocol with hidden admin functions",
                attack_vectors=["access_control", "liquidity_drain"]
            ),
            
            # Iron Finance - Algorithmic stablecoin collapse
            TestContract(
                address="0x7b65B489fE53fCE1F6548Db886C08aD73111DDd8",
                name="Iron Finance",
                symbol="TITAN",
                chain=ChainType.POLYGON,
                category=ContractCategory.RUG_PULL,
                expected_risk_level="high",
                deployment_date="2021-05-20",
                incident_date="2021-06-16",
                loss_amount_usd=**********,
                source_verified=True,
                description="Algorithmic stablecoin death spiral and bank run",
                attack_vectors=["market_manipulation", "liquidity_drain"]
            )
        ]
    
    def _load_legitimate_contracts(self) -> List[TestContract]:
        """Load established legitimate contracts for false-positive testing."""
        return [
            # USDC - USD Coin
            TestContract(
                address="******************************************",
                name="USD Coin",
                symbol="USDC",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="minimal",
                deployment_date="2018-09-26",
                source_verified=True,
                description="Centre consortium stablecoin with regulatory compliance",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Wrapped Bitcoin (WBTC)
            TestContract(
                address="******************************************",
                name="Wrapped Bitcoin",
                symbol="WBTC",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="minimal",
                deployment_date="2019-01-24",
                source_verified=True,
                description="Decentralized wrapped Bitcoin with multi-sig custody",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Uniswap Token (UNI)
            TestContract(
                address="******************************************",
                name="Uniswap",
                symbol="UNI",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="low",
                deployment_date="2020-09-16",
                source_verified=True,
                description="Uniswap protocol governance token",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Chainlink Token (LINK)
            TestContract(
                address="******************************************",
                name="Chainlink",
                symbol="LINK",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="low",
                deployment_date="2017-09-20",
                source_verified=True,
                description="Chainlink oracle network token",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Aave Token (AAVE)
            TestContract(
                address="******************************************",
                name="Aave",
                symbol="AAVE",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="low",
                deployment_date="2020-10-02",
                source_verified=True,
                description="Aave protocol governance token",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Compound Token (COMP)
            TestContract(
                address="******************************************",
                name="Compound",
                symbol="COMP",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="low",
                deployment_date="2020-06-15",
                source_verified=True,
                description="Compound protocol governance token",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Maker Token (MKR)
            TestContract(
                address="******************************************",
                name="Maker",
                symbol="MKR",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="low",
                deployment_date="2017-12-27",
                source_verified=True,
                description="MakerDAO governance token",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Dai Stablecoin (DAI)
            TestContract(
                address="******************************************",
                name="Dai Stablecoin",
                symbol="DAI",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="minimal",
                deployment_date="2019-11-18",
                source_verified=True,
                description="MakerDAO decentralized stablecoin",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Polygon Token (MATIC)
            TestContract(
                address="******************************************",
                name="Polygon",
                symbol="MATIC",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="low",
                deployment_date="2019-04-28",
                source_verified=True,
                description="Polygon network token",
                source_url="https://etherscan.io/address/******************************************"
            ),
            
            # Synthetix Network Token (SNX)
            TestContract(
                address="******************************************",
                name="Synthetix Network Token",
                symbol="SNX",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.LEGITIMATE,
                expected_risk_level="low",
                deployment_date="2018-03-11",
                source_verified=True,
                description="Synthetix protocol token",
                source_url="https://etherscan.io/address/******************************************"
            )
        ]
    
    def _load_honeypot_contracts(self) -> List[TestContract]:
        """Load known honeypot contracts for specific testing."""
        return [
            TestContract(
                address="******************************************",
                name="HoneyPot Example 1",
                symbol="HONEY1",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.HONEYPOT,
                expected_risk_level="critical",
                description="Cannot sell tokens - only owner can transfer",
                attack_vectors=["honeypot", "trading_restrictions"]
            ),
            
            TestContract(
                address="******************************************",
                name="HoneyPot Example 2",
                symbol="HONEY2",
                chain=ChainType.BSC,
                category=ContractCategory.HONEYPOT,
                expected_risk_level="critical",
                description="High fees prevent profitable selling",
                attack_vectors=["honeypot", "fee_manipulation"]
            )
        ]
    
    def _load_suspicious_contracts(self) -> List[TestContract]:
        """Load suspicious contracts with potential risks."""
        return [
            TestContract(
                address="******************************************",
                name="Suspicious Token 1",
                symbol="SUS1",
                chain=ChainType.ETHEREUM,
                category=ContractCategory.SUSPICIOUS,
                expected_risk_level="medium",
                description="Centralized ownership with pause functionality",
                attack_vectors=["ownership_centralization", "trading_restrictions"]
            ),
            
            TestContract(
                address="******************************************",
                name="Suspicious Token 2",
                symbol="SUS2",
                chain=ChainType.BSC,
                category=ContractCategory.SUSPICIOUS,
                expected_risk_level="medium",
                description="Unlocked liquidity with high concentration",
                attack_vectors=["liquidity_risk", "ownership_centralization"]
            )
        ]
    
    def get_all_contracts(self) -> List[TestContract]:
        """Get all test contracts."""
        return (
            self.documented_rug_pulls +
            self.legitimate_contracts +
            self.honeypot_contracts +
            self.suspicious_contracts
        )
    
    def get_contracts_by_category(self, category: ContractCategory) -> List[TestContract]:
        """Get contracts by category."""
        all_contracts = self.get_all_contracts()
        return [c for c in all_contracts if c.category == category]
    
    def get_contracts_by_risk_level(self, risk_level: str) -> List[TestContract]:
        """Get contracts by expected risk level."""
        all_contracts = self.get_all_contracts()
        return [c for c in all_contracts if c.expected_risk_level == risk_level]
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of test dataset."""
        all_contracts = self.get_all_contracts()
        
        return {
            'total_contracts': len(all_contracts),
            'by_category': {
                category.value: len(self.get_contracts_by_category(category))
                for category in ContractCategory
            },
            'by_risk_level': {
                level: len(self.get_contracts_by_risk_level(level))
                for level in ['minimal', 'low', 'medium', 'high', 'critical']
            },
            'by_chain': {
                chain.value: len([c for c in all_contracts if c.chain == chain])
                for chain in ChainType
            },
            'documented_incidents': len([c for c in all_contracts if c.loss_amount_usd is not None]),
            'total_documented_losses_usd': sum(
                c.loss_amount_usd for c in all_contracts 
                if c.loss_amount_usd is not None
            )
        }


# Factory function
def get_enhanced_test_dataset() -> EnhancedTestDataset:
    """Get enhanced test dataset instance."""
    return EnhancedTestDataset()
