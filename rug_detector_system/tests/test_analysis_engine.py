"""
Analysis Engine Tests - Comprehensive Testing Suite

This module provides comprehensive tests for the analysis engine including
static analysis, risk scoring, and pattern detection capabilities.

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from analysis import (
    StaticAnalyzer, AnalysisResult, VulnerabilityType,
    RiskScorer, RiskLevel, RiskScore, AlertType,
    analyze_contract_static, calculate_risk_score, generate_alerts
)


class TestStaticAnalyzer:
    """Test suite for StaticAnalyzer."""
    
    @pytest.fixture
    async def analyzer(self):
        """Create StaticAnalyzer instance for testing."""
        analyzer = StaticAnalyzer()
        await analyzer.initialize()
        return analyzer
    
    @pytest.fixture
    def sample_contract_code(self):
        """Sample contract code for testing."""
        return """
        pragma solidity ^0.8.0;
        
        contract TestContract {
            address public owner;
            bool public tradingEnabled = false;
            mapping(address => bool) public blacklisted;
            
            modifier onlyOwner() {
                require(msg.sender == owner, "Not owner");
                _;
            }
            
            function setTradingEnabled(bool _enabled) public onlyOwner {
                tradingEnabled = _enabled;
            }
            
            function blacklistAddress(address _addr) public onlyOwner {
                blacklisted[_addr] = true;
            }
            
            function transfer(address to, uint256 amount) public {
                require(tradingEnabled, "Trading disabled");
                require(!blacklisted[msg.sender], "Address blacklisted");
                // Transfer logic
            }
        }
        """
    
    @pytest.fixture
    def honeypot_contract_code(self):
        """Honeypot contract code for testing."""
        return """
        pragma solidity ^0.8.0;
        
        contract HoneypotContract {
            address private owner;
            mapping(address => uint256) public balances;
            
            constructor() {
                owner = msg.sender;
            }
            
            function transfer(address to, uint256 amount) public {
                require(balances[msg.sender] >= amount, "Insufficient balance");
                if (msg.sender != owner) {
                    revert("Transfer not allowed");
                }
                balances[msg.sender] -= amount;
                balances[to] += amount;
            }
            
            function emergencyWithdraw() public {
                require(msg.sender == owner, "Not owner");
                payable(owner).transfer(address(this).balance);
            }
        }
        """
    
    async def test_analyzer_initialization(self, analyzer):
        """Test analyzer initialization."""
        assert analyzer is not None
        assert len(analyzer.rug_pull_patterns) > 0
        assert analyzer.stats['contracts_analyzed'] == 0
    
    async def test_contract_analysis_basic(self, analyzer, sample_contract_code):
        """Test basic contract analysis."""
        result = await analyzer.analyze_contract("0x123", sample_contract_code)
        
        assert isinstance(result, AnalysisResult)
        assert result.contract_address == "0x123"
        assert result.contract_name == "TestContract"
        assert result.analysis_timestamp > 0
        assert result.analysis_duration > 0
        assert isinstance(result.vulnerabilities, list)
    
    async def test_honeypot_detection(self, analyzer, honeypot_contract_code):
        """Test honeypot pattern detection."""
        result = await analyzer.analyze_contract("0x456", honeypot_contract_code)
        
        # Should detect honeypot patterns
        honeypot_vulns = [v for v in result.vulnerabilities 
                         if v.type == VulnerabilityType.HONEYPOT]
        assert len(honeypot_vulns) > 0
        
        # Should have reasonable risk score
        assert result.overall_risk_score > 0.3
    
    async def test_pattern_analysis(self, analyzer, sample_contract_code):
        """Test pattern analysis functionality."""
        vulnerabilities = analyzer._analyze_patterns(sample_contract_code)
        
        assert isinstance(vulnerabilities, list)
        
        # Should detect trading restrictions
        trading_vulns = [v for v in vulnerabilities 
                        if v.type == VulnerabilityType.TRADING_RESTRICTIONS]
        assert len(trading_vulns) > 0
    
    async def test_complexity_metrics(self, analyzer, sample_contract_code):
        """Test complexity metrics calculation."""
        metrics = analyzer._calculate_complexity_metrics(sample_contract_code)
        
        assert isinstance(metrics, dict)
        assert 'total_lines' in metrics
        assert 'function_count' in metrics
        assert 'complexity_score' in metrics
        assert metrics['total_lines'] > 0
        assert metrics['function_count'] > 0
    
    async def test_risk_score_calculation(self, analyzer):
        """Test risk score calculation."""
        vulnerabilities = [
            Mock(severity=VulnerabilityType.HONEYPOT, confidence=0.8),
            Mock(severity=VulnerabilityType.TRADING_RESTRICTIONS, confidence=0.6)
        ]
        
        score = analyzer._calculate_risk_score(vulnerabilities)
        assert 0.0 <= score <= 1.0
    
    async def test_analyzer_statistics(self, analyzer, sample_contract_code):
        """Test analyzer statistics tracking."""
        initial_stats = analyzer.get_stats()
        
        await analyzer.analyze_contract("0x789", sample_contract_code)
        
        updated_stats = analyzer.get_stats()
        assert updated_stats['contracts_analyzed'] > initial_stats['contracts_analyzed']
    
    @patch('analysis.static_analyzer.subprocess.run')
    async def test_slither_integration(self, mock_subprocess, analyzer, sample_contract_code):
        """Test Slither integration."""
        # Mock Slither output
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = '{"results": {"detectors": []}}'
        
        slither_output = await analyzer._run_slither_analysis(sample_contract_code)
        assert slither_output is not None
        assert 'results' in slither_output
    
    async def test_error_handling(self, analyzer):
        """Test error handling in analysis."""
        # Test with invalid contract code
        result = await analyzer.analyze_contract("0xERROR", None)
        
        assert isinstance(result, AnalysisResult)
        assert result.contract_address == "0xERROR"
        assert result.overall_risk_score == 0.0


class TestRiskScorer:
    """Test suite for RiskScorer."""
    
    @pytest.fixture
    async def scorer(self):
        """Create RiskScorer instance for testing."""
        scorer = RiskScorer()
        await scorer.initialize()
        return scorer
    
    @pytest.fixture
    def sample_analysis_data(self):
        """Sample analysis data for testing."""
        return {
            'static_analysis': {
                'vulnerabilities': [
                    {
                        'type': 'honeypot',
                        'severity': 'high',
                        'confidence': 0.8,
                        'title': 'Potential honeypot detected'
                    },
                    {
                        'type': 'trading_restrictions',
                        'severity': 'medium',
                        'confidence': 0.6,
                        'title': 'Trading restrictions found'
                    }
                ]
            },
            'liquidity_analysis': {
                'liquidity_locked': False,
                'lock_duration_days': 0
            },
            'ownership_analysis': {
                'ownership_renounced': False,
                'multi_sig_wallet': False
            },
            'market_analysis': {
                'market_cap': 50000,
                'volume_24h': 10000
            }
        }
    
    async def test_scorer_initialization(self, scorer):
        """Test scorer initialization."""
        assert scorer is not None
        assert len(scorer.risk_thresholds) == 5
        assert len(scorer.factor_weights) > 0
        assert len(scorer.alert_rules) > 0
    
    async def test_risk_score_calculation(self, scorer, sample_analysis_data):
        """Test risk score calculation."""
        risk_score = await scorer.calculate_risk_score("0x123", sample_analysis_data)
        
        assert isinstance(risk_score, RiskScore)
        assert risk_score.contract_address == "0x123"
        assert 0.0 <= risk_score.overall_score <= 1.0
        assert risk_score.risk_level in RiskLevel
        assert 0.0 <= risk_score.confidence <= 1.0
        assert len(risk_score.factors) > 0
    
    async def test_static_analysis_factor(self, scorer, sample_analysis_data):
        """Test static analysis factor calculation."""
        factor = scorer._calculate_static_analysis_factor(
            sample_analysis_data['static_analysis']
        )
        
        assert factor.name == "static_analysis"
        assert 0.0 <= factor.score <= 1.0
        assert factor.weight > 0
        assert len(factor.evidence) > 0
    
    async def test_liquidity_factor(self, scorer, sample_analysis_data):
        """Test liquidity factor calculation."""
        factor = scorer._calculate_liquidity_factor(
            sample_analysis_data['liquidity_analysis']
        )
        
        assert factor.name == "liquidity_analysis"
        assert 0.0 <= factor.score <= 1.0
        assert factor.weight > 0
    
    async def test_ownership_factor(self, scorer, sample_analysis_data):
        """Test ownership factor calculation."""
        factor = scorer._calculate_ownership_factor(
            sample_analysis_data['ownership_analysis']
        )
        
        assert factor.name == "ownership_analysis"
        assert 0.0 <= factor.score <= 1.0
        assert factor.weight > 0
    
    async def test_alert_generation(self, scorer, sample_analysis_data):
        """Test alert generation."""
        risk_score = await scorer.calculate_risk_score("0x123", sample_analysis_data)
        alerts = await scorer.generate_alerts(risk_score)
        
        assert isinstance(alerts, list)
        
        for alert in alerts:
            assert alert.alert_type in AlertType
            assert alert.severity in RiskLevel
            assert alert.contract_address == "0x123"
            assert 0.0 <= alert.risk_score <= 1.0
            assert len(alert.recommended_actions) > 0
    
    async def test_high_risk_alert_generation(self, scorer):
        """Test high-risk alert generation."""
        high_risk_data = {
            'static_analysis': {
                'vulnerabilities': [
                    {
                        'type': 'honeypot',
                        'severity': 'critical',
                        'confidence': 0.9,
                        'title': 'Critical honeypot detected'
                    }
                ]
            },
            'liquidity_analysis': {
                'liquidity_locked': False,
                'lock_duration_days': 0
            },
            'ownership_analysis': {
                'ownership_renounced': False,
                'multi_sig_wallet': False
            }
        }
        
        risk_score = await scorer.calculate_risk_score("0xHIGH", high_risk_data)
        alerts = await scorer.generate_alerts(risk_score)
        
        # Should generate alerts for high-risk contract
        assert len(alerts) > 0
        
        # Should have high severity alerts
        high_severity_alerts = [a for a in alerts 
                               if a.severity in [RiskLevel.HIGH, RiskLevel.CRITICAL]]
        assert len(high_severity_alerts) > 0
    
    async def test_risk_level_determination(self, scorer):
        """Test risk level determination."""
        assert scorer._determine_risk_level(0.95) == RiskLevel.CRITICAL
        assert scorer._determine_risk_level(0.75) == RiskLevel.HIGH
        assert scorer._determine_risk_level(0.55) == RiskLevel.MEDIUM
        assert scorer._determine_risk_level(0.35) == RiskLevel.LOW
        assert scorer._determine_risk_level(0.15) == RiskLevel.MINIMAL
    
    async def test_scorer_statistics(self, scorer, sample_analysis_data):
        """Test scorer statistics tracking."""
        initial_stats = scorer.get_stats()
        
        await scorer.calculate_risk_score("0x789", sample_analysis_data)
        
        updated_stats = scorer.get_stats()
        assert updated_stats['scores_calculated'] > initial_stats['scores_calculated']


class TestAnalysisPipeline:
    """Test suite for complete analysis pipeline."""
    
    async def test_complete_pipeline(self):
        """Test complete analysis pipeline."""
        contract_code = """
        pragma solidity ^0.8.0;
        
        contract SuspiciousContract {
            address private owner;
            bool public tradingEnabled = false;
            
            constructor() {
                owner = msg.sender;
            }
            
            modifier onlyOwner() {
                require(msg.sender == owner, "Not owner");
                _;
            }
            
            function setTradingEnabled(bool _enabled) public onlyOwner {
                tradingEnabled = _enabled;
            }
            
            function transfer(address to, uint256 amount) public {
                require(tradingEnabled, "Trading disabled");
                // Transfer logic
            }
            
            function emergencyWithdraw() public onlyOwner {
                payable(owner).transfer(address(this).balance);
            }
        }
        """
        
        # Step 1: Static analysis
        static_result = await analyze_contract_static("0xPIPELINE", contract_code)
        assert isinstance(static_result, AnalysisResult)
        
        # Step 2: Risk scoring
        analysis_data = {
            'static_analysis': static_result.to_dict(),
            'liquidity_analysis': {
                'liquidity_locked': False,
                'lock_duration_days': 0
            },
            'ownership_analysis': {
                'ownership_renounced': False,
                'multi_sig_wallet': False
            }
        }
        
        risk_score = await calculate_risk_score("0xPIPELINE", analysis_data)
        assert isinstance(risk_score, RiskScore)
        
        # Step 3: Alert generation
        alerts = await generate_alerts(risk_score)
        assert isinstance(alerts, list)
        
        # Verify pipeline produces meaningful results
        assert (len(static_result.vulnerabilities) > 0 or 
                risk_score.overall_score > 0 or 
                len(alerts) > 0)
    
    async def test_pipeline_performance(self):
        """Test pipeline performance."""
        contract_code = "pragma solidity ^0.8.0; contract Test {}"
        
        start_time = time.time()
        
        # Run pipeline
        static_result = await analyze_contract_static("0xPERF", contract_code)
        analysis_data = {'static_analysis': static_result.to_dict()}
        risk_score = await calculate_risk_score("0xPERF", analysis_data)
        alerts = await generate_alerts(risk_score)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within reasonable time
        assert duration < 10.0  # 10 seconds max
    
    async def test_pipeline_error_handling(self):
        """Test pipeline error handling."""
        # Test with invalid data
        try:
            risk_score = await calculate_risk_score("0xERROR", {})
            assert isinstance(risk_score, RiskScore)
            assert risk_score.overall_score == 0.0
        except Exception as e:
            pytest.fail(f"Pipeline should handle errors gracefully: {e}")


# Performance benchmarks
class TestPerformance:
    """Performance tests for analysis engine."""
    
    @pytest.mark.performance
    async def test_static_analysis_performance(self):
        """Test static analysis performance."""
        analyzer = StaticAnalyzer()
        await analyzer.initialize()
        
        contract_code = "pragma solidity ^0.8.0; contract Test { function test() public {} }"
        
        start_time = time.time()
        result = await analyzer.analyze_contract("0xPERF", contract_code)
        end_time = time.time()
        
        duration = end_time - start_time
        assert duration < 5.0  # Should complete within 5 seconds
        assert isinstance(result, AnalysisResult)
    
    @pytest.mark.performance
    async def test_risk_scoring_performance(self):
        """Test risk scoring performance."""
        scorer = RiskScorer()
        await scorer.initialize()
        
        analysis_data = {
            'static_analysis': {'vulnerabilities': []},
            'liquidity_analysis': {'liquidity_locked': True},
            'ownership_analysis': {'ownership_renounced': True}
        }
        
        start_time = time.time()
        risk_score = await scorer.calculate_risk_score("0xPERF", analysis_data)
        end_time = time.time()
        
        duration = end_time - start_time
        assert duration < 1.0  # Should complete within 1 second
        assert isinstance(risk_score, RiskScore)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
