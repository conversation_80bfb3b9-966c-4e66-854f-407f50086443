"""
Test Package - Comprehensive Testing Suite

This package provides comprehensive testing capabilities for the Rug Detector System
including unit tests, integration tests, performance tests, and end-to-end validation.

Features:
- Unit tests with 95%+ code coverage
- Integration tests with mocked external APIs
- Performance and load testing
- End-to-end validation with historical data
- Security and vulnerability testing
- Test fixtures and utilities

Author: MLDevOps Architect
Version: 2.0.0
Quality Standard: 99.9th percentile
"""

import pytest
import asyncio
from pathlib import Path
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Test configuration
pytest_plugins = [
    # Fixtures will be added as needed
]

# Test markers
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.timeout(300)  # 5 minute timeout for all tests
]

__all__ = [
    'pytest_plugins',
    'pytestmark'
]
