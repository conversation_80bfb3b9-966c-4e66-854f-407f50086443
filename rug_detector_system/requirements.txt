# Rug Detector System - Production Dependencies
# Pinned versions for reproducible builds and security
# M1 Mac ARM64 compatible versions verified
# Last updated: 2025-07-06

# ============================================================================
# CORE BLOCKCHAIN LIBRARIES
# ============================================================================

# Web3 and Ethereum interaction (verified working versions)
web3==7.12.0                    # Latest stable, Python 3.8-3.13 support
eth-account==0.13.7             # Ethereum account management
eth-hash==0.7.1                 # Ethereum hashing utilities
eth-typing==5.2.1               # Ethereum type definitions
eth-utils==5.3.0                # Ethereum utility functions
eth-abi==5.2.0                  # Ethereum ABI utilities
hexbytes==1.3.1                 # Hex byte string utilities

# ============================================================================
# DATABASE AND PERSISTENCE
# ============================================================================

# PostgreSQL database
SQLAlchemy==2.0.41              # ORM and database toolkit (M1 optimized)
psycopg2-binary==2.9.10         # PostgreSQL adapter (binary for M1 Mac)
alembic==1.14.0                 # Database migration tool

# Redis caching
redis==6.2.0                    # Redis client (latest stable)

# ============================================================================
# HTTP CLIENTS AND NETWORKING
# ============================================================================

# HTTP libraries (verified working versions)
requests==2.32.4                # HTTP library
httpx==0.28.1                   # Async HTTP client
urllib3==2.5.0                  # HTTP client library

# WebSocket support
websockets==15.0.1              # WebSocket client/server (M1 optimized)

# Async HTTP
aiohttp==3.12.13                # Async HTTP client/server (M1 optimized)

# ============================================================================
# DATA VALIDATION AND SERIALIZATION
# ============================================================================

# Data validation (already installed)
pydantic==2.9.2                 # Data validation using Python type hints
pydantic-core==2.23.4           # Core validation logic for Pydantic

# ============================================================================
# CONFIGURATION AND ENVIRONMENT
# ============================================================================

# Environment and configuration
python-dotenv==1.1.1            # Load environment variables from .env
pyyaml==6.0.2                   # YAML parser and emitter

# ============================================================================
# LOGGING AND MONITORING
# ============================================================================

# Structured logging
structlog==25.4.0               # Structured logging (latest)

# Monitoring and metrics
prometheus-client==0.22.1       # Prometheus metrics client (latest)

# ============================================================================
# ASYNC AND CONCURRENCY
# ============================================================================

# Retry and resilience
tenacity==9.1.2                 # Retry library (already installed)

# ============================================================================
# CRYPTOGRAPHY AND SECURITY
# ============================================================================

# Cryptographic libraries (already installed via eth-account)
cryptography==45.0.5            # Cryptographic recipes and primitives
pycryptodome==3.23.0            # Cryptographic library

# ============================================================================
# UTILITIES AND HELPERS
# ============================================================================

# CLI and user interface (already installed)
click==8.1.8                    # Command line interface creation
rich==14.0.0                    # Rich text and beautiful formatting
typer==0.16.0                   # Modern CLI framework

# ============================================================================
# DEVELOPMENT AND TESTING (Optional - install with pip install -e ".[dev]")
# ============================================================================

# Note: Development dependencies are defined in pyproject.toml [project.optional-dependencies]
# Install with: pip install -e ".[dev]"

# ============================================================================
# VERSION CONSTRAINTS
# ============================================================================

# Python version requirement: >=3.11,<4.0
# All packages verified for M1 Mac ARM64 compatibility
# Security-scanned versions as of 2025-07-06
# Zero vulnerabilities reported by safety scan
