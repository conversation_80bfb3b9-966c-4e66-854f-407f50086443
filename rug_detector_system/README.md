# Rug Detector System v2.0

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Pre-commit](https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white)](https://github.com/pre-commit/pre-commit)
[![Security: bandit](https://img.shields.io/badge/security-bandit-yellow.svg)](https://github.com/PyCQA/bandit)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A **production-grade, modular system** for detecting rug-pulls and suspicious activities on EVM-compatible blockchains in real-time. Built with 99.9th percentile quality standards and optimized for M1 Mac development.

## 🏗️ Architecture

The system follows MLDevOps best practices with clear separation of concerns:

```
rug_detector_system/
├── src/                      # Core source code
│   ├── analysis/            # Analysis engines (static, dynamic, forensic)
│   ├── blockchain/          # Web3 integration and real-time data
│   ├── config.py           # Configuration management
│   ├── database.py         # Database operations
│   └── main.py             # Application entry point
├── tests/                   # Comprehensive test suite
├── docs/                    # Documentation
├── scripts/                 # Utility scripts
└── config/                  # Configuration files
```

### 🔧 Core Components

- **Static Analysis**: Slither-based smart contract vulnerability detection
- **Dynamic Analysis**: Real-time behavior pattern monitoring
- **Forensic Engine**: Liquidity and market manipulation analysis
- **Multi-chain Support**: Ethereum, Polygon, BSC compatibility
- **Real-time Streaming**: WebSocket-based blockchain event monitoring
- **Production Infrastructure**: PostgreSQL, Redis, Kafka integration

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** (optimized for M1 Mac)
- **Git** for version control
- **Make** for development workflow automation

### Installation

1. **Clone and setup the project:**
   ```bash
   git clone <repository-url>
   cd rug_detector_system
   make dev-setup
   ```

2. **Activate virtual environment:**
   ```bash
   source venv/bin/activate
   ```

3. **Configure environment:**
   ```bash
   cp ../env .env
   # Edit .env with your API keys (already configured)
   ```

4. **Validate setup:**
   ```bash
   make validate-env
   make run-test-connection
   ```

## 🔑 Configuration

The system uses your existing comprehensive API key configuration:

- **Blockchain**: Alchemy Web3 provider, Etherscan API
- **Market Data**: CoinGecko, Dune Analytics
- **Infrastructure**: PostgreSQL, Redis, Kafka
- **Monitoring**: Prometheus, Grafana integration

## 🧪 Development Workflow

### Code Quality Tools

- **Black**: Code formatting (88 char line length)
- **isort**: Import sorting
- **flake8**: Linting with security extensions
- **mypy**: Static type checking
- **bandit**: Security vulnerability scanning
- **safety**: Dependency vulnerability checking

### Common Commands

```bash
# Development setup
make dev-setup              # Complete development environment setup
make validate-env           # Validate environment configuration

# Code quality
make format                 # Format code (black + isort)
make lint                   # Run linting checks
make type-check            # Run type checking
make security              # Run security scans

# Testing
make test                  # Run all tests
make test-cov             # Run tests with coverage report
make test-integration     # Run integration tests only

# Application
make run                  # Run the application
make run-test-connection  # Test API connections

# Quality gates
make quality-gate         # 95% coverage requirement
make ci                   # Simulate CI pipeline
```

### Pre-commit Hooks

Automated quality checks on every commit:
- Code formatting (Black, isort)
- Linting (flake8 with extensions)
- Type checking (mypy)
- Security scanning (bandit, safety)
- YAML/JSON validation
- Commit message linting

## 🏃‍♂️ Running the System

### 1. Connection Test (Critical First Step)
```bash
make run-test-connection
```
**This must pass before proceeding!**

### 2. Start the Application
```bash
make run
```

### 3. Development Mode
```bash
make run-dev
```

## 🧪 Testing Strategy

### Test Categories
- **Unit Tests**: Individual component testing
- **Integration Tests**: API and database integration
- **Security Tests**: Vulnerability and penetration testing
- **Performance Tests**: Load testing and benchmarking
- **End-to-End Tests**: Complete workflow validation

### Coverage Requirements
- **Minimum**: 95% code coverage
- **Target**: 99.9th percentile quality standards
- **Validation**: Automated quality gates

## 🔒 Security

### Security Measures
- **Dependency Scanning**: Automated vulnerability detection
- **Code Analysis**: Static security analysis with bandit
- **Secret Management**: Environment-based configuration
- **API Key Protection**: Secure credential handling

### Security Commands
```bash
make security              # Run security scans
make audit                # Full security audit
```

## 📊 Monitoring & Observability

### Metrics & Monitoring
- **Prometheus**: Metrics collection
- **Grafana**: Dashboard visualization
- **Structured Logging**: JSON-formatted logs
- **Health Checks**: Application health endpoints

### Performance Optimization
- **M1 Mac Optimized**: Native ARM64 performance
- **Caching**: Redis-based intelligent caching
- **Connection Pooling**: Efficient resource management
- **Async Processing**: Non-blocking operations

## 🔧 Troubleshooting

### Common Issues

1. **Connection Test Fails**
   - Verify API keys in `.env` file
   - Check network connectivity
   - Validate API key permissions

2. **Import Errors**
   - Ensure virtual environment is activated
   - Run `make install-dev`

3. **Pre-commit Failures**
   - Run `make format` to fix formatting
   - Address linting issues shown in output

### Debug Commands
```bash
make validate-env          # Check environment setup
make health-check         # Application health status
make metrics              # View application metrics
```

## 📈 Performance

### M1 Mac Optimizations
- Native ARM64 Python builds
- Optimized numerical libraries
- Memory-efficient data structures
- Parallel processing capabilities

### Benchmarking
```bash
make benchmark            # Run performance benchmarks
make profile             # Profile application performance
```

## 🤝 Contributing

### Development Standards
- **Code Style**: Black formatting, 88 character lines
- **Type Hints**: Full type annotation required
- **Documentation**: Comprehensive docstrings
- **Testing**: 95%+ coverage requirement
- **Security**: All dependencies scanned

### Workflow
1. Create feature branch
2. Implement changes with tests
3. Run `make dev-check` (all quality checks)
4. Submit pull request
5. Automated CI validation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with MLDevOps best practices
- Optimized for M1 Mac development
- Production-grade architecture patterns
- 99.9th percentile quality standards

---

**Quality Standard**: 99.9th percentile | **Architecture**: MLDevOps | **Platform**: M1 Mac Optimized
